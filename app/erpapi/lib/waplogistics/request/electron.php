<?php

/**
 * <AUTHOR> 2015-12-15
 * @describe 电子面单请求类
 */
class erpapi_waplogistics_request_electron extends erpapi_logistics_request_abstract
{
    protected $title;
    protected $timeOut = 10;
    protected $primaryBn = '';
    protected $cacheLimit = 5000;
    protected $directNum = 1;
    protected $everyNum = 100;

    //电子面单请求统一出口
    final protected function requestCall($method, $params, $callback = array(), $orign_params = array(), $gateway = '')
    {
        if (!$this->title) {
            $this->title = $this->__channelObj->channel['name'] . $this->__channelObj->channel['channel_type'] . '获取电子面单';
        }
        // 御城河-运单直连
        if (!$callback && $orign_params['order_bns']) {
            $hchsafe = array(
                'to_node_id' => $this->__configObj->get_to_node_id(),
                'tradeIds' => $orign_params['order_bns'],
            );

            kernel::single('base_hchsafe')->order_push_log($hchsafe);
        }
        $this->__caller->writeFailLog = false;

        return $this->__caller->call($method, $params, $callback, $this->title, $this->timeOut, $this->primaryBn, true, $gateway);
    }

    //回收电子面单 默认都做作废处理
    public function recycleWaybill($waybillNumber, $delivery_bn = '')
    {
        app::get('logisticsmanager')->model('waybill')->update(array('status' => 2, 'create_time' => time()), array('waybill_number' => $waybillNumber));
    }

    protected function bufferBackToRet($rlt)
    {//各自实现
        return array();
    }

    //缓存池异步回调方法
    public function bufferRequestCallBack($result, $callback_params)
    {
        $rlt = $this->callback($result, $callback_params);
        $arrWaybill = $this->bufferBackToRet($rlt);
        if (empty($arrWaybill)) {
            return array('rsp' => 'fail', 'msg' => '数据处理失败');
        }
        $objChannel = app::get('logisticsmanager')->model('channel');
        $channel = $objChannel->dump(array('channel_id' => $callback_params['channel_id'], 'status' => 'true'));
        if (empty($channel)) {
            return array('rsp' => 'fail', 'msg' => '电子面单来源缺失或停用');
        }
        $waybillModel = app::get('logisticsmanager')->model('waybill');
        $data = array();
        foreach ($arrWaybill as $val) {
            $val = trim($val);
            $row = $waybillModel->dump(array('channel_id' => $channel['channel_id'], 'waybill_number' => $val), 'id');
            if (!$row) {
                $data[] = array(
                    'waybill_number' => $val,
                    'channel_id' => $channel['channel_id'],
                    'logistics_code' => $channel['logistics_code'],
                    'status' => 0,
                    'create_time' => time(),
                );
            }
        }
        if (empty($data)) {
            return array('rsp' => 'fail', 'msg' => '没有可用的单号');
        }
        $insertSql = ome_func::get_insert_sql($waybillModel, $data);
        $ret = $waybillModel->db->exec($insertSql);
        if ($ret) {
            return array('rsp' => 'succ', 'msg' => '数据写入成功');
        } else {
            return array('rsp' => 'fail', 'msg' => '数据写入失败');
        }
    }

    //回传物流公司请求统一接口
    final protected function deliveryCall($method, $logData, $params, $gateway = '', $isAsync = true)
    {
        if (!$this->title) {
            $this->title = $this->__channelObj->channel['name'] . $this->__channelObj->channel['channel_type'] . '电子面单物流回传';
        }
        $this->__caller->writeFailLog = false;

        $logData['obj_bn'] = $this->primaryBn;
        if ($isAsync) {
            $callback = array(
                'class' => get_class($this),
                'method' => 'deliveryBack',
                'params' => $logData
            );
        }

        $ret = $this->__caller->call($method, $params, $callback, $this->title, $this->timeOut, $this->primaryBn, true, $gateway);
        if (empty($callback)) {
            return $this->deliveryBack($ret, $logData);
        }
        $this->logisticsLog($logData['logi_no'], $logData['delivery_id'], $params);

        return true;


    }

    public function deliveryBack($result, $callback_params)
    {

        if (in_array($callback_params['channel_type'], array('ems', '360buy'))) {
            $rlt = $this->callback($result, $callback_params);

            $logisticsLogObj = app::get('logisticsmanager')->model('logistics_log');
            if ($rlt['rsp'] == 'succ') {
                $logisticsLogObj->update(array('status' => 'success'), array('delivery_id' => $callback_params['delivery_id'], 'logi_no' => $callback_params['logi_no']));
            } else {
                $logisticsLogObj->update(array('status' => 'fail'), array('delivery_id' => $callback_params['delivery_id'], 'logi_no' => $callback_params['logi_no']));
            }
        }

        return true;
    }

    protected function logisticsLog($logiNo, $delivery_id, $params)
    {
        if (in_array($this->__channelObj->channel['channel_type'], array('ems', '360buy'))) {
            //回填日志只针对EMS 和京东记录便于重试
            $logisticsLogObj = &app::get('logisticsmanager')->model('logistics_log');
            $row = $logisticsLogObj->dump(array('logi_no' => $logiNo, 'delivery_id' => $delivery_id), 'log_id, retry');
            if ($row['log_id']) {
                return $logisticsLogObj->update(array('retry' => $row['retry'] + 1), array('log_id' => $row['log_id']));
            }
            $logSdf = array(
                'logi_no' => $logiNo,
                'delivery_id' => $delivery_id,
                'channel_id' => $this->__channelObj->channel['channel_id'],
                'channel_type' => $this->__channelObj->channel['channel_type'],
                'status' => 'running',
                'create_time' => time(),
                'params' => $params,
            );
            return $logisticsLogObj->insert($logSdf);
        } else {
            return true;
        }

    }

    //是否直辖市
    public function isMunicipality($province)
    {
        $municipality = array('北京市', '上海市', '天津市', '重庆市');
        $status = false;
        foreach ($municipality as $zxs) {
            if (substr($zxs, 0, strlen($province)) == $province) {
                $status = true;
                break;
            }
        }
        return $status;
    }

    #过滤特殊字符
    public function charFilter($str)
    {
        if (strpos($str, '@hash')) {
            return $str;
        }
        $str = str_replace(array('&#34;', '“', '&quot;', '&quot',), '”', $str);
        $str = str_replace(array("<", ">", "&", "'", '"', '', '+', '\\', '%'), '', $str);
        return $str;
    }

    /**
     * 处理直连返回结果
     *
     */
    protected function directDataProcess($data, $channel_id = null)
    {
        $objWaybill = app::get('logisticsmanager')->model('waybill');
        $waybillExtendModel = app::get('logisticsmanager')->model('waybill_extend');
        foreach ($data as $val) {
            if ($val['succ']) {
                $logi_no = trim($val['logi_no']);
                # 查询是否存在该发货单的电子面单信息
                $dly_filter = [
                    'delivery_id' => $val['delivery_id']
                ];
                $arrWaybill = $objWaybill->dump($dly_filter, 'id,status');
                if (!$arrWaybill) {
                    $arrWaybill = array(
                        'delivery_id' => $val['delivery_id'],
                        'waybill_number' => $logi_no,
                        'logistics_code' => $val['logistics_code'],
                        'status' => 1,
                        'monthly_account' => $val['monthly_account'],     // 月结号
                        'monthly_type' => $val['monthly_type'],           // 月结号类型
                        'product_type' => $val['product_type'],           // 服务类型
                        'pickup_time' => $val['pickup_time'],             // 上门取件时间
                        'create_time' => time(),
                    );
                    # 客户编码
                    if (!empty($val['customer_code'])) {
                        $arrWaybill['customer_code'] = $val['customer_code'];
                    }
                    if (!empty($channel_id)) {
                        $arrWaybill['channel_id'] = $channel_id;
                    }
                    # 发货单人信息
                    if (!empty($val['send_info'])) {
                        $arrWaybill['send_info'] = serialize($val['send_info']);
                    }
                    # 请求唯一单据号
                    if (!empty($val['unique_code'])) {
                        $arrWaybill['unique_code'] = $val['unique_code'];
                    }
                    $objWaybill->insert($arrWaybill);
                } elseif ($arrWaybill['status'] == '2') {
                    # 更新电子面单
                    $update_data = [
                        'status' => '1',
                        'waybill_number' => $logi_no,
                        'logistics_code' => $val['logistics_code'],
                        'monthly_account' => $val['monthly_account'],     // 月结号
                        'monthly_type' => $val['monthly_type'],           // 月结号类型
                        'product_type' => $val['product_type'],           // 服务类型
                        'pickup_time' => $val['pickup_time'],             // 上门取件时间
                    ];
                    # 客户编码
                    if (!empty($val['customer_code'])) {
                        $update_data['customer_code'] = $val['customer_code'];
                    }
                    if (!empty($channel_id)) {
                        $update_data['channel_id'] = $channel_id;
                    }
                    # 发货单人信息
                    if (!empty($val['send_info'])) {
                        $update_data['send_info'] = serialize($val['send_info']);
                    }
                    # 请求唯一单据号
                    if (!empty($val['unique_code'])) {
                        $update_data['unique_code'] = $val['unique_code'];
                    }
                    $objWaybill->update($update_data, array('id' => $arrWaybill['id']));
                }
                if (!$val['noWayBillExtend']) {
                    $waybillExtend = array(
                        'waybill_id' => $arrWaybill['id'],
                        'mailno_barcode' => $val['mailno_barcode'],
                        'qrcode' => $val['qrcode'],
                        'sort_code' => $val['sort_code'],
                        'position' => $val['position'] ?: '',
                        'position_no' => $val['position_no'] ?: '',
                        'package_wdjc' => $val['package_wdjc'],
                        'package_wd' => $val['package_wd'],
                        'print_config' => $val['print_config'],
                        'json_packet' => $val['json_packet'],
                    );

                    $filter = array('waybill_id' => $waybillExtend['waybill_id']);
                    if (!$waybillExtendModel->dump($filter)) {
                        $ret = $waybillExtendModel->insert($waybillExtend);
                    } else {
                        $ret = $waybillExtendModel->update($waybillExtend, $filter);
                    }

                }
            }
        }

    }

    public function bind()
    {
        $params = array(
            'app' => 'app.applyNodeBind',
            'node_id' => base_shopnode::node_id('ome'),
            'from_certi_id' => base_certificate::certi_id(),
            'callback' => kernel::openapi_url('openapi.ome.shop', 'shop_callback', array('channel_type' => $this->node_type)),
            'sess_callback' => urlencode(kernel::openapi_url('openapi.ome.shop', 'shop_callback', array('channel_type' => $this->node_type))),
            'api_url' => kernel::base_url(1) . kernel::url_prefix() . '/api',
            'node_type' => $this->node_type,
            'to_node' => $this->to_node,
            'shop_name' => $this->shop_name,
        );
        $token = base_certificate::token();
        $params['certi_ac'] = $this->genBindSign($params, $token);
        //$api_url = 'http://sws.ex-sandbox.com/api.php';
        $api_url = MATRIX_RELATION_URL . 'api.php';
        $headers = array(
            'Connection' => 5,
        );

        $core_http = kernel::single('base_httpclient');
        $response = $core_http->set_timeout(5)->post($api_url, $params, $headers);

        $response = json_decode($response, true);

        $status = false;
        if ($response['res'] == 'succ' || $response['msg']['errorDescription'] == '绑定关系已存在,不需要重复绑定') {
            $status = true;
        }
        return $status;
    }

    public function genBindSign($params, $token)
    {
        ksort($params);
        $str = '';
        foreach ($params as $key => $value) {
            if ($key != 'certi_ac') {
                $str .= $value;
            }
        }
        $signString = md5($str . $token);
        return $signString;
    }

    protected function _formate_receiver_city($receiver_city)
    {
        $zhixiashi = array('北京', '上海', '天津', '重庆');
        $zizhiqu = array('内蒙古', '宁夏回族', '新疆维吾尔', '西藏', '广西壮族');

        if (in_array($receiver_city, $zhixiashi)) {
            $receiver_city = $receiver_city . '市';
        } else if (in_array($receiver_city, $zizhiqu)) {
            $receiver_city = $receiver_city . '自治区';
        } else if ($receiver_city == '广西') {
            $receiver_city = $receiver_city . '壮族自治区';
        } else if ($receiver_city == '宁夏') {
            $receiver_city = $receiver_city . '回族自治区';

        } else if ($receiver_city == '新疆') {
            $receiver_city = $receiver_city . '维吾尔自治区';
        } elseif (!preg_match('/(.*?)省/', $receiver_city)) {
            $receiver_city = $receiver_city . '省';
        }
        return $receiver_city;
    }

    //获取关联信息
    public function getLogisticRelate($logiId)
    {
        $dlyCorpRes = app::get('ome')->model('dly_corp')->dump(array('corp_id' => $logiId), 'channel_id,prt_tmpl_id');
        $logChannelRes = app::get('logisticsmanager')->model('channel')->dump(array('channel_id' => $dlyCorpRes['channel_id']), 'shop_id');
        list($jdbusinesscode, $shop_id) = explode('|||', $logChannelRes['shop_id']);
        return array('prt_tmpl_id' => $dlyCorpRes['prt_tmpl_id'], 'jd_businesscode' => $jdbusinesscode);
    }

    //是否为京东打印控件
    public function isJdPrintControl($corp_id)
    {
        $corp = app::get('ome')->model('dly_corp')->db_dump($corp_id, 'prt_tmpl_id, channel_id');

        if ($corp['channel_id'] != $this->__channelObj->channel['channel_id']) {
            $prtTmpl = app::get('ome')->model('dly_corp_channel')->db_dump(
                array('channel_id' => $this->__channelObj->channel['channel_id'], 'corp_id' => $corp_id), 'prt_tmpl_id');

            if ($prtTmpl) {
                $corp['prt_tmpl_id'] = $prtTmpl['prt_tmpl_id'];
            }
        }

        $tmpRes = app::get('logisticsmanager')->model('express_template')->dump(array('template_id' => $corp['prt_tmpl_id']), 'control_type');

        return $tmpRes['control_type'] == 'jd' ? true : false;
    }

    //获取京东打印数据
    public function getPrintData($deliveryIdList, $delivery, $jdBusinesscode, $jpwj = 'jp')
    {
        $this->title = '获取京东打印数据';
        $orderNo = $delivery['order_bns'][0];
        $mapCode = [];
        $params = [];
        if ($jpwj == 'jp') {
            $mapCode['ewCustomerCode'] = $jdBusinesscode;

            $params['cp_code'] = 'JD';
        }

        if ($jpwj == 'wj') {
            $jdalpha = explode('|||', $this->__channelObj->channel['shop_id']);
            $mapCode['eCustomerCode'] = $jdalpha[2];
        }

        $shopRes = app::get('ome')->model('shop')->dump(array('shop_id' => $delivery['shop_id']), 'tbbusiness_type');
        $popFlag = $shopRes['tbbusiness_type'] == 'SOP' ? 1 : 0;

        $waybillInfos = array();
        foreach ($deliveryIdList as $key => $logiNo) {
            $waybillInfos[$key]['orderNo'] = $orderNo;
            $waybillInfos[$key]['popFlag'] = $popFlag;
            $waybillInfos[$key]['wayBillCode'] = $logiNo;
            $waybillInfos[$key]['jdWayBillCode'] = $logiNo;
        }

        $params['map_code'] = json_encode($mapCode);
        $params['waybill_infos'] = json_encode($waybillInfos);
        $params['object_id'] = substr(time(), 4) . uniqid();
        $back = $this->requestCall(STORE_USER_DEFINE_AREA, $params);

        $printData = '';
        if ($back['rsp'] == 'succ') {
            $data = json_decode($back['data'], true);
            $printData = $data['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['perPrintData'] ?: '';
        }
        return [$printData, $back['res']];
    }

    public function getEncryptPrintData($sdf)
    {
        return $this->error('不支持获取打印数据');
    }


    function intercetioncancel($sdf)
    {
        $waybillModel = app::get('logisticsmanager')->model('waybill');
        # 更新状态
        $waybillModel->update(array('status' => 2, 'create_time' => time()), array('id' => $sdf['waybill_id']));

        $this->primaryBn = empty($sdf['delivery_bn']) ? $sdf['waybill_number'] : $sdf['delivery_bn'];
        $this->title = $sdf['company_code'] . '取消电子面单';

        # 参数
        $params = array(
            'track_no' => $sdf['waybill_number']
        );
        $callback = array(
            'class' => get_class($this),
            'method' => 'intercetioncancelBack',
            'params' => $sdf
        );

        //error_log(date('Y-m-d H:i:s').': res-0:'.json_encode($params).PHP_EOL,3,DATA_DIR.'/interception.log');

        return $this->requestCall(STORE_WAYBILL_CANCEL, $params, $callback, $sdf['waybill_number']);
    }


    function intercetioncancelBack($result, $callback_params)
    {
        $interception_id = $callback_params['interception_id'];
        $interceptionObj = app::get('ome')->model('interception');
        $operationLogObj = app::get('ome')->model('operation_log');
        $return_product_obj = app::get('ome')->model('return_product');
        $interceptionInfo = $interceptionObj->dump(array('id' => $interception_id));

        //error_log(date('Y-m-d H:i:s').': res-3:'.json_encode($interceptionInfo).PHP_EOL,3,DATA_DIR.'/interception.log');
        //error_log(date('Y-m-d H:i:s').': res-4:'.$result['rsp'].PHP_EOL,3,DATA_DIR.'/interception.log');

        if (!$interceptionInfo) {
            return array('rsp' => 'fail', 'msg' => '没有找到拦截数据');
        }

        $delivery_bn = $interceptionInfo['delivery_bn'];
        $deliveryInfo = app::get('ome')->model('delivery')->dump(array('delivery_bn' => $delivery_bn));

        //error_log(date('Y-m-d H:i:s').': res-5:'.$delivery_bn.PHP_EOL,3,DATA_DIR.'/interception.log');

        $orderObj = app::get('ome')->model('orders');
        $orderInfo = $orderObj->dump(['order_id' => $interceptionInfo['order_id']]);

        if ($result['rsp'] == 'succ') {
            //其他快递拦截成功，创建系统退货单
            $deliveryInfo['return_id'] = $interceptionInfo['return_id'];
            $reshipInfo = kernel::single('ome_interception')->createReship($orderInfo, $deliveryInfo, 'cancel');
            if (!$reshipInfo) {
                $operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '回传:退货单创建失败,请手动创建退货单');
                $operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单(' . $deliveryInfo['delivery_bn'] . ')系统回传取消退货单创建失败,请手动创建退货单');
                $operationLogObj->write_log('return@ome', $interceptionInfo['return_id'], '发货单(' . $deliveryInfo['delivery_bn'] . ')系统回传取消退货单创建失败,请手动创建退货单');
                $msg = '回传:退货单创建失败,请手动创建退货单';
                $interceptionObj->update(['status' => 'return_failed', 'error_msg' => $msg], ['id' => $interceptionInfo['id']]);
            } else {
                $reship_bn = $reshipInfo['reship_bn'];
                $reship_id = $reshipInfo['reship_id'];
                $operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '退货单创建成功,退货单号: ' . $reship_bn);
                $operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单回传(' . $deliveryInfo['delivery_bn'] . ')快递取消退货单创建成功,退货单号: ' . $reship_bn);
                $operationLogObj->write_log('return@ome', $interceptionInfo['return_id'], '发货单回传(' . $deliveryInfo['delivery_bn'] . ')快递取消退货单创建成功,退货单号: ' . $reship_bn);
            }
            $interceptionObj->update(['status' => 'cancel_succ', 'reship_bn' => $reship_bn, 'reship_id' => $reship_id, 'return_logi_no' => $deliveryInfo['logi_no'], 'error_msg' => ''], ['id' => $interceptionInfo['id']]);
            $return_product_obj->update(array('intercept_type' => 'system', 'intercept_status' => '3'), array('return_id' => $interceptionInfo['return_id']));

            kernel::single('ome_interception')->son_succ($interceptionInfo['return_id'],$deliveryInfo,'cancel','cancel_succ');

            return array('rsp' => 'succ', 'msg' => '数据写入成功');
        } else {
            $interceptionObj->update(['status' => 'failed', 'error_msg' => $result['msg']], ['id' => $interceptionInfo['id']]);
            $operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '系统取消快递失败,' . $result['msg']);
            $operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单回传(' . $deliveryInfo['delivery_bn'] . ')系统取消快递失败,' . $result['msg']);
            $operationLogObj->write_log('return@ome', $interceptionInfo['return_id'], '发货单回传(' . $deliveryInfo['delivery_bn'] . ')系统取消快递失败,' . $result['msg']);
            return array('rsp' => 'fail', 'msg' => '系统取消快递失败,' . $result['msg']);
        }
    }
}
