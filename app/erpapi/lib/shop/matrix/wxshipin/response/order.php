<?php

/**
 * @Author: <EMAIL>
 * @Date: 2023/4/20
 * @Describe: 订单接收类
 */
class erpapi_shop_matrix_wxshipin_response_order extends erpapi_shop_response_order
{
    public $object_comp_key = 'bn-oid-obj_type';

    protected $_update_accept_dead_order = true;

    #平台订单状态
    protected $_sourceStatus = array(
        '10' => 'WAIT_BUYER_PAY', #待付款
        '20' => 'WAIT_SELLER_SEND_GOODS', #待发货
        '21' => 'SELLER_CONSIGNED_PART', #部分发货
        '30' => 'WAIT_BUYER_CONFIRM_GOODS', #待收货
        '100' => 'TRADE_FINISHED', # 完成
        '200' => 'TRADE_CLOSED', #取消
        '250' => 'TRADE_CLOSED', #取消
    );

    protected function _createAnalysis()
    {
        foreach ((array)$this->_ordersdf['order_objects'] as $objkey => $object) {
            foreach ($object['order_items'] as $itemkey => $item) {
                if ($item['status'] == 'close') {
                    $this->_ordersdf['cost_item'] = $this->_ordersdf['cost_item'] - $item['price'] * $item['quantity'];
                    $this->_ordersdf['pmt_order'] = $this->_ordersdf['pmt_order'] - $item['part_mjz_discount'];
                    $this->_ordersdf['total_amount'] = $this->_ordersdf['total_amount'] - $item['divide_order_fee'];
                    $this->_ordersdf['cur_amount'] = $this->_ordersdf['cur_amount'] - $item['divide_order_fee'];
                }
            }
        }
    }

    protected function _updateAnalysis()
    {
        $hasSend = array();
        foreach ($this->_tgOrder['order_objects'] as $ob) {
            foreach ($ob['order_items'] as $i) {
                if ($i['sendnum'] > 0) {
                    $tpKey = $ob['oid'] ? $ob['oid'] : $this->order_get_obj_key($ob);
                    $hasSend[$tpKey] = $ob;
                    break;
                }
            }
        }
        foreach ((array)$this->_ordersdf['order_objects'] as $objkey => $object) {
            $tpKey = $object['oid'] ? $object['oid'] : $this->order_get_obj_key($object);
            if ($hasSend[$tpKey]) {
                $this->_ordersdf['order_objects'][$objkey]['is_update'] = 'false';
                continue;
            }
            foreach ($object['order_items'] as $itemkey => $item) {
                if ($item['status'] == 'close') {
                    $this->_ordersdf['cost_item'] = $this->_ordersdf['cost_item'] - $item['price'] * $item['quantity'];
                    $this->_ordersdf['pmt_order'] = $this->_ordersdf['pmt_order'] - $item['part_mjz_discount'];
                    $this->_ordersdf['total_amount'] = $this->_ordersdf['total_amount'] - $item['divide_order_fee'];
                    $this->_ordersdf['cur_amount'] = $this->_ordersdf['cur_amount'] - $item['divide_order_fee'];
                }
            }
        }
        //兼容未支付状态，付款完成未变已支付
        if($this->_tgOrder['pay_status'] != '0'){
            $this->_ordersdf['payed'] = $this->_tgOrder['payed'];
            $this->_ordersdf['pay_status'] = $this->_tgOrder['pay_status'];
        }
    }

    protected function get_update_components()
    {
        $components = array('markmemo', 'custommemo', 'marktype');

        if ($this->_tgOrder) {
            $rs = app::get('ome')->model('order_extend')->getList('extend_status,bool_extendstatus', array('order_id' => $this->_tgOrder['order_id']));
            // 如果ERP收货人信息未发生变动时，则更新淘宝收货人信息
            if ($rs[0]['extend_status'] != 'consignee_modified') {
                $components[] = 'consignee';
            }
        }

        if (($this->_ordersdf['pay_status'] != $this->_tgOrder['pay_status']) || ($this->_ordersdf['shipping']['is_cod'] == 'true' && $this->_ordersdf['status'] == 'dead')) {
            $refundApply = app::get('ome')->model('refund_apply')->getList('apply_id', array('order_id' => $this->_tgOrder['order_id'], 'status|noequal' => '3'));
            // 如果没有退款申请单，以前端为主
            if (!$refundApply) {
                $components[] = 'master';
            }
        }
        $rs = app::get('ome')->model('order_extend')->getList('extend_status,bool_extendstatus', array('order_id' => $this->_tgOrder['order_id']));
        if (!($rs[0]['bool_extendstatus'] & ome_order_bool_extendstatus::__GOODS_PRICE)
            && !($rs[0]['bool_extendstatus'] & ome_order_bool_extendstatus::__MODIFY_SHIPPING)) {
            if (!array_search('master', $components)) $components[] = 'master';
            $components[] = 'items';
        }

        return $components;
    }

    protected function _analysis()
    {
        parent::_analysis();

        $this->_ordersdf['coupon_data'] = array();
        if (isset($this->_ordersdf['order_objects']) && !empty($this->_ordersdf['order_objects']) && (!isset($this->_ordersdf['is_history']) || $this->_ordersdf['is_history'] == 'false')) {
            // 矩阵返回优惠数据不请求接口，否则请求接口
            $ext_data = array();
            $ext_data['shop_id'] = $this->__channelObj->channel['shop_id'];
            $ext_data['shop_type'] = $this->__channelObj->channel['shop_type'];
            $ext_data['createtime'] = $this->_ordersdf['createtime'];
            $ext_data['order_bn'] = $this->_ordersdf['order_bn'];
            $ext_data['price_info'] = $this->_ordersdf['extend_field']['price_info'] ?? [];
            $ext_data['coupon_info'] = $this->_ordersdf['extend_field']['coupon_info'] ?? [];
            $ext_data['openid'] = $this->_ordersdf['member_info']['buyer_open_uid'];
            $ext_data['coupon_source'] = 'push';
            // 优惠明细数据format
            $result = kernel::single('ome_order_coupon')->couponDataFormat($this->_ordersdf['order_objects'], $ext_data, $ext_data['shop_type']);

            $this->_ordersdf['coupon_data'] = $result['coupon_data'];
            $this->_ordersdf['objects_coupon_data'] = $result['objects_coupon_data'];
        }
    }

    protected function get_create_plugins()
    {
        $plugins = parent::get_create_plugins();
        # 优惠相关
        $plugins[] = 'couponwxshipin';
        return $plugins;
    }

    public function get_update_plugins()
    {
        $plugins = parent::get_update_plugins();

        // 未支付变已支付 支付单缺失问题
        if ($this->_tgOrder['pay_status'] == '0' && $this->_ordersdf['pay_status'] == '1' && !in_array('payment', $plugins)) {
            $plugins[] = 'payment';
        }

        # 优惠相关
        $plugins[] = 'couponwxshipin';
        return $plugins;
    }
}