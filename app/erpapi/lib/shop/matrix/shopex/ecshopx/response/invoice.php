<?php

/**
 * 开票接口
 * Class erpapi_shop_matrix_shopex_ecshopx_response_invoice
 */
class erpapi_shop_matrix_shopex_ecshopx_response_invoice extends erpapi_shop_response_invoice
{
    public function xcxapply($params){
        $invoice_items = $params['invoice_items'];
        $order_mdl = app::get('ome')->model('orders');
        $order_objs_mdl = app::get('ome')->model('order_objects');

        //1.查看有没有相似订单并且没取消的
        $this->__apilog['title'] = '小程序开发票';
        if(!is_array($invoice_items)){
            $invoice_items = json_decode($invoice_items,1);
        }

        $add_invoice_data = [];
        $add_invoice_items = [];
        $order_bns = [];
        $total_amount = 0;
        $order_ids = [];
        foreach ($invoice_items as $invoice_info){

            $this->__apilog['title'] = '小程序开发票';
            $this->__apilog['original_bn'] = $invoice_info['invoice_apply_bn'];

            $order_bn = $invoice_info['order_bn'];
            $order_bns[] = $order_bn;
            $order_info = $order_mdl->dump(['order_bn'=>$order_bn],'order_id,order_bn,shop_id,ship_area,ship_addr,ship_tel,shop_type,tax_way');

            if(!$order_info){
                $this->__apilog['result']['msg'] = '自助开发票: 没有找到订单号:'.$order_bn;
                return false;
            }

            if($order_info['tax_way'] == '2'){
                $this->__apilog['result']['msg'] = '自助开发票: 订单号:'.$order_bn."线下已开票，请勿重复开票";
                return false;
            }

            $item_type = 'sales';
            //运费
            if($invoice_info['bn'] == 'shippingFeeLine888'){
                $item_type = 'ship';
                $order_objs_info = [
                    'name'=>'运费',
                    'goods_id'=>0,
                    'obj_id'=>$order_info['order_id']
                ];
            }else{
                $order_objs_info = $order_objs_mdl->dump(['oid'=>$invoice_info['oid']]);
                if(!$order_objs_info){
                    $order_objs_info = $order_objs_mdl->dump(['order_id'=>$order_info['order_id'],'bn'=>$invoice_info['bn']]);
                }

                if(!$order_objs_info){
                    $this->__apilog['result']['msg'] = '自助开发票: 没有找到对应 oid:'.$order_bn;
                    return false;
                }
            }



            $total_amount += $invoice_info['amount'];
            $order_ids[] = $order_info['order_id'];

            $add_invoice_data = [
                'mode'=>'1',
                'shop_id'=>$order_info['shop_id'],
                'title'=>$invoice_info['tax_title'],
                'tax_title'=>$invoice_info['tax_title'],
                'content'=>'',
                'invoice_receiver_name'=>$invoice_info['tax_title'],
                'ship_area'=>'',
                'ship_addr'=>'',
                'ship_tel'=>$invoice_info['tax_mobile'],
                'shop_type'=>$order_info['shop_type'],
                'receiver_email'=>$invoice_info['tax_email'],
                'ship_tax'=>$invoice_info['tax_no'],
                'remarks'=>'',
                'invoice_apply_bn'=>$invoice_info['invoice_apply_bn'],
                'town_code'=>$invoice_info['town_code'],
                'ship_email'=>$invoice_info['tax_email']
            ];

            //商品相关
            $add_invoice_items[] = [
                'item_type'=>$item_type,
                'bn'=>$invoice_info['bn'],
                'item_name'=>$order_objs_info['name'],
                'tax_code'=>$invoice_info['tax_no'],
                'quantity'=>$invoice_info['num'],
                'amount'=>$invoice_info['amount'],
                'bm_id'=>$order_objs_info['goods_id'],
                'order_bn'=>$order_bn,
                'order_id'=>$order_info['order_id'],
                'of_item_id'=>$order_objs_info['obj_id'],
                'of_id'=>$order_objs_info['obj_id'],
                'isxcx'=>'true',
                'oid'=>$invoice_info['oid'],
            ];
        }

        $add_invoice_data['amount'] = $total_amount;
        $add_invoice_data['order_bn'] = implode(',',array_unique($order_bns));
        $add_invoice_data['order_ids'] = $order_ids;
        $add_invoice_data['auto_invoice'] = 1;

        $settings = app::get('invoice')->model('order_setting')->getList('*');
        $set_info = [];
        foreach ($settings as $sinfo){
            $shop_ids = $sinfo['shopids'];
            $towncodes = $sinfo['towncodes'];
            if($shop_ids) {
                $shop_ids = explode(',', $sinfo['shopids']);
            }

            if($towncodes) {
                $towncodes = explode(',', $sinfo['towncodes']);
            }

            if(in_array($order_info['shop_id'],$shop_ids) && in_array($add_invoice_data['town_code'],$towncodes)) {
                $set_info = $sinfo;
                break;
            }
        }

        if($set_info) {
            $add_invoice_data['ship_bank'] = $set_info['bank'];
            $add_invoice_data['ship_bank_no'] = $set_info['bank_no'];
            $add_invoice_data['ship_company_addr'] = $set_info['address'];
            $add_invoice_data['ship_company_tel'] = $set_info['telphone'];
        }

        if(count($order_bns)>1) {
            $add_invoice_data['invoice_type'] = 'merge';
        }
        $add_invoice_data['items'] = $add_invoice_items;
        //来源
        $add_invoice_data['order_source'] = '1';

        $this->_sdf = $add_invoice_data;
        return  $this->_sdf;
    }
}