<?php

class erpapi_shop_matrix_luban_response_order extends erpapi_shop_response_order
{
    protected $_update_accept_dead_order = true;

    //平台订单状态
    protected $_sourceStatus = array(
        '1' => 'WAIT_BUYER_PAY',  //待确认/待支付（订单创建完毕）
        '105' => 'WAIT_SELLER_SEND_GOODS', //已支付
        '2' => 'SELLER_READY_GOODS',  //备货中
        '101' => 'SELLER_CONSIGNED_PART', //部分发货
        '3' => 'WAIT_BUYER_CONFIRM_GOODS',  //已发货（全部发货）
        '4' => 'TRADE_CLOSED',  //已取消
        '5' => 'TRADE_FINISHED',  //已完成（已收货）
    );
    //平台优惠名称
    protected $couponTypeName = [
        'promotion_talent_amount' => '达人优惠金额',
        'pay_amount' => '支付金额',
        'promotion_pay_amount' => '支付优惠金额',
        'promotion_platform_amount' => '平台优惠金额',
        'shop_cost_amount' => '商家承担金额',
        'promotion_shop_amount' => '店铺优惠金额',
        'promotion_redpack_platform_amount' => '平台红包优惠金额',
        'origin_amount' => '商品现价',
        'order_amount' => '订单金额',
        'promotion_amount' => '订单优惠总金额',
        'promotion_redpack_amount' => '红包优惠金额',
        'platform_cost_amount' => '平台承担金额',
        'author_cost_amount' => '作者（达人）承担金额',
    ];

    public function _canAccept()
    {
        $presalesetting = app::get('ome')->getConf('ome.order.presale');

        if (in_array($this->_ordersdf['trade_type'], array('step')) || in_array($this->_ordersdf['t_type'], array('step'))) {
            $this->_ordersdf['order_type'] = 'presale';
        }

        if (app::get('presale')->is_installed() && $presalesetting == '1' && $this->_ordersdf['order_type'] == 'presale') {
            if (in_array($this->_ordersdf['step_trade_status'], array('FRONT_PAID_FINAL_NOPAID'))) {
                $this->_accept_unpayed_order = true;
            }
        }

        if (($this->_accept_unpayed_order == false && in_array($this->_ordersdf['step_trade_status'], array('FRONT_NOPAID_FINAL_NOPAID', 'FRONT_PAID_FINAL_NOPAID'))) || ($this->_accept_unpayed_order == true && in_array($this->_ordersdf['step_trade_status'], array('FRONT_NOPAID_FINAL_NOPAID')))) {
            $this->__apilog['result']['msg'] = '定金未付尾款未付或定金已付尾款未付订单不接收';
            return false;
        }

        return parent::_canAccept();
    }

    protected function get_update_plugins()
    {
        $plugins = parent::get_update_plugins();

        if ((in_array($this->_tgOrder['order_type'], array('presale')))
            && ($this->_tgOrder['pay_status'] == '3' || $this->_tgOrder['total_amount'] != $this->_ordersdf['total_amount'])
            && $this->_tgOrder['cost_item'] == $this->_ordersdf['cost_item']
            && $this->_ordersdf['step_trade_status'] == 'FRONT_PAID_FINAL_PAID') {
            $plugins[] = 'payment';
        }
        // 未支付变已支付 支付单缺失问题
        if ($this->_tgOrder['pay_status'] == '0' && $this->_ordersdf['pay_status'] == '1' && !in_array('payment', $plugins)) {
            $plugins[] = 'payment';
        }
        $plugins[] = 'orderlabels'; // 更新抖音小店订单可更新中转集运标签
        $plugins[] = 'couponluban';
        return $plugins;
    }

    protected function get_update_components()
    {
        $components = array('markmemo', 'marktype', 'custommemo', 'booltype');

        if (($this->_ordersdf['pay_status'] != $this->_tgOrder['pay_status']) || ($this->_ordersdf['shipping']['is_cod'] == 'true' && $this->_ordersdf['status'] == 'dead')) {
            $refundApply = app::get('ome')->model('refund_apply')->getList('apply_id', array('order_id' => $this->_tgOrder['order_id'], 'status|noequal' => '3'));
            // 如果没有退款申请单，以前端为主
            if (!$refundApply) {
                $components[] = 'master';
            }
        }

        // 更新收货地址
        $rs = app::get('ome')->model('order_extend')->getList('extend_status', array('order_id' => $this->_tgOrder['order_id']));
        if ($rs[0]['extend_status'] != 'consignee_modified') {
            $orRe = app::get('ome')->model('order_receiver')->db_dump(['order_id' => $this->_tgOrder['order_id']], 'encrypt_source_data');
            $ensd = json_decode($orRe['encrypt_source_data'], 1);
            if (empty($ensd['open_address_id']) || $ensd['open_address_id'] != $this->_ordersdf['index_field']['open_address_id']) {
                $components[] = 'consignee';
            }
        }

        //查看是否预售订单已支付状态
        if (in_array($this->_tgOrder['order_type'], array('presale'))) {
            $components[] = 'tbpresale';
        }

        if ((in_array($this->_tgOrder['order_type'], array('presale')))
            && ($this->_tgOrder['pay_status'] == '3' || $this->_tgOrder['total_amount'] != $this->_ordersdf['total_amount'])
            && $this->_tgOrder['cost_item'] == $this->_ordersdf['cost_item']
            && $this->_ordersdf['step_trade_status'] == 'FRONT_PAID_FINAL_PAID') {
            if (!array_search('master', $components)) $components[] = 'master';
            $components[] = 'items';
        }

        return $components;
    }

    //创建订单的插件
    protected function get_create_plugins()
    {
        $plugins = parent::get_create_plugins();

        $plugins[] = 'orderextend';

        //费用明细（比如佣金、附加费）
        array_push($plugins, 'settle', 'coupon');

        $plugins[] = 'orderlabels';
        $plugins[] = 'couponluban';
        return $plugins;
    }

    protected function _analysis()
    {
        parent::_analysis();
        if ($this->_ordersdf['member_info']['buyer_open_uid']) {
            $this->_ordersdf['member_info']['uname'] = $this->_ordersdf['member_info']['buyer_open_uid'];
        }

        $this->_ordersdf['shipping']['shipping_name'] = '';

        // 虚拟发货
        if ($this->_ordersdf['shipping']['is_virtual_delivery'] == 'true') {
            $this->_ordersdf['shipping']['shipping_name'] = 'virtual_delivery';
        }

        //订单没有明细,直接返回
        if (empty($this->_ordersdf['order_objects']) && !is_array($this->_ordersdf['order_objects'])) {
            return true;
        }

        # 店铺字段
        $this->_ordersdf['shop_id'] = $this->__channelObj->channel['shop_id'];
        $this->_ordersdf['shop_type'] = $this->__channelObj->channel['shop_type'];
        $this->_ordersdf['org_id'] = $this->__channelObj->channel['org_id'];
        $this->_ordersdf['tag_key_list'] = [];
        //抖音订单扩展信息
        $authoList = array();
        $warehouseList = array();
        if ($this->_ordersdf['extend_field']) {
            //主播信息
            if ($this->_ordersdf['extend_field']['author_info']) {
                foreach ($this->_ordersdf['extend_field']['author_info'] as $oid => $orderVal) {
                    if ($orderVal['author_id']) {
                        $authoList[$oid]['author_id'] = $orderVal['author_id'];
                    }

                    if ($orderVal['author_name']) {
                        $authoList[$oid]['author_name'] = $orderVal['author_name'];
                    }

                    if ($orderVal['room_id']) {
                        $authoList[$oid]['room_id'] = $orderVal['room_id'];
                    }
                }
            }
            // 订单达人
            if ($authoList) {
                $this->_ordersdf['extend_field']['is_host'] = true;
            }

            //抖音增值服务
            $dy_added_service = '';
            if ($this->_ordersdf['extend_field']['platform_order_tag_ui']) {
                foreach ($this->_ordersdf['extend_field']['platform_order_tag_ui'] as $tag_val) {
                    $dy_added_service .= $tag_val['key'] . ',';

                    // 集运标识转成oms本地标识（二期）
                    // 改用platform_order_tag_ui识别，
                    // key=logistics_transit是偏远中转；key=remote_derict是偏远直邮,偏远直邮不用打标
                    if ($tag_val['key'] == 'logistics_transit') {
                        $consolList = [
                            '1' => 'XJJY', // 中国新疆中转
                        ];
                        if (!$this->_ordersdf['extend_field']['consolidate_info']) {
                            $this->_ordersdf['extend_field']['consolidate_info'] = [];
                        }
                        $this->_ordersdf['extend_field']['consolidate_info']['consolidate_type'] = $consolList['1'];
                    }
                }
            }
            if (!empty($dy_added_service)) {
                $this->_ordersdf['extend_field']['dy_added_service'] = substr($dy_added_service, 0, strlen($dy_added_service) - 1);
            }

            //指定仓发货
            if ($this->_ordersdf['extend_field']['store_info']) {
                $warehouseMdl = app::get('logisticsmanager')->model('warehouse_shop');
                $branchMdl = app::get('ome')->model('branch');

                foreach ($this->_ordersdf['extend_field']['store_info'] as $oid => $orderVal) {
                    if (empty($orderVal['inventory_list'])) {
                        continue;
                    }

                    foreach ($orderVal['inventory_list'] as $waKey => $waVal) {
                        // 兼容 out_warehouse_id
                        if (!$waVal['out_warehouse_id'] && $waVal['warehouse_id']) {
                            $local_warehouse = $warehouseMdl->db_dump(['outwarehouse_id' => $waVal['warehouse_id'], 'shop_id' => $this->__channelObj->channel['shop_id']], 'branch_id');

                            $branch = $branchMdl->db_dump(intval($local_warehouse['branch_id']), 'branch_bn');

                            $waVal['out_warehouse_id'] = $branch['branch_bn'];
                        }

                        if ($waVal['warehouse_id']) {
                            $warehouseList[$oid]['warehouse_ids'][] = $waVal['warehouse_id'];
                        }

                        if ($waVal['out_warehouse_id']) {
                            $warehouseList[$oid]['out_warehouse_ids'][] = $waVal['out_warehouse_id'];
                        }
                    }
                }
            }

            // 商品单标签，只要有一个商品是顺丰包邮，则整单打顺丰包邮的标签
            if ($this->_ordersdf['extend_field']['sku_order_tag_ui']) {
                foreach ($this->_ordersdf['extend_field']['sku_order_tag_ui'] as $oid => $skuVal) {
                    foreach ($skuVal as $sk => $sv) {
                        if ($sv['key'] == 'sf_free_shipping') {
                            $this->_ordersdf['sf_free_shipping'] = 'true';
                            break;
                        }
                    }
                }
            }

            if ($this->_ordersdf['sf_free_shipping'] == 'true') {
                $dly = app::get('ome')->model('dly_corp')->db_dump([
                    'type|in' => ['shunfeng', 'shunfengkuaiyun'],
                    'disabled' => 'false',
                ]);
                if ($dly) {
                    $this->_ordersdf['shipping']['shipping_name'] = $dly['type'];
                } else {
                    $this->_ordersdf['shipping']['shipping_name'] = 'shunfeng';
                }
            }

            /*
            // 集运标识转成oms本地标识
            if ($this->_ordersdf['extend_field']['consolidate_info']) {

                $consolList = [
                    '1'  => 'XJJY', // 中国新疆中转
                    // '2'  => 'XZJY', // 中国西藏中转 现在只有新疆中转
                ];
                $consolType = $this->_ordersdf['extend_field']['consolidate_info']['consolidate_type'];
                if ($consolList[$consolType]) {
                    $this->_ordersdf['extend_field']['consolidate_info']['consolidate_type'] = $consolList[$consolType];
                }
            }
            */

            //自选快递订单履约发货
            if (is_array($this->_ordersdf['extend_field']['order_tag']) && isset($this->_ordersdf['extend_field']['order_tag']['shop_optional_express_info'])) {
                $shop_optional_express_info = $this->_ordersdf['extend_field']['order_tag']['shop_optional_express_info'];
                if (is_string($shop_optional_express_info)) {
                    $this->_ordersdf['extend_field']['order_tag']['shop_optional_express_info'] = @json_decode($shop_optional_express_info, true);
                }
                foreach ($this->_ordersdf['extend_field']['order_tag']['shop_optional_express_info']['ExpressCompanys'] as $val) {
                    if (isset($val['ExpressCompanyCode']) && $val['ExpressCompanyCode']) {
                        $this->_ordersdf['shipping']['shipping_name'] = $val['ExpressCompanyCode'];
                        break;
                    }
                }
            }
        }

        //list
        if ($authoList || $warehouseList) {
            $ordFunLib = kernel::single('ome_order_func');

            $is_flag = false;
            foreach ($this->_ordersdf['order_objects'] as $key => $val) {
                $oid = $val['oid'];

                //主播信息
                if ($authoList[$oid]['author_id']) {
                    $this->_ordersdf['order_objects'][$key]['authod_id'] = $authoList[$oid]['author_id'];
                }

                if ($authoList[$oid]['author_name']) {
                    //主播姓名未过滤表情符,京东需要表情符
                    //@todo：需要sdb_ome_order_objects表支持utf8mb4字符集
                    $this->_ordersdf['order_objects'][$key]['author_name'] = $authoList[$oid]['author_name'];
                }

                //直播间ID
                if ($authoList[$oid]['room_id']) {
                    if (!$this->_ordersdf['order_objects'][$key]['addon']) {
                        $this->_ordersdf['order_objects'][$key]['addon'] = [];
                    }
                    $this->_ordersdf['order_objects'][$key]['addon']['room_id'] = $authoList[$oid]['room_id'];
                }

                //指定仓发货
                if ($warehouseList[$oid]['warehouse_ids']) {
                    $this->_ordersdf['order_objects'][$key]['warehouse_ids'] = $warehouseList[$oid]['warehouse_ids'];
                }

                if ($warehouseList[$oid]['out_warehouse_ids']) {
                    $this->_ordersdf['order_objects'][$key]['out_warehouse_ids'] = $warehouseList[$oid]['out_warehouse_ids'];

                    $is_flag = true;
                }

                // // 顺丰包邮
                // if ($sf_free_shipping[$oid]) {
                //     $this->_ordersdf['order_objects'][$key]['sf_free_shipping'] = 'true';
                // }
            }

            //是否指定仓发货
            if ($is_flag) {
                $this->_ordersdf['is_assign_store'] = 'true';
            }
        }

        # 是否福袋
        if (!empty($this->_ordersdf['extend_field']['platform_order_tag_ui'])) {
            $is_lucky_flag = false;
            foreach ($this->_ordersdf['extend_field']['platform_order_tag_ui'] as $row) {
                if ($row['key'] == 'draw_lottery') {
                    $is_lucky_flag = true;
                    break;
                }
            }
            if ($is_lucky_flag) {
                $this->_ordersdf['extend_field']['is_lucky_flag'] = '1';
            }
        }

        //抖音sku维度实付金额与优惠明细
        $this->_ordersdf['coupon_data'] = array();
        if (isset($this->_ordersdf['coupon_field']) && !empty($this->_ordersdf['coupon_field'])) {
            # 如果不是数组转成数组
            if (!is_array($this->_ordersdf['coupon_field'])) {
                $this->_ordersdf['coupon_field'] = json_decode($this->_ordersdf['coupon_field'], true);
            }

            $couponData = $this->_ordersdf['coupon_field'];
            $coupon = [];
            foreach ($couponData as $key => $value) {
                //type
                foreach ($value as $k => $v) {
                    if ($v <= 0 || in_array($k, array('sku_id', 'item_num', 'oid'))) {
                        continue;
                    }

                    $coupon[] = array(
                        'num' => $value['item_num'],
                        'oid' => $value['oid'],
                        'material_bn' => '',
                        'material_name' => '',
                        'type' => $k,
                        'type_name' => $this->couponTypeName[$k] ?? '',
                        'amount' => $v,
                        'create_time' => sprintf('%.0f', time()),
                        'pay_time' => $this->_ordersdf['payment_detail']['pay_time'] ?? '',
                        'shop_type' => $this->__channelObj->channel['shop_type'],
                    );
                }

                //实付金额
                $realPay = $value['pay_amount'] - $value['promotion_pay_amount'];
                $realPayData = [
                    'num' => $value['item_num'],
                    'oid' => $value['oid'],
                    'material_bn' => '',
                    'material_name' => '',
                    'type' => 'user_payamount',
                    'type_name' => '实付金额',
                    'amount' => $realPay,
                    'create_time' => sprintf('%.0f', time()),
                    'pay_time' => $this->_ordersdf['payment_detail']['pay_time'],
                    'shop_type' => $this->__channelObj->channel['shop_type'],
                ];

                array_push($coupon, $realPayData);
            }

            $this->_ordersdf['coupon_data'] = $coupon;
            unset($this->_ordersdf['coupon_field']);
        }

        unset($extendList);

        // 处理gift_mids
        reset($this->_ordersdf['order_objects']);
        foreach ($this->_ordersdf['order_objects'] as $objectKey => $object) {
            if (!isset($object['gift_mids']) || !$object['gift_mids']) {
                continue;
            }

            $gift_mids = json_decode($object['gift_mids'], true);
            if (!is_array($gift_mids) || !$gift_mids) {
                continue;
            }

            $gift_mids_str = implode(',', $gift_mids);

            $this->_ordersdf['order_objects'][$objectKey]['gift_mids'] = $gift_mids_str;
            // 订单明细层也需处理,存在提层情况
            foreach ($object['order_items'] as $itemKey => $orderItem) {
                if (!isset($orderItem['gift_mids']) || !$object['gift_mids']) {
                    continue;
                }

                $this->_ordersdf['order_objects'][$objectKey]['order_items'][$itemKey]['gift_mids'] = $gift_mids_str;
            }
        }
    }


    /**
     * 订单更新
     * @description：解决抖音修改备注,但更新时间无变化,导致OMS订单备注没有更新
     * @description：抖音订单去A
     */
    protected function _operationSel()
    {
        $lastmodify = kernel::single('ome_func')->date2time($this->_ordersdf['lastmodify']);

        $orderModel = app::get('ome')->model('orders');
        $filter = array(
            'order_bn' => $this->_ordersdf['order_bn'],
            'shop_id' => $this->__channelObj->channel['shop_id']
        );
        $tgOrder = $orderModel->dump($filter, '*', array('order_objects' => array('*', array('order_items' => array('*')))));
        if (empty($tgOrder) && substr($this->_ordersdf['order_bn'], -1) === 'A') {
            //把订单号去A再查下一遍，看订单是否存在
            $this->_ordersdf['order_bn'] = substr($this->_ordersdf['order_bn'], 0, -1);
            $filter = array(
                'order_bn' => $this->_ordersdf['order_bn'],
                'shop_id' => $this->__channelObj->channel['shop_id']
            );
            $tgOrder = $orderModel->dump($filter, '*', array('order_objects' => array('*', array('order_items' => array('*')))));
        }
        $this->_tgOrder = $tgOrder;

        $this->__apilog['result']['data'] = array('tid' => $this->_ordersdf['order_bn']);
        $this->__apilog['original_bn'] = $this->_ordersdf['order_bn'];
        $this->__apilog['title'] = '创建订单[' . $this->_ordersdf['order_bn'] . ']';

        if (empty($this->_tgOrder)) {
            $this->_operationSel = 'create';
        } elseif ($lastmodify > $this->_tgOrder['outer_lastmodify']) {
            $upData = array('outer_lastmodify' => $lastmodify);

            if ($this->_ordersdf['source_status']) {
                $upData['source_status'] = $this->_ordersdf['source_status'];
                if ($this->_ordersdf['source_status'] == 'TRADE_CLOSED') {
                    $orderModel->rebackDeliveryByOrderId($this->_tgOrder['order_id'], false, '平台订单状态取消');
                }
                $arr_create_invoice = array(
                    'order_id' => $this->_tgOrder['order_id'],
                    'source_status' => $this->_ordersdf['source_status']
                );
                kernel::single('invoice_order_front_router', 'b2c')->operateTax($arr_create_invoice);
            }

            //买家确认收货时间
            if ($this->_ordersdf['end_time']) {
                $upData['end_time'] = $this->_ordersdf['end_time'];
            }

            $orderModel->update($upData, array('order_id' => $this->_tgOrder['order_id'], 'outer_lastmodify|lthan' => $lastmodify));

            $affect_row = $orderModel->db->affect_row();

            if ($affect_row > 0) {
                $this->_operationSel = 'update';
            }
        }

        if (!$this->_operationSel && $this->_tgOrder) {
            $orderExtendModel = app::get('ome')->model('order_extend');
            $orderExtendInfo = $orderExtendModel->dump(['order_id' => $this->_tgOrder['order_id']], 'bool_extendstatus');
            if ($orderExtendInfo) {
                if ($orderExtendInfo['bool_extendstatus'] & ome_order_bool_extendstatus::__UPDATESKU_ORDER) {
                    $this->_operationSel = 'update';
                }
            }
        }

        if ($this->_operationSel == 'update') {
            if ($this->_ordersdf['status'] == 'dead' && $this->_tgOrder['status'] == 'active' && $this->_ordersdf['total_amount'] == $this->_tgOrder['total_amount'] && $this->_ordersdf['cost_item'] == $this->_tgOrder['cost_item'] && $this->_ordersdf['ship_status'] == '0') {
                $this->_operationSel = 'close';
            }
        }

        //订单操作不为空,则跳过
        if ($this->_operationSel) {
            return true;
        }

        ///////////////////////////////////////////
        // 解决订单备注没更新(淘宝平台问题，备注修改订单最后时间不变),
        // 同时防止比较明细，失败订单恢复后又重新更新为失败订单
        ///////////////////////////////////////////
        $memochg = false;

        if ($this->_tgOrder) {
            $last_custom_mark = array();
            $last_mark_text = array();
            $custom_mark = array();

            if ($this->_tgOrder['custom_mark'] && is_string($this->_tgOrder['custom_mark'])) {
                $custom_mark = unserialize($this->_tgOrder['custom_mark']);
            }

            $mark_text = array();
            if ($this->_tgOrder['mark_text'] && is_string($this->_tgOrder['mark_text'])) {
                $mark_text = unserialize($this->_tgOrder['mark_text']);
            }

            foreach ((array)$custom_mark as $key => $value) {
                if (strstr($value['op_time'], "-")) $value['op_time'] = strtotime($value['op_time']);

                if (intval($value['op_time']) > intval($last_custom_mark['op_time'])) {
                    $last_custom_mark = $value;
                }
            }

            foreach ((array)$mark_text as $key => $value) {
                if (strstr($value['op_time'], "-")) $value['op_time'] = strtotime($value['op_time']);

                if (intval($value['op_time']) > intval($last_mark_text['op_time'])) {
                    $last_mark_text = $value;
                }
            }

            if ($this->_ordersdf['custom_mark'] && $this->_ordersdf['custom_mark'] != $last_custom_mark['op_content']) {
                $memochg = true;
            }

            if ($this->_ordersdf['mark_text'] && $this->_ordersdf['mark_text'] != $last_mark_text['op_content']) {
                $memochg = true;
            }

            //更新订单平台状态(平台时间未变化,也需要更新)
            //@todo：抖音平台source_status字段状态值已经变化,但推送给OMS时lastmodify时间字段值未变,导致提示：更新时间没变，无需更新;
            if ($this->_ordersdf['source_status'] && $this->_ordersdf['source_status'] != $this->_tgOrder['source_status']) {
                $orderMdl = app::get('ome')->model('orders');

                //lastmodify
                $lastmodify = kernel::single('ome_func')->date2time($this->_ordersdf['lastmodify']);

                //sdf
                $upData = array();
                $upData['source_status'] = $this->_ordersdf['source_status'];

                //平台订单已取消，撤消OMS发货单
                if ($this->_ordersdf['source_status'] == 'TRADE_CLOSED') {
                    $orderMdl->rebackDeliveryByOrderId($this->_tgOrder['order_id'], false, '平台订单状态取消');
                }

                //update
                $orderMdl->update($upData, array('order_id' => $this->_tgOrder['order_id'], 'outer_lastmodify|sthan' => $lastmodify));
                $affect_row = $orderMdl->db->affect_row();
                if ($affect_row > 0) {
                    $this->_operationSel = 'update';
                }
            }
        }

        //更新订单操作
        if ($memochg) {
            $this->_operationSel = 'update';
        }
    }

    protected function get_convert_components()
    {
        $components = parent::get_convert_components();

        $components[] = 'tbpresale';
        return $components;
    }

    protected function formatItemsUpdateSkuSdf()
    {
        parent::formatItemsUpdateSkuSdf();
        if ($this->_ordersdf['change_sku']) {
            $change_sku = $this->_ordersdf['old_sku'];
            $old_sku = $this->_ordersdf['change_sku'];
            $this->_ordersdf['change_sku'] = $change_sku;
            $this->_ordersdf['old_sku'] = $old_sku;
        }
    }

    protected function _updateAnalysis()
    {
        // 更新订单的时候先清理当前订单的集运标识
        $order_id = $this->_tgOrder['order_id'];
        $omsConsolidateType = kernel::single('ome_bill_label')->consolidateTypeBox;
        $labelAll = app::get('omeauto')->model('order_labels')->getList('*', ['label_code|in' => $omsConsolidateType]);
        if ($labelAll) {
            $labelAll = array_column($labelAll, 'label_id');
            $error_msg = '';
            kernel::single('ome_bill_label')->delLabelFromBillId($order_id, $labelAll, 'order', $error_msg);
        }
    }

    protected function _createAnalysis()
    {
        parent::_createAnalysis();
        //保存优惠明细
        $this->getCouponDetailParamsFormat();
    }

    protected function getCouponDetailParamsFormat()
    {
        $ext_data['shop_type'] = $this->__channelObj->channel['shop_type'];
        $ext_data['payment_detail'] = $this->_ordersdf['payment_detail'];
        $ext_data['createtime'] = $this->_ordersdf['createtime'];
        $coupon_data = $this->_ordersdf['coupon_data'] ?: [];
        //自选快递订单履约发货
        if (is_array($this->_ordersdf['extend_field']['order_tag']) && isset($this->_ordersdf['extend_field']['order_tag']['shop_optional_express_info'])) {
            foreach ($this->_ordersdf['extend_field']['order_tag']['shop_optional_express_info']['ExpressCompanys'] as $val) {
                $amount = $this->_ordersdf['extend_field']['order_tag']['shop_optional_express_info']['Amount'];
                if (isset($val['ExpressCompanyCode']) && $val['ExpressCompanyCode']) {
                    $coupon_data[] = array(
                        'type' => 'expressCompanyFee',
                        'type_name' => '自选快递运费',
                        'material_bn' => 'expressCompanyFee',
                        'material_name' => '自选快递运费',
                        'amount' => $amount,
                        'total_amount' => $amount,
                        'create_time' => kernel::single('ome_func')->date2time($ext_data['createtime']),
                        'pay_time' => kernel::single('ome_func')->date2time($ext_data['payment_detail']['pay_time']),
                        'shop_type' => $ext_data['shop_type'],
                        'source' => 'local',
                    );
                }
            }

        }
        $this->_ordersdf['coupon_data'] = $coupon_data;
    }
}
