<?php

/**
 * @desc 退款单数据保存
 * @author: jintao
 * @since: 2016/7/21
 */
class erpapi_shop_response_process_refund
{

    public function add($params)
    {
        $refundApply = $params['refund_apply'];
        if ($params['refund']) {
            $msg = '退款单' . $params['refund_bn'] . '已存在';

            return array('rsp' => 'succ', 'msg' => $msg);
        }
        if ($params['status'] == 'succ' || $params['refund_type'] == 'refund') {
            return $this->_dealRefund($params);
        } elseif ($params['refund_type'] == 'apply') {
            return $this->_dealRefundApply($params);
        }
        return array('rsp' => 'fail', 'msg' => '该类型(refund_type: ' . $params['refund_type'] . ')状态(status:'.$params['status'].')不处理');
    }

    private function _dealRefund($params)
    {
        $refundApply = $params['refund_apply'];
        if($params['status'] == 'succ'){
            $msg = '生成退款单';
            $params['t_ready'] = kernel::single('ome_func')->date2time($params['t_ready']);
            $params['t_received'] = kernel::single('ome_func')->date2time($params['t_received']);
            $sdf = array(
                'refund_bn' => $params['refund_bn'],
                'shop_id' => $params['shop_id'],
                'order_id' => $params['order']['order_id'],
                'account' => $params['account'],
                'bank' => $params['bank'],
                'pay_account' => $params['pay_account'],
                'currency' => 'CNY',
                'money' => $params['money'],
                'paycost' => $params['paycost'],
                'cur_money' => $params['cur_money'],
                'pay_type' => $params['pay_type'],
                'payment' => $params['payment'] ? $params['payment'] : $refundApply['payment'],
                'paymethod' => $params['paymethod'],
                'download_time' => time(),
                'status' => $params['status'],
                'memo' => $params['memo'],
                'trade_no' => $params['trade_no'],
                'return_id' => $params['refund_apply']['return_id'],
                'refund_refer' => $params['refund_apply']['refund_refer'],
                'oid' => $params['oid'],
                't_ready' => $params['t_ready'],
                't_sent' => $params['t_sent'],
                't_received' => $params['t_received'],
            );
            $rs = app::get('ome')->model('refunds')->insert($sdf);
            if (!$rs) {
                return array('rsp' => 'fail', 'msg' => '退款单生成失败');
            }
            if ($refundApply) {
                $msg .= $this->_dealAfterRefund($refundApply, $sdf);
            }

            // 退款金额
            $params['refund_fee'] = $params['money'];
            //自动编辑订单&&符合条件并自动审单
            $error_msg = '';
            $is_abnormal = false;
            $isResultEdit = $this->_autoEditorder($params, $error_msg, $is_abnormal);
            if (!$isResultEdit) {
                $msg .= '&nbsp;&nbsp;' . $error_msg;
            }

            if ($params['update_order_payed']) {
                $rs = $this->_updateOrder($params['order']['order_id'], $params['money']);
                $rs && $msg .= "\n更新订单[{$params['order_bn']}]支付状态";
            }

            //发票红冲
            kernel::single('invoice_process')->invoice_cancel($params['order']);

            return array('rsp' => 'succ', 'msg' => $msg);
        }elseif($params['status'] == 'failed'){
            if($refundApply){
                $filter = array(
                    'apply_id' => $refundApply['apply_id'],
                );
                $updateData = array('status' => '6');//更新为退款失败
                app::get('ome')->model('refund_apply')->update($updateData, $filter);
                $msg = '更新退款申请单' . $refundApply['refund_apply_bn'];
                return array('rsp' => 'succ', 'msg' => $msg." 退款申请单更新成功");
            }
        }
        return array('rsp' => 'succ', 'msg' => "退款申请单更新成功");
    }

    /**
     * 平台推送的已退款单,需要编辑订单删除退款的商品
     *
     * @param $sdf 平台推送的退款数据
     * @param $error_msg 错误信息
     * @param $is_abnormal 是否为异常(订单已生成发货单,平台已退款但撤消发货单失败,导致删除订单退款商品失败)
     * @return bool
     */
    public function _autoEditorder($sdf, &$error_msg = null, &$is_abnormal = false)
    {
        $orderObj = app::get('ome')->model('orders');
        $orderObjMdl = app::get('ome')->model('order_objects');
        $orderItemObj = app::get('ome')->model("order_items");
        $logObj = app::get('ome')->model('operation_log');

        $refundLib = kernel::single('ome_order_refund');

        //orderInfo
        $order_id = $sdf['order']['order_id'];
        $order_filter = array('order_id' => $order_id);
        $order_detail = $orderObj->dump($order_filter, '*');

        if ($order_detail['status'] == 'dead') {
            $error_msg = '订单已经取消不能再编辑';
            return false;
        }

        //check支持的平台
        if (!in_array($order_detail['shop_type'], array('taobao', 'tmall', 'luban', 'website_d1m', 'website', 'weimobv', 'youzan', 'wxshipin', 'ecos.ecshopx'))) {
            $error_msg = $order_detail['shop_type'] . '平台订单不支持编辑';
            return false;
        }

        if ($order_detail['source'] != 'matrix') {
            $error_msg = '订单来源不是matrix类型';
            return false;
        }

        if (!in_array($order_detail['ship_status'], array('0', '2', '3'))) {
            $error_msg = '订单发货状态ship_status=' . $order_detail['ship_status'] . ',不允许编辑订单';
            return false;
        }

        //天猫只退款价保金额时,不编辑订单明细
        if ($sdf['isPriceProtect']) {
            $error_msg = '只退款价保金额,不允许编辑订单';
            return false;
        }

        //check过滤不需要删除订单明细的退款申请备注
        $reason = $sdf['memo'];
        if (!empty($reason)) {
            $reasonList = $refundLib->setReasonTypes();
            if (in_array($reason, $reasonList)) {
                $error_msg = '退款原因：' . $reason . ',不允许编辑订单';
                return false;
            }
        }

        //编辑订单失败时,更新异常状态
        $new_abnormal_status = $order_detail['abnormal_status'] | ome_preprocess_const::__ORDER_REFUND_ABNORMAL;

        //判断订单是否编辑过
        $item_list = $orderObj->getItemBranchStore($order_id);

        //格式化订单结构数据扩展信息
        ome_order_func::order_sdf_extend($item_list);

        //增加事务避免并发导致订单为部分退款状态
        $trans = kernel::database()->beginTransaction();
        //先更新订单主信息， 避免行明细更新导致死锁
        $update_sql = "UPDATE sdb_ome_orders SET createtime=`createtime` WHERE order_id=" . $order_id;
        $orderObj->db->exec($update_sql);

        //items
        $edit_flag = false;
        $needChangeFreezeItem = [];
        foreach ($item_list as $itemKey => $items) {
            foreach ($items as $item) {
                //oid
                if ($item['oid'] != $sdf['oid']) {
                    continue;
                }

                //check
                if ($item['delete'] == 'true') {
                    $error_msg = '删除商品bn：' . $item['bn'] . '失败,订单商品已经是删除状态。';
                    $logObj->write_log('order_edit@ome', $order_id, $error_msg);
                    continue;
                }

                //check退款金额是否与订单金额相等
                if ($sdf['refund_fee'] != $item['divide_order_fee'] && $sdf['refund_fee'] != $item['sale_price']) {
                    $error_msg = '删除商品bn：' . $item['bn'] . '失败,退款金额与订单商品金额不匹配';
                    $logObj->write_log('order_edit@ome', $order_id, $error_msg);
                    continue;
                }

                //check订单商品明细
                if (!empty($item['order_items'])) {
                    foreach ($item['order_items'] as $order_item) {
                        //check退款商品已经发货完成
                        if ($order_item['sendnum'] > 0) {
                            //update order_objects
                            $update_sql = "UPDATE sdb_ome_order_objects SET pay_status='5' WHERE order_id=" . $order_id . " AND obj_id=" . $item['obj_id'];
                            $orderObj->db->exec($update_sql);

                            //更新订单为异常状态
                            if (!$is_abnormal) {
                                $order_sql = "UPDATE sdb_ome_orders SET abnormal_status=" . $new_abnormal_status . " WHERE order_id=" . $order_id;
                                $orderObj->db->exec($order_sql);
                            }

                            //logs
                            $error_msg = '退款商品bn：' . $item['bn'] . '已经发货完成,无法删除!';
                            $logObj->write_log('order_edit@ome', $order_id, $error_msg);

                            //异常标记
                            $is_abnormal = true;

                            continue 2;
                        }

                        //check退款商品已经生成发货单,发货单打回失败
                        if ($order_item['split_num'] > 0) {
                            //update order_objects
                            $update_sql = "UPDATE sdb_ome_order_objects SET pay_status='5' WHERE order_id=" . $order_id . " AND obj_id=" . $item['obj_id'];
                            $orderObj->db->exec($update_sql);

                            //更新订单为异常状态
                            if (!$is_abnormal) {
                                $order_sql = "UPDATE sdb_ome_orders SET abnormal_status=" . $new_abnormal_status . " WHERE order_id=" . $order_id;
                                $orderObj->db->exec($order_sql);
                            }

                            //logs
                            $error_msg = '退款商品bn：' . $item['bn'] . '已经拆分生成发货单,无法删除!';
                            $logObj->write_log('order_edit@ome', $order_id, $error_msg);

                            //异常标记
                            $is_abnormal = true;

                            continue 2;
                        }
                    }
                }

                //优惠分摊金额
                $item['part_mjz_discount'] = $item['part_mjz_discount'] ? $item['part_mjz_discount'] : 0;

                //删除订单object层商品
                $delete_sql = "UPDATE sdb_ome_order_objects SET `delete`='true', pay_status='5' WHERE order_id=" . $order_id . " AND obj_id=" . $item['obj_id'];
                $affect_row = $orderObj->db->exec($delete_sql);
                if ($affect_row) {
                    $edit_flag = true;
                    $item['part_mjz_discount'] = $item['part_mjz_discount'] ? $item['part_mjz_discount'] : 0;

                    //删除item层货品
                    $affect_row = $orderItemObj->update(array('delete' => 'true'), array('order_id' => $order_id, 'obj_id' => $item['obj_id']));

                    //删除商品后释放冻结库存(有更新影响行数,才需要释放库存)
                    //@todo：现在矩阵同分同秒推送退款完成单和更新订单时，会导致并发重复释放库存冻结;
                    if (!is_bool($affect_row) && !empty($item['order_items'])) {
                        foreach ($item['order_items'] as $order_item) {
                            if (isset($order_item['delete']) && $order_item['delete'] == 'true') {
                                continue;
                            }
                            $needChangeFreezeItem[] = $order_item;
                        }
                    } else {
                        //logs
                        $error_msg = '删除订单item层商品bn：' . $item['bn'] . '失败';
                        $logObj->write_log('order_edit@ome', $order_id, $error_msg);
                    }
                } else {
                    //logs
                    $error_msg = '删除订单object层商品bn：' . $item['bn'] . '失败';
                    $logObj->write_log('order_edit@ome', $order_id, $error_msg);
                }

                //更新订单金额
                $order_sql = "UPDATE sdb_ome_orders SET pmt_goods=pmt_goods-" . $item['pmt_price'] . ", pmt_order=pmt_order-" . $item['part_mjz_discount'] . ", cost_item=cost_item-" . $item['amount'];
                $order_sql .= ", total_amount=total_amount-" . $sdf['refund_fee'] . ", final_amount=final_amount-" . $sdf['refund_fee'] . " WHERE order_id=" . $order_id;
                $orderObj->db->exec($order_sql);

                // custom 根据子订单id查询是否有关联赠品
                // $this->__deletePlatformGift($item['oid'], $order_id, $needChangeFreezeItem);
            }
        }
        if ($needChangeFreezeItem) {
            uasort($needChangeFreezeItem, [kernel::single('console_iostockorder'), 'cmp_productid']);

            $basicMStockLib = kernel::single('material_basic_material_stock');
            $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');

            foreach ($needChangeFreezeItem as $item) {
                // 预占释放
                $basicMStockLib->unfreeze($item['product_id'], abs($item['nums']));
                $basicMStockFreezeLib->unfreeze($item['product_id'], material_basic_material_stock_freeze::__ORDER, 0, $order_id, '', material_basic_material_stock_freeze::__SHARE_STORE, abs($item['nums']));
            }
        }

        //提交事务
        kernel::database()->commit($trans);

        //编辑订单成功
        if ($edit_flag) {
            $objectInof = $orderObjMdl->getList('name,quantity as num', array('order_id' => $order_id, 'delete' => 'false'));
            if ($objectInof) {
                $tostr = json_encode($objectInof);
                $orderObj->update(['tostr' => $tostr], ['order_id' => $order_id]);
            }

            //重置状态
            if ($order_detail['process_status'] == 'splitting') {
                $unSplitNum = app::get('ome')->model('delivery')->countOrderSplitNumber($order_detail['order_id']);
                if ($unSplitNum == 0) {
                    $orderObj->update(['process_status' => 'splited'], ['order_id' => $order_id]);
                }
            }

            //log
            $logObj->write_log('order_edit@ome', $order_id, "订单修改并恢复");

            //余单撤销
            if ($order_detail['ship_status'] == '2') {
                $unShipNum = app::get('ome')->model('delivery')->countOrderSendNumber($order_detail['order_id']);
                if ($unShipNum == 0) {
                    kernel::single('ome_order_order')->order_revoke($order_detail['order_id']);
                }
            }

            //将未修改以前的数据存储以便查询
            $log_id = $logObj->getList('log_id', array('operation' => 'order_edit@ome', 'obj_id' => $order_id), 0, 1, 'log_id DESC');
            $log_id = $log_id[0]['log_id'];
            $order_detail['item_list'] = $item_list;
            $orderObj->write_log_detail($log_id, $order_detail);

            //kernel::single('ome_order_func')->update_order_pay_status($order_id);

            //延迟5分钟自动重新路由审核订单
            $sdf = array('op_type' => 'timing_confirm', 'timing_time' => strtotime('5 minutes'), 'memo' => '退款完成编辑订单后重新路由');
            kernel::single('ome_order')->auto_order_combine($order_id, $sdf);
        } else {
            //订单退款商品无法删除,如有CRM赠品则打标记
            kernel::single('erpapi_shop_response_process_aftersalev2')->_labelOrderCrmGift($order_id);
        }

        //订单编辑失败：返回false,这样同步日志会有error_msg信息
        if ($is_abnormal) {
            return false;
        }
        return true;
    }

    private function _dealAfterRefund($refundApply, $sdf)
    {
        $filter = array(
            'apply_id' => $refundApply['apply_id'],
        );
        $updateData = array('status' => '4', 'refunded' => $sdf['money']);
        app::get('ome')->model('refund_apply')->update($updateData, $filter);
        $msg = "\n" . '更新退款申请单' . $refundApply['refund_apply_bn'];
        if ($refundApply['addon']) {
            $addon = unserialize($refundApply['addon']);
            $return_id = $addon['return_id'];
            $reship_id = $addon['reship_id'];
            if ($return_id) {
                $pReturnModel = app::get('ome')->model('return_product');
                $pReturnData = $pReturnModel->getList('refundmoney,return_bn', array('return_id' => $return_id), 0, 1);
                $pReturn = $pReturnData[0];
                $refundMoney = bcadd((float)$sdf['money'], (float)$pReturn['refundmoney'], 3);
                # 更新退款时间
                $pReturnModel->update(array('refundmoney' => $refundMoney, 'status' => '4', 'outer_lastmodify' => $sdf['t_received']), array('return_id' => $return_id));
                $return_bn = $pReturn['return_bn'];
                $msg .= "\n更新售后申请单[{$return_bn}]金额：" . $refundMoney;
            }
            if ($return_id || $reship_id) {
                //生成售后单
                kernel::single('sales_aftersale')->generate_aftersale($refundApply['apply_id'], 'refund');
            }
        }

        // 推送sap
        if (empty($refundApply['reship_id'])) {
            kernel::single('ome_sap_sap')->push_refund($refundApply['apply_id'], $refundApply['shop_type']);

            //没有发货的订单，生成一个取消发货单
            kernel::single('ome_delivery_cancel')->refund_create_by_refund_id($refundApply['apply_id']);

        } else {
            kernel::single('ome_sap_sap')->push_reship($refundApply["reship_id"], $refundApply['shop_type']);
        }
        return $msg;
    }

    private function _updateOrder($orderId, $refundMoney)
    {
        if (empty($orderId)) {
            return false;
        }

        //更新订单支付金额
        if ($refundMoney) {
            $sql = "update sdb_ome_orders set payed=IF((CAST(payed AS char)-IFNULL(0,cost_payment)-" . $refundMoney . ")>=0,payed-IFNULL(0,cost_payment)-" . $refundMoney . ",0)  where order_id=" . $orderId;
            kernel::database()->exec($sql);
        }

        //更新订单支付状态
        return kernel::single('ome_order_func')->update_order_pay_status($orderId);
    }

    private function _dealRefundApply($params)
    {
        if ($params['refund_apply']) {
            $oOperation_log = app::get('ome')->model('operation_log');//写日志
            $refundApply = $params['refund_apply'];
            $updateData = array(
                'status' => $params['status'],
            );
            if ($updateData['status'] == '0') {
                $sdf = $this->refund_apply_convert($params);
                $rs = app::get('ome')->model('refund_apply')->update($sdf, array('apply_id' => $refundApply['apply_id']));
                $memo = '(退款金额、原因或版本变化)退款申请单更新为未审核';
            } else {
                if ($params['memo']) {
                    if ($refundApply['memo'] && false === strpos($params['memo'], $refundApply['memo'])) {
                        $updateData['memo'] = $refundApply['memo'] . ',' . $params['memo'];
                    } elseif (!$refundApply['memo']) {
                        $updateData['memo'] = $params['memo'];
                    }
                }
                $filter = array('apply_id' => $refundApply['apply_id'], 'money' => $params['money']);
                $rs = app::get('ome')->model('refund_apply')->update($updateData, $filter);
                $memo = "更新退款申请单[{$refundApply['refund_apply_bn']}]状态成功：{$params['status']},影响行数：" . $rs;
            }

            if (is_bool($rs)) {
                return array('rsp' => 'fail', 'msg' => "更新退款申请单[{$refundApply['refund_apply_bn']}]状态失败：可能是金额不一致");
            } else {
                kernel::single('ome_order_func')->update_order_pay_status($params['order']['order_id']);

                $oOperation_log->write_log('refund_apply@ome', $refundApply['apply_id'], $memo);
                return array('rsp' => 'succ', 'msg' => "更新退款申请单[{$refundApply['refund_apply_bn']}]状态成功：{$params['status']},影响行数：" . $rs);
            }

        } else {
            $sdf = $this->refund_apply_convert($params);

            //创建退款单
            $is_update_order = true;//是否更新订单付款状态
            kernel::single('ome_refund_apply')->createRefundApply($sdf, $is_update_order, $error_msg);

            // 补充售前ag
            $aligenius_conf_limit_amount = app::get('ome')->getConf('shop.refund.amount.aliag.config.' . $params['shop_id']);
            if (!empty($aligenius_conf_limit_amount)) {
                if ($aligenius_conf_limit_amount > 0 && $sdf['money'] < $aligenius_conf_limit_amount) {
                    $this->_noticeAg($sdf);
                }
            } else {
                $this->_noticeAg($sdf);
            }

            return array('rsp' => 'succ', 'msg' => '退款申请单新建成功');
        }
    }

    private function refund_apply_convert($params)
    {
        $addon = serialize(array('refund_bn' => $params['refund_bn']));

        $params['t_ready'] = kernel::single('ome_func')->date2time($params['t_ready']);
        $sdf = array(
            'order_id' => $params['order']['order_id'],
            'refund_apply_bn' => $params['refund_bn'],
            'pay_type' => $params['pay_type'],
            'account' => $params['account'],
            'bank' => $params['bank'],
            'pay_account' => $params['pay_account'],
            'money' => $params['money'] ? $params['money'] : '0',
            'refunded' => '0',
            'memo' => $params['memo'],
            'create_time' => $params['t_ready'],
            'status' => $params['status'],
            'shop_id' => $params['shop_id'],
            'addon' => $addon,
            'source' => 'matrix',
            'shop_type' => $params['shop_type'],
        );
        if ($params['refund_refer']) {
            $sdf['refund_refer'] = $params['refund_refer'];
        }
        if ($params['bn']) {
            $sdf['bn'] = $params['bn'];
        }
        if ($params['oid']) {
            $sdf['oid'] = $params['oid'];
        }
        if ($params['product_data']) {
            $sdf['product_data'] = $params['product_data'];
        }
        if($params['belong_store_id']){
            $sdf['belong_store_id'] = $params['belong_store_id'];
        }

        return $sdf;
    }

    public function statusUpdate($params)
    {
        $filter = array(
            'refund_id' => $params['refund']['refund_id'],
            'status|noequal' => $params['status']
        );
        $updateData = array('status' => $params['status']);
        $msg = '更新退款单状态成功';
        $rs = app::get('ome')->model('refunds')->update($updateData, $filter);
        if (!is_bool($rs)) {
            if ($params['update_order_payed']) {
                $rs = $this->_updateOrder($params['order']['order_id'], $params['refund']['money']);
                $rs && $msg .= "\n更新订单[{$params['order_bn']}]支付状态";
            }
        }
        return array('rsp' => 'succ', 'msg' => $msg);

    }

    private function _noticeAg($sdf)
    {
        //取当前订单的处理状态
        $orderObj = app::get('ome')->model('orders');
        $order_filter = array("order_id" => $sdf['order_id']);
        $order_detail = $orderObj->dump($order_filter, 'order_bn,process_status,source');

        $aliag_status = app::get('ome')->getConf('shop.aliag.config.' . $sdf['shop_id']);
        // 兼容自有体系对接
        if ($aliag_status && in_array($sdf['shop_type'], array('ecos.ecshopx')) && $sdf['status'] == 0 && $order_detail['source'] == 'matrix') {
            //识别是否开启AG并且是天猫订单的新建退款申请
            $params = array(
                'order_bn' => $order_detail['order_bn'],
                'tid' => $order_detail['order_bn'],
                'order_id' => $sdf['order_id'],
                'apply_id' => $sdf['apply_id'],
                'refund_apply_id' => $sdf['apply_id'],
                'refund_bn' => $sdf['refund_apply_bn'],
                'is_aftersale_refund' => false,
                'shop_id' => $sdf['shop_id'],
                'money' => $sdf['money'],
                'pay_account' => $sdf['pay_account'],
            );
            $params['cancel_dly_status'] = 'SUCCESS';
            $dlyId = app::get('ome')->model('delivery')->getDeliverIdByOrderId($sdf['order_id']);
            //检查当前订单的状态
            if ($dlyId) {
                $params['cancel_dly_status'] = 'FAIL';
            }

            kernel::single('ome_service_refund')->refund_request($params);
        }
    }
}
