<?php

/**
 * Created by PhpStorm.
 * User: sqs
 * Date: 2022/7/15
 * Time: 6:05 PM
 */


class erpapi_sap_request_order extends erpapi_sap_request_abstract
{
    /**
     * 推送类型 - 支付
     * @var string
     */
    protected $__push_type = 'payed';

    public function push($sdf)
    {
        $rsaObj = kernel::single('erpapi_sap_rsa');
        $oOperation_log = app::get('ome')->model('operation_log');
        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $shopMdl = app::get('ome')->model('shop');

        # 时间检测
        $check_push = $this->check_push_time($this->__push_type, $sdf['order']['order_id']);
        if ($check_push) {
            return $this->error('单据正在处理，请稍候再试');
        }

        # 0元抽奖订单不推送esb
        if (!empty($sdf['order']['is_lucky_flag'])) {
            return $this->error('0元抽奖订单不推送esb');
        }

        # 未支付订单不推送esb
        if (in_array($sdf['order']['pay_status'], ['0', '3'])) {
            return $this->error('未支付或部分付款的订单不推送esb');
        }

        # 换货单生成的换出订单不推送esb
        if (!empty($sdf['order']['relate_order_bn'])) {
            return $this->error('换货单生成的换出订单不推送ESB');
        }

        # 设置推送时间
        $shopInfo = $shopMdl->dump(array('shop_id' => $sdf['order']['shop_id']), 'start_order_time');
        if (!empty($shopInfo['start_order_time'])) {
            $this->__push_time = $shopInfo['start_order_time'];
        }

        # 判断历史推送状态，不能重复推送
        $sapOrderInfo = $sapOrderMdl->dump(array('bill_id' => $sdf['order']['order_id'], 'bill_type' => $this->__push_type), '*');
        if (!empty($sapOrderInfo) && $sapOrderInfo['sync_status'] == 'succ') {
            $msg = '该单据（类型：' . $sapOrderInfo['bill_type'] . '）已经推送过了，不能重复推送';
            $oOperation_log->write_log('sap_order@ome', $sapOrderInfo['sap_id'], $msg);
            return $this->error($msg);
        }

        //        if (!empty($sdf['order']['relate_order_bn'])) {
        //            $sourceOrderBn = $orderObj->getSourceOrder($sdf['order']);
        //            if (!empty($sourceOrderBn)) {
        //                if ($sourceOrderBn == $sdf['order']['order_bn']) {
        //                    $sdf['order']['source_order_id'][] = $sdf['order']['order_id'];
        //                } else {
        //                    $orderInfo = $modelOrders->dump(array('order_bn' => $sourceOrderBn), 'order_id,order_bn');
        //                    $sdf['order']['source_order_id'][] = $orderInfo['order_id'];
        //                }
        //            }
        //        }

        # 保存中间表主表
        $sdf['sap_id'] = $sapOrderInfo['sap_id'];
        $sapOrderInfo = $this->saveSapMainData($sdf, $this->__push_type);

        try {
            # 其他扩展参数
            $sdf['ext']['sap_data'] = $sapOrderInfo;
            //params
            $params = $this->_format_params($sdf, $error_msg);
            if (!$params) {
                throw new Exception('数据本地验证失败,(' . $error_msg . ')');
            }

            # 0元订单状态变成无需推送
            if (isset($params['total_pay_amount']) && floatval($params['total_pay_amount']) <= 0) {
                $updateSap = [
                    'sync_status' => 'none',
                    'sync_time' => time()
                ];
                $sapOrderMdl->update($updateSap, array('sap_id' => $sapOrderInfo['sap_id']));
                # 合并修改数据
                $sapOrderInfo = array_merge($sapOrderInfo, $updateSap);
                # 删除无用字段
                unset($params['total_pay_amount']);
            }

            # 检查数据
            $order_data = json_decode($params['data'], true);
            # 扩展参数
            $order_data['extend'] = [
                'order_id' => $sdf['order']['order_id'],
                'push_type' => $this->__push_type,
                'shop_type' => $sdf['order']['shop_type'],
                'source_order_id' => $sdf['order']['source_order_id'] ?? [],
                'total_amount' => $sdf['order']['total_amount'],
                'is_group_buy_order' => $sdf['order']['is_group_buy_order'] ?? false, // 视频号的一起买订单
            ];
            $is_check = $this->_check_params($order_data, $error_msg);
            if ($is_check['status'] != 'succ') {
                throw new Exception('数据本地验证失败,(' . $is_check['msg'] . ')', ($is_check['status'] == 'none' ? 101 : 102));
            }
            $params['data'] = json_encode($order_data);
        } catch (Exception $ex) {
            $msg = $ex->getMessage();
            $oOperation_log->write_log('sap_order@ome', $sapOrderInfo['sap_id'], $msg);
            # 错误代码
            $code = $ex->getCode();
            $rsp = $code == 101 ? 'none' : 'fail';
            parent::_log_sync_status($sapOrderInfo['sap_id'], null, ['rsp' => $rsp, 'msg' => $msg]);
            return $this->error($msg);
        }

        # 检查推送开关
        if ($this->check_push_sap() == 'OFF') {
            # 记录日志
            $oOperation_log->write_log('sap_order@ome', $sapOrderInfo['sap_id'], '未开启推送开关，暂不推送');
            return $this->succ('success');
        }

        # 历史订单无需推送
        if ($sapOrderInfo['sync_status'] == 'none' || $this->is_history_order($sdf['order'])) {
            $oOperation_log->write_log('sap_order@ome', $sapOrderInfo['sap_id'], '该订单无需推送esb');
            return $this->succ('success');
        }

        # 只推送状态为待推送的
        if (isset($order_data['sync_status']) && $order_data['sync_status'] == 'none') {
            # 记录日志
            $oOperation_log->write_log('sap_order@ome', $sapOrderInfo['sap_id'], '此业务只推送日期【' . date('Y-m-d H:i:s', $this->__push_time) . '】之后的订单数据');
            return $this->succ('success');
        }

        if (!empty($params['data'])) {
            # data加密
            $params['data'] = $rsaObj->rsa_encode($sdf['order']['shop_type'], $params['data']);
        }

        # 签名
        $params['sign'] = $rsaObj->gen_sign($sdf['order']['shop_type'], $params);

        $this->__original_bn = $sdf['order']['order_bn'];
        $title = '支付订单推送SAP接口';
        # 请求接口
        $callback = array();
        $count = 0;
        $current_time = time();

        do {
            $response = $this->__caller->call(SAP_PUSH_SALES, $params, $callback, $title, 30, $this->__original_bn, true);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['err_msg'])) {
                break;
            }

            $count++;
        } while ($count < 3);

        # 记录推送状态
        parent::_log_sync_status($sapOrderInfo['sap_id'], $current_time, $response);
        # 记录操作日志
        $memo = $title . '：' . $response['rsp'];
        if ($response['rsp'] == 'fail') {
            $memo .= '，原因：' . $response['err_msg'];
        }
        $oOperation_log->write_log('sap_order@ome', $sapOrderInfo['sap_id'], $memo);
        return $response;
    }

    /**
     * 数据校验
     * @param $params
     * @param $error_msg
     * @return mixed
     */
    protected function _check_params($params, &$error_msg)
    {
        if (isset($params['extend']) && $params['extend']['is_history']) {
            return array('status' => 'succ');
        }

        # 视频号判断是否一起买活动
        if ($params['extend']['shop_type'] == 'wxshipin' && !empty($params['extend']['is_group_buy_order'])) {
            return array('status' => 'succ');
        }

        if (empty($params['orderItems'][0]['orderTime'])) {
            return array('status' => 'fail', 'msg' => '下单时间不能为空');
        }

        # 读取原始订单的支付单信息
        $order_id = $params['extend']['order_id'];
        if (!empty($params['extend']['source_order_id'])) {
            $order_id = is_array($params['extend']['source_order_id']) ? current($params['extend']['source_order_id']) : $params['extend']['source_order_id'];
        }
        # 检查是否存在订单的支付单
        $paymentsMdl = app::get('ome')->model('payments');
        $paymentList = $paymentsMdl->getList('pay_bn,money', array('order_id' => $order_id));
        if (empty($paymentList)) {
            return array('status' => 'none', 'msg' => '原始订单不存在支付单信息');
        }

        # 微信商城增加过滤的金额
        if ($params['extend']['shop_type'] == 'ecos.ecshopx') {
            $couponMdl = app::get('ome')->model('order_coupon_ecshopx');
            $notPushData = ['platform_type' => [], 'coupon_type' => []];
            # 店铺类型转换
            $shop_type = kernel::single('erpapi_sap_func')->getShopType($params['extend']['shop_type']);
            # 获取无需推送esb的优惠字段列表
            $notPushList = kernel::single('ome_sap_data_platform_' . $shop_type)->getNotPushCouponTypeList();
            if (!empty($notPushList)) {
                foreach ($notPushList as $item) {
                    list($platform_type, $coupon_type) = explode('@', $item);
                    if (!in_array($platform_type, $notPushData['platform_type'])) {
                        $notPushData['platform_type'][] = $platform_type;
                    }
                    if (!in_array($coupon_type, $notPushData['coupon_type'])) {
                        $notPushData['coupon_type'][] = $coupon_type;
                    }
                }
            }
            # 查询其他优惠
            $filter = [];
            if (!empty($notPushData['platform_type'])) {
                $filter['platform_type|in'] = $notPushData['platform_type'];
            }
            if (!empty($notPushData['coupon_type'])) {
                $filter['coupon_type|in'] = $notPushData['coupon_type'];
            }
            if (!empty($filter)) {
                # 增加订单ID查询条件
                $filter['order_id'] = $params['extend']['order_id'];
                $otherList = $couponMdl->getList('oid,coupon_amount', $filter);
                if (!empty($otherList)) {
                    # 刷新子单号
                    $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', $shop_type));
                    foreach ($otherList as $key => $item) {
                        $otherList[$key]['oid'] = $platformObj->getOid($params['extend']['order_id'], $item['oid']);
                    }
                    $otherData = array_column($otherList, null, 'oid');
                    # 设置已过滤的金额，用于后续金额的判断
                    foreach ($params['orderItems'] as $key => $item) {
                        $other_amount = empty($otherData[$item['eshopOrderSn']]) ? 0 : $otherData[$item['eshopOrderSn']]['coupon_amount'];
                        if (floatval($other_amount) <= 0) {
                            continue;
                        }
                        $params['orderItems'][$key]['orderPrice'] += $other_amount;
                        $params['orderItems'][$key]['discAmount'] += $other_amount;
                        $params['orderItems'][$key]['payAmount'] += $other_amount;
                    }
                    foreach ($params['goodItems'] as $key => $item) {
                        $other_amount = empty($otherData[$item['eshopOrderSn']]) ? 0 : $otherData[$item['eshopOrderSn']]['coupon_amount'];
                        if (floatval($other_amount) <= 0) {
                            continue;
                        }
                        $params['goodItems'][$key]['totalPrice'] += $other_amount;
                    }
                    foreach ($otherData as $item) {
                        $pay_item = [
                            "eshopOrderSn" => $item['oid'],
                            "amount" => $item['coupon_amount'],
                        ];
                        $params['paymentItems'][] = $pay_item;
                    }
                }
            }
        }

        # 检查参数
        $result = parent::_check_params($params, $new_msg);
        if (!$result) {
            return array('status' => 'fail', 'msg' => $new_msg);
        }
        return array('status' => 'succ');
    }

    /**
     * 销售单请求参数
     * @param $sdf
     * @param $error_msg
     * @return void
     */
    public function _format_params($sdf, &$error_msg)
    {
        if (empty($sdf['order']['order_objects'])) {
            $error_msg = '订单明细不存在';
            return false;
        }

        $shop_type = $sdf['order']['shop_type'];
        # appid
        $this->__app_id = $this->getAppId($shop_type);
        if (empty($this->__app_id)) {
            $error_msg = '未找到店铺类型为"' . $sdf['order']['shop_type'] . '"的appId映射关系';
            return false;
        }

        $data = $error_list = [];
        $result = ['oid' => [], 'source_oid' => [], 'coupon_id' => []];
        $total_pay_amount = 0;
        $orderItemsMdl = app::get('ome')->model('order_items');
        # 数据类
        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($shop_type)));
        # 运费分摊
        $new_objects = $this->_split_cost_freight_items($sdf['order']['order_objects'], $sdf['order']['cost_freight']);
        # 获取基础物料
        $materialList = $this->_getOrderBasicMaterial($sdf['order']['order_id'], true);
        foreach ($new_objects as $object) {
            $key = sprintf('%d_%s', $object['obj_id'], $object['bn']);
            $bmInfo = $materialList[$key] ?? [];

            if (empty($bmInfo['material_spu_id'])) {
                $error_list[] = '商品大码不能为空';
            }
            if (empty($bmInfo['org_no'])) {
                $error_list[] = '门店id不能为空';
            }
            if (empty($bmInfo['store_bn'])) {
                $error_list[] = '商户id不能为空';
            }
            if (empty($bmInfo['material_bn'])) {
                $error_list[] = '商品SKU信息不能为空';
            }
            if (empty($sdf['order']['paytime'])) {
                $error_list[] = '订单下单时间不能为空';
            }
            if (empty($object['oid'])) {
                $error_list[] = '商品编码：' . $object['bn'] . ',不存在oid子单号';
            }
            # 检查门店id与商户id是否一致
            if (!empty($bmInfo['org_no']) && !empty($bmInfo['store_bn'])) {
                $new_mallId = substr($bmInfo['store_bn'], 0, 4);
                if ($new_mallId != $bmInfo['org_no']) {
                    $error_list[] = '门店id[' . $bmInfo['org_no'] . ']与商户id[' . $bmInfo['store_bn'] . ']不一致';
                }
            }

            # 获取oid
            $oid = $platformObj->getOid($object['order_id'], $object['oid']);
            # 记录oid，用于读取支付方式和优惠券，优惠券需使用原始oid字段，支付方式需使用getOid()得到的oid字段
            $result['oid'][] = $oid;
            $result['source_oid'][] = $object['oid'];

            # 读取订单明细
            $orderItems = $orderItemsMdl->getList('shop_goods_id,shop_product_id', array('order_id' => $object['order_id'], 'obj_id' => $object['obj_id']));
            $itemInfo = current($orderItems);

            # 扩展参数
            $extend_data = [
                'shop_type' => $shop_type,
                'order_source' => $object['order_source'] ?? null
            ];

            # 订单信息
            $orderHead = [
                'orderSource' => $this->getOrderSource($shop_type, $extend_data),
                'orderType' => 'PAID', // 固定值
                'orderSn' => $sdf['order']['order_bn'],  // 订单编号
                'eshopOrderSn' => $oid, // 业务订单编号
                'mallId' => $bmInfo['org_no'], // 门店Id
                'shopId' => $bmInfo['store_bn'], // 商户id
                'spuId' => $bmInfo['material_spu_id'], // 商品大码
                'productId' => $itemInfo['shop_goods_id'] ?? '', // 平台商品ID
                'skuId' => $itemInfo['shop_product_id'] ?? '', // 平台SKU ID
                'vipId' => $sdf['order']['card_number'] ?? '', // 会员卡号
                'orderTime' => $sdf['order']['paytime'] . '000', // 订单下单/退单时间 长度：13位
                'orderPrice' => 0, // 订单总金额
                'discAmount' => 0, // 订单优惠金额
                'payAmount' => 0, // 订单实付金额
                'transAmount' => floatval($object['cost_freight'] ?? 0), // 运费
                'bankAmount' => 0, // 银行手续费
                'oriFlowNo' => '', // 流水号
            ];
            $data['orderItems'][] = $orderHead;

            # 订单明细
            $orderItem = [
                'eshopOrderSn' => $oid, // 在线商城业务订单编号
                'spuId' => $bmInfo['material_spu_id'] ?? '', // 商品大码
                'sku' => $bmInfo['material_bn'] ?? '', // 商品SKU信息
                'count' => intval($object['quantity']), // 订单购买商品数量
                'unitPrice' => 0, // 商品单价
                'totalPrice' => 0, // 商品总价
                'mallId' => $bmInfo['org_no'] ?? '', // 门店Id
            ];
            $data['goodItems'][] = $orderItem;
        }

        # 错误信息
        if (!empty($error_list)) {
            $error_msg = implode(';', $error_list);
            return false;
        }

        # 订单支付信息
        $error_message = '';
        $order_payments = $this->_format_order_payments($sdf, $shop_type, $result['oid'], $error_message);
        if (empty($order_payments)) {
            $error_msg = empty($error_message) ? '获取推送支付明细失败' : $error_message;
            return false;
        }

        # 获取优惠字段
        $discount_fields = kernel::single(sprintf('erpapi_sap_mapping_platform_%s', erpapi_sap_func::getShopType($shop_type)))->getDiscountFields();
        # 计算订单支付信息中的oid金额总和
        $paymentsOidList = ['payment' => [], 'discount' => []];
        foreach ($order_payments as $k => $item) {
            $key = $item['eshopOrderSn'];
            if (empty($key)) {
                continue;
            }
            # 记录优惠券code
            if (!empty($item['couponCode'])) {
                $result['coupon_id'][] = $item['couponCode'];
            }
            # 计算订单支付明细金额之和
            $paymentsOidList['payment'][$key] = bcadd($paymentsOidList['payment'][$key], $item['amount'], 2);
            # 计算订单明细中的优惠金额
            if (!empty($discount_fields) && in_array($item['addon']['sub_type'], $discount_fields)) {
                $paymentsOidList['discount'][$key] = bcadd($paymentsOidList['discount'][$key], $item['amount'], 2);
            }
        }

        $data['paymentItems'] = $order_payments;
        # 更新订单明细中的金额
        foreach ($data['orderItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['payAmount'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['orderItems'][$k]['orderPrice'] = $data['orderItems'][$k]['payAmount'];
            }
            if (!empty($paymentsOidList['discount'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['discAmount'] = floatval($paymentsOidList['discount'][$item['eshopOrderSn']]);
            }
            # 记录当前订单的总支付金额
            $total_pay_amount = bcadd($total_pay_amount, $data['orderItems'][$k]['payAmount'], 2);
        }
        # 更新sku明细中的支付金额
        foreach ($data['goodItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['goodItems'][$k]['totalPrice'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['goodItems'][$k]['unitPrice'] = floatval(bcdiv($data['goodItems'][$k]['totalPrice'], $item['count'], 2));
            }
        }

        # 订单优惠信息
        $order_coupons = $this->_format_order_coupons($sdf, $shop_type, $result['coupon_id']);
        if (!empty($order_coupons)) {
            $data['couponItems'] = $order_coupons;
        }

        # 保存中间表的明细
        $this->_saveSapItemsData($data, $sdf['ext']['sap_data']);

        # 删除扩展字段
        foreach ($data['paymentItems'] as $k => $item) {
            unset($data['paymentItems'][$k]['addon']);
            //小程序不需要推送 esb 过滤
            if($shop_type=='ecos.ecshopx' && $data['paymentItems'][$k]['paymentCode']=='no_esb'){
                unset($data['paymentItems'][$k]);
            }
        }
        foreach ($data['couponItems'] as $k => $item) {
            unset($data['couponItems'][$k]['eshopOrderSn']);
        }

        $params = [
            'appId' => $this->__app_id,
            'timestamp' => erpapi_sap_func::mixtimestamp(true),
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'total_pay_amount' => $total_pay_amount,
        ];
        return $params;
    }

    /**
     * 获取支付单列表
     * @param $sdf
     * @return void
     */
    private function _format_order_payments($sdf, $shop_type, $oid, &$error_message)
    {
        $result = [];
        # 判断来源订单号，如果存在来源订单号，则取原始订单的支付单信息
        if (empty($sdf['order']['source_order_id'])) {
            if (empty($sdf['payments'])) {
                $error_message = '支付明细列表不能为空';
                return false;
            }

            foreach ($sdf['payments'] as $item) {
                # 支付方式映射关系
                $paymentsData = erpapi_sap_func::getPlatformPaymentType($shop_type, $item);
                if (empty($paymentsData['code'])) {
                    $error_message = '支付方式[' . $item['type'] . ' > ' . $paymentsData['source'] . ']映射金蝶支付编码失败';
                    return false;
                } elseif (empty($paymentsData['name'])) {
                    $error_message = '支付方式[' . $item['type'] . ' > ' . $paymentsData['source'] . ']映射金蝶支付名称失败';
                    return false;
                }

                # 订单支付
                if ($item['type'] == 'payment') {
                    $payment = [
                        'eshopOrderSn' => $item['oid'], // 业务订单编号
                        'paymentType' => $paymentsData['name'], // 金蝶支付方式名称(DP支付名称)
                        'paymentCode' => $paymentsData['code'], // 金蝶支付方式编码，包含平台补贴(金蝶支付编码)
                        'originPaymentType' => $item['paymethod'], // 原始支付方式名称
                        'originPaymentCode' => empty($item['payment']) ? $item['paymethod'] : $item['payment'], // 原始支付方式编码
                        'amount' => floatval($item['money']), // 支付金额
                        'subsidyAmount' => 0, // 平台补贴金额
                        // 扩展字段，用于后续计算用
                        'addon' => [
                            'type' => $item['type'],
                            'sub_type' => $item['coupon_type'],
                            'amount' => floatval($item['money'])
                        ],
                    ];
                    # 商城微信支付原始编码转换成支付名称
                    if ($shop_type == 'ecos.ecshopx' && $payment['originPaymentCode'] == 'wxpay') {
                        $payment['originPaymentCode'] = $item['paymethod'];
                    }
                    $result[] = $payment;
                }

                # 平台活动
                if ($item['type'] == 'platform') {
                    $payment = [
                        'eshopOrderSn' => $item['oid'], // 业务订单编号
                        'paymentType' => $paymentsData['name'], // 金蝶支付方式名称(DP支付名称)
                        'paymentCode' => $paymentsData['code'], // 金蝶支付方式编码，包含平台补贴(金蝶支付编码)
                        'originPaymentType' => $item['paymethod'], // 原始支付方式名称
                        'originPaymentCode' => $item['coupon_type'], // 原始支付方式编码
                        'amount' => floatval($item['money']), // 支付金额
                        'subsidyAmount' => 0, // 平台补贴金额
                        // 扩展字段，用于后续计算用
                        'addon' => [
                            'type' => $item['type'],
                            'sub_type' => $item['sub_type'],
                            'amount' => floatval($item['money']),
                            'category' => $item['category'],
                        ],
                    ];
                    # 券号
                    if (!empty($item['coupon_code'])) {
                        $payment['couponCode'] = $item['coupon_code'];
                    }
                    $result[] = $payment;
                }
            }
        } else {
            $payments = $this->_getSouceOrderPayments($sdf['order']['source_order_id'], $oid);
            if (!empty($payments)) {
                $result = array_merge($result, $payments);
            }
        }
        return $result;
    }

    /**
     * 获取订单优惠券列表
     * @param $sdf
     * @param $shop_type
     * @return mixed
     */
    private function _format_order_coupons($sdf, $shop_type, $coupon_id = [])
    {
        $result = [];
        # 判断来源订单号，如果存在来源订单号，则取原始订单的优惠券信息
        if (empty($sdf['order']['source_order_id'])) {
            if (empty($sdf['coupon'])) {
                return $result;
            }
            foreach ($sdf['coupon'] as $item) {
                $coupon = [
                    'eshopOrderSn' => $item['eshopOrderSn'], // 子订单号
                    'couponCode' => $item['couponCode'], // 券号码
                    'couponName' => $item['couponName'], // 券名称
                    'couponAmount' => floatval($item['couponAmount']), // 券面值
                    'couponType' => $item['couponType'], // 成本分摊方（平台，商家）
                ];
                $result[] = $coupon;
            }
        } else {
            $coupons = $this->_getSourceOrderCoupons($sdf['order']['source_order_id'], $shop_type, $coupon_id);
            if (!empty($coupons)) {
                $result = array_merge($result, $coupons);
            }
        }
        return $result;
    }

    /**
     * 保存到sap中间数据表 - 主数据
     * @param $sdf
     * @return mixed
     */
    protected function saveSapMainData($sdf, $bill_type)
    {
        $orderMdl = app::get('sales')->model('sap_orders');

        $order_data = [
            'bill_id' => $sdf['order']['order_id'],
            'bill_no' => $sdf['order']['order_bn'],
            'bill_type' => $bill_type,
            'shop_id' => $sdf['order']['shop_id'],
            'shop_type' => $sdf['order']['shop_type'],
            'create_time' => time()
        ];
        # 3月25日0点之前正向订单无需推送
        if (!empty($sdf['order']['paytime']) && !empty($this->__push_time) && $sdf['order']['paytime'] < $this->__push_time) {
            $order_data['sync_status'] = 'none';
        }
        # 来源订单ID
        if (!empty($sdf['order']['source_order_id'])) {
            $order_data['source_bill_id'] = implode(',', $sdf['order']['source_order_id']);
        } else {
            $order_data['source_bill_id'] = $sdf['order']['order_id'];
        }
        if (!empty($sdf['sap_id'])) {
            $order_data['sap_id'] = $sdf['sap_id'];
        }
        # 历史订单状态为无需推送
        if ($this->is_history_order($sdf['order'])) {
            $order_data['sync_status'] = 'none';
        }
        $result = $orderMdl->save($order_data);
        if (!$result) {
            return false;
        }
        return $order_data;
    }

    public function onlyPush($sap_id, $sdf)
    {
        $this->__original_bn = $sdf['orderItems'][0]['orderSn'];
        $title = '支付订单推送SAP接口';
        # 请求接口
        $callback = array();
        $count = 0;
        $current_time = time();
        $oOperation_log = app::get('ome')->model('operation_log');

        do {
            $response = $this->__caller->call(SAP_PUSH_SALES, $sdf, $callback, $title, 30, $this->__original_bn, true);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['msg'])) {
                break;
            }
            $count++;
        } while ($count < 3);

        # 记录推送状态
        parent::_log_sync_status($sap_id, $current_time, $response);
        # 记录操作日志
        $memo = $title . '：' . $response['rsp'];
        if ($response['rsp'] == 'fail') {
            $memo .= '，原因：' . $response['msg'];
        }
        $oOperation_log->write_log('sap_order@ome', $sap_id, $memo);
        return $response;
    }
}
