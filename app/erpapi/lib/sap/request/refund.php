<?php
/**
 * Created by PhpStorm.
 * User: sqs
 * Date: 2022/7/15
 * Time: 6:05 PM
 */


class erpapi_sap_request_refund extends erpapi_sap_request_abstract
{
    /**
     * 推送类型 - 仅退款
     * @var string
     */
    protected $__push_type = 'refund';

    public function push($sdf)
    {
        $rsaObj = kernel::single('erpapi_sap_rsa');
        $oOperation_log = app::get('ome')->model('operation_log');
        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $orderObj = kernel::single('ome_order');
        $modelOrders = app::get('ome')->model('orders');
        $reshipMdl = app::get('ome')->model('reship');

        # 时间检测
        $check_push = $this->check_push_time($this->__push_type, $sdf['refund_apply']['apply_id']);
        if ($check_push) {
            return $this->error('单据正在处理，请稍候再试');
        }

        # 部分退款标识不推送esb
        if (!empty($sdf['refund_apply']['is_part_refund']) && $sdf['refund_apply']['is_part_refund'] == '1') {
            return $this->error('订单存在部分退款不推送esb');
        }

        # 原始订单为0元抽奖订单，不推送esb
        if (!empty($sdf['order']['is_lucky_flag'])) {
            return $this->error('原始订单为0元抽奖订单，不推送esb');
        }

        # 判断历史推送状态，不能重复推送
        $sapOrderInfo = $sapOrderMdl->dump(array('bill_id' => $sdf['refund_apply']['apply_id'], 'bill_type' => $this->__push_type), '*');
        if (!empty($sapOrderInfo) && $sapOrderInfo['sync_status'] == 'succ') {
            $msg = '该单据（类型：' . $sapOrderInfo['bill_type'] . '）已经推送过了，不能重复推送';
            $oOperation_log->write_log('sap_refund@ome', $sapOrderInfo['sap_id'], $msg);
            return $this->error($msg);
        }

        # 是否历史订单
        $is_history = $this->is_history_order($sdf['refund_apply']);
        # 判断是否存在来源订单号
        if (!empty($sdf['order']['relate_order_bn'])) {
            # 判断订单是否为换货单
            $is_change = $reshipMdl->is_change_order($sdf['order']['order_id']);
            if ($is_change) {
                $sourceOrderBn = $orderObj->getSourceOrder($sdf['order']);
                if (!empty($sourceOrderBn)) {
                    $orderInfo = $modelOrders->dump(array('order_bn' => $sourceOrderBn), 'order_id,order_bn');
                    $sdf['refund_apply']['source_order_id'][] = $orderInfo['order_id'];
                }
            }
        }

        # 保存中间表主表
        if (empty($sapOrderInfo)) {
            $sapOrderInfo = $this->saveSapMainData($sdf, $this->__push_type);
        }

        try {
            # 其他扩展参数
            $sdf['ext']['sap_data'] = $sapOrderInfo;
            $sdf['ext']['is_history'] = $is_history;
            $sdf['ext']['order_is_history'] = $this->is_history_order($sdf['order']);
            //params
            $params = $this->_format_params($sdf, $error_msg);
            if (!$params) {
                throw new Exception('数据本地验证失败,(' . $error_msg . ')');
            }

            # 0元订单状态变成无需推送
            if (isset($params['total_pay_amount']) && floatval($params['total_pay_amount']) <= 0) {
                $updateSap = [
                    'sync_status' => 'none',
                    'sync_time' => time()
                ];
                $sapOrderMdl->update($updateSap, array('sap_id' => $sapOrderInfo['sap_id']));
                # 合并修改数据
                $sapOrderInfo = array_merge($sapOrderInfo, $updateSap);
                # 删除无用字段
                unset($params['total_pay_amount']);
            }

            # 检查数据
            $order_data = json_decode($params['data'], true);
            # 扩展参数
            $order_data['extend'] = [
                'order_id' => $sdf['order']['order_id'],
                'source_order_id' => empty($sdf['refund_apply']['source_order_id']) ? $sdf['order']['order_id'] : $sdf['refund_apply']['source_order_id'],
                'shop_type' => $sdf['order']['shop_type'],
                'shop_id' => $sdf['order']['shop_id'],
                'is_history' => $is_history,
            ];
            $is_check = $this->_check_params($order_data, $error_msg);
            if (!$is_check) {
                throw new Exception('数据本地验证失败,(' . $error_msg . ')');
            }

            # 替换历史子单号
            /*if ($sdf['ext']['order_is_history'] && !empty($params['oid_mapping'])) {
                foreach ($order_data['orderItems'] as $key => $item) {
                    if (empty($params['oid_mapping'][$item['oriFlowNo']])) {
                        continue;
                    }
                    $order_data['orderItems'][$key]['oriFlowNo'] = $params['oid_mapping'][$item['oriFlowNo']];
                }
                # 替换data参数
                unset($params['oid_mapping'], $order_data['extend']);
                $params['data'] = json_encode($order_data, JSON_UNESCAPED_UNICODE);
            }*/
        } catch (Exception $ex) {
            $msg = $ex->getMessage();
            $oOperation_log->write_log('sap_refund@ome', $sapOrderInfo['sap_id'], $msg);
            parent::_log_sync_status($sapOrderInfo['sap_id'], null, ['rsp' => 'fail', 'msg' => $msg]);
            return $this->error($msg);
        }

        # 检查推送开关
        if ($this->check_push_sap() == 'OFF') {
            # 记录日志
            $oOperation_log->write_log('sap_refund@ome', $sapOrderInfo['sap_id'], '未开启推送开关，暂不推送');
            return $this->succ('success');
        }

        # 历史退款单无需推送
        if ($sapOrderInfo['sync_status'] == 'none' || $is_history) {
            $oOperation_log->write_log('sap_refund@ome', $sapOrderInfo['sap_id'], '该退款单无需推送esb');
            return $this->succ('success');
        }

        # data加密
        if (!empty($params['data'])) {
            $params['data'] = $rsaObj->rsa_encode($sdf['refund_apply']['shop_type'], $params['data']);
        }

        # 签名
        $params['sign'] = $rsaObj->gen_sign($sdf['refund_apply']['shop_type'], $params);
        $this->__original_bn = $sdf['order']['order_bn'];
        $title = '订单退款推送SAP接口';
        # 请求接口
        $callback = array();
        $count = 0;
        $current_time = time();

        do {
            $response = $this->__caller->call(SAP_PUSH_SALES, $params, $callback, $title, 30, $this->__original_bn, true);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['msg'])) {
                break;
            }
            $count++;
        } while ($count < 3);

        # 记录推送状态
        parent::_log_sync_status($sapOrderInfo['sap_id'], $current_time, $response);
        # 记录操作日志
        $memo = $title . '：' . $response['rsp'];
        if ($response['rsp'] == 'fail') {
            $memo .= '，原因：' . $response['msg'];
        }
        $oOperation_log->write_log('sap_refund@ome', $sapOrderInfo['sap_id'], $memo);
        return $response;
    }

    /**
     * 请求参数
     * @param $sdf
     * @param $error_msg
     * @return array|bool
     */
    public function _format_params($sdf, &$error_msg)
    {
        if (empty($sdf['refund_apply']['product_list'])) {
            $error_msg = '仅退款SKU明细不能为空';
            return false;
        }

        $shop_type = $sdf['order']['shop_type'];
        # appid
        $this->__app_id = $this->getAppId($shop_type);
        if (empty($this->__app_id)) {
            $error_msg = '未找到店铺类型为"' . $sdf['order']['shop_type'] . '"的appId映射关系';
            return false;
        }

        $data = $error_list = $oid_mapping = [];
        $total_pay_amount = 0;
        $refundData = ['oid' => [], 'source_oid' => [], 'coupon_id' => []];
        # 数据类
        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($shop_type)));

        foreach ($sdf['refund_apply']['product_list'] as $k => $item) {
            # 获取基础物料
            $bmInfo = $this->getMaterial($item['bn'], 'material_bn,material_spu_id');
            if (empty($bmInfo['material_spu_id'])) {
                $error_list[] = '商品大码不能为空';
            }
            if (empty($bmInfo['material_bn'])) {
                $error_list[] = '商品SKU信息不能为空';
            }
            if (empty($sdf['refund_apply']['refund_time'])) {
                $error_list[] = '退单时间不能为空';
            }
            # 子单号
            $oid = $platformObj->getOid($sdf['order']['order_id'], $item['oid']);
            # 扩展参数
            $extend_data = [
                'shop_type' => $shop_type,
                'order_source' => $sdf['order_source_oid'][$oid] ?? null,
            ];

            # 获取线下门店信息
            $storeInfo = $this->getEsbStoreByOrder($sdf['order']['order_id'], $item['bn']);
            if (empty($storeInfo['org_no'])) {
                $error_list[] = '门店id不能为空';
            }
            if (empty($storeInfo['store_bn'])) {
                $error_list[] = '商户id不能为空';
            }
            # 检查门店id与商户id是否一致
            if (!empty($storeInfo['org_no']) && !empty($storeInfo['store_bn'])) {
                $new_mallId = substr($storeInfo['store_bn'], 0, 4);
                if ($new_mallId != $storeInfo['org_no']) {
                    $error_list[] = '门店id[' . $storeInfo['org_no'] . ']与商户id[' . $storeInfo['store_bn'] . ']不一致';
                }
            }

            # 订单信息
            $orderHead = [
                'orderSource' => $this->getOrderSource($shop_type, $extend_data),
                'orderType' => 'REFUNDED', // 固定值
                'orderSn' => $sdf['refund_apply']['refund_apply_bn'],  // 订单编号
                'mallId' => $storeInfo['org_no'] ?? '', // 小镇编码
                'shopId' => $storeInfo['store_bn'] ?? '', // 线下门店编码
                'spuId' => $bmInfo['material_spu_id'] ?? '', // 商品大码
                'productId' => $item['shop_goods_id'] ?? '', // 平台商品ID
                'skuId' => $item['shop_product_id'] ?? '', // 平台SKU ID
                'vipId' => $sdf['order']['card_number'] ?? '', // 会员卡号
                'orderTime' => $sdf['refund_apply']['refund_time'] . '000', // 订单下单/退单时间 长度：13位
                'orderPrice' => floatval(empty($sdf['refund_apply']['refunded']) ? $sdf['refund_apply']['money'] : $sdf['refund_apply']['refunded']), // 订单总金额
                'discAmount' => 0, // 订单优惠金额
                'transAmount' => 0, // 运费
                'bankAmount' => 0, // 银行手续费
                'oriFlowNo' => $oid, // 流水号，如果是退款单，则传订单号
            ];

            # 设置历史子单号
            /*if ($sdf['ext']['order_is_history']) {
                if (empty($item['history_oid'])) {
                    $error_list[] = '子单号：' . $oid . '，对应的历史子单号为空';
                } else {
                    # 记录当前子单号对应历史子单号的映射关系，方便后续替换
                    $oid_mapping[$oid] = $item['history_oid'];
                }
            }*/

            # 微信商城的运费放在最后一个退款单上（支付为状态为全额退款时，带上运费）
            if($shop_type != 'ecos.ecshopx' || ($shop_type === 'ecos.ecshopx' && $sdf['order']['pay_status'] == '5')) {
                $orderHead['transAmount'] = floatval($sdf['order']['cost_freight']);   // 运费
            }
            # 序号
            $indexNo = str_pad($k + 1, 3, '0', STR_PAD_LEFT);
            $orderHead['eshopOrderSn'] = $orderHead['orderSn'] . $indexNo;
            # 商品退款金额
            $orderHead['payAmount'] = $orderHead['orderPrice'];
            $data['orderItems'][] = $orderHead;
            # 记录oid
            $refundData['oid'][$oid] = $orderHead['eshopOrderSn'];
            $refundData['source_oid'][] = $item['oid'];

            # 订单明细
            $orderItem = [
                'eshopOrderSn' => $orderHead['eshopOrderSn'], // 在线商城业务订单编号
                'spuId' => $bmInfo['material_spu_id'] ?? '', // 商品大码
                'sku' => $bmInfo['material_bn'] ?? '', // 商品SKU信息
                'count' => intval($item['num']), // 订单购买商品数量
                'unitPrice' => floatval($item['price']), // 商品单价
                'mallId' => $storeInfo['org_no'] ?? '', // 小镇编码
            ];
            $orderItem['totalPrice'] = floatval(bcmul($item['num'], $item['price'], 2));
            $data['goodItems'][] = $orderItem;
        }

        # 错误信息
        if (!empty($error_list)) {
            $error_msg = implode(';', $error_list);
            return false;
        }

        $source_order_id = $sdf['refund_apply']['source_order_id'] ?? $sdf['refund_apply']['order_id'];
        $discount_fields = kernel::single(sprintf('erpapi_sap_mapping_platform_%s', erpapi_sap_func::getShopType($shop_type)))->getDiscountFields();
        # 计算订单支付信息中的oid金额总和
        $paymentsOidList = ['payment' => [], 'discount' => []];
        # 获取订单支付信息
        $payments = $this->_getSouceOrderPayments($source_order_id, array_keys($refundData['oid']));
        if (empty($payments)) {
            $error_msg = '获取原单支付明细失败';
            return false;
        }

        # 获取优惠券code
        foreach ($payments as $k => $payment) {
            # 使用带序号的子单号作为key处理
            $key = $refundData['oid'][$payment['eshopOrderSn']];
            if (empty($key)) {
                continue;
            }

            # 记录优惠券code
            if (!empty($payment['couponCode'])) {
                $refundData['coupon_id'][] = $payment['couponCode'];
            }
            # 计算订单支付明细金额之和
            $paymentsOidList['payment'][$key] = bcadd($paymentsOidList['payment'][$key], $payment['amount'], 2);
            # 优惠类型
            if (!empty($discount_fields) && in_array($payment['addon']['sub_type'], $discount_fields)) {
                $paymentsOidList['discount'][$key] = bcadd($paymentsOidList['discount'][$key], $payment['amount'], 2);
            }
            # 更新子订单号
            $payments[$k]['eshopOrderSn'] = $key;
            # 删除扩展字段
            unset($payments[$k]['addon']);
        }
        $data['paymentItems'] = $payments;

        # 更新订单明细中的金额
        foreach ($data['orderItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['payAmount'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['orderItems'][$k]['orderPrice'] = $data['orderItems'][$k]['payAmount'];
            }
            if (!empty($paymentsOidList['discount'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['discAmount'] = floatval($paymentsOidList['discount'][$item['eshopOrderSn']]);
            }
            # 记录订单总实付金额
            $total_pay_amount = bcadd($total_pay_amount, $data['orderItems'][$k]['payAmount'], 2);
        }
        # 更新sku明细中的支付金额
        foreach ($data['goodItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['goodItems'][$k]['totalPrice'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['goodItems'][$k]['unitPrice'] = floatval(bcdiv($data['goodItems'][$k]['totalPrice'], $item['count'], 2));
            }
        }

        # 获取订单优惠券信息
        $coupons = $this->_getSourceOrderCoupons($source_order_id, $shop_type, $refundData['coupon_id']);
        if (!empty($coupons)) {
            $data['couponItems'] = $coupons;
        }

        # 保存中间表的明细
        $this->_saveSapItemsData($data, $sdf['ext']['sap_data']);

        # 删除扩展字段
        foreach ($data['couponItems'] as $k => $item) {
            unset($data['couponItems'][$k]['eshopOrderSn']);
        }

        # 验证订单是否推送
        $order_pushed = $this->check_push_order($source_order_id);
        if (!$order_pushed) {
            $error_msg = '原始支付订单未成功推送';
            return false;
        }

        $params = [
            'appId' => $this->__app_id,
            'timestamp' => erpapi_sap_func::mixtimestamp(true),
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            //'oid_mapping' => $oid_mapping,
            'total_pay_amount' => $total_pay_amount,
        ];
        return $params;
    }

    /**
     * 保存到sap中间数据表 - 主数据
     * @param $sdf
     * @return mixed
     */
    protected function saveSapMainData($sdf, $bill_type)
    {
        $orderMdl = app::get('sales')->model('sap_orders');

        $order_data = [
            'bill_id' => $sdf['refund_apply']['apply_id'],
            'bill_no' => $sdf['refund_apply']['refund_apply_bn'],
            'bill_type' => $bill_type,
            'shop_id' => $sdf['refund_apply']['shop_id'],
            'shop_type' => $sdf['refund_apply']['shop_type'],
            'create_time' => time()
        ];
        # 来源订单ID
        if (!empty($sdf['refund_apply']['source_order_id'])) {
            $order_data['source_bill_id'] = implode(',', $sdf['refund_apply']['source_order_id']);
        } else {
            $order_data['source_bill_id'] = $sdf['refund_apply']['order_id'];
        }
        # 历史退款单无需推送
        if ($this->is_history_order($sdf['refund_apply'])) {
            $order_data['sync_status'] = 'none';
        }
        $result = $orderMdl->save($order_data);
        if (!$result) {
            return false;
        }
        return $order_data;
    }

    /**
     * 数据校验
     * @param $params
     * @param $error_msg
     * @return bool
     */
    protected function _check_params($params, &$error_msg)
    {
        if (isset($params['extend']) && $params['extend']['is_history']) {
            return true;
        }

        # 检查参数
        $result = parent::_check_params($params, $new_msg);
        if (!$result) {
            $error_msg = $new_msg;
            return false;
        }

        return $this->_check_refund_params($params, $error_msg);
    }

    public function onlyPush($sap_id, $sdf)
    {
        $this->__original_bn = $sdf['orderItems'][0]['oriFlowNo'];
        $title = '订单退款推送SAP接口';
        # 请求接口
        $callback = array();
        $count = 0;
        $current_time = time();
        $oOperation_log = app::get('ome')->model('operation_log');

        do {
            $response = $this->__caller->call(SAP_PUSH_SALES, $sdf, $callback, $title, 30, $this->__original_bn, true);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['msg'])) {
                break;
            }
            $count++;
        } while ($count < 3);

        # 记录推送状态
        parent::_log_sync_status($sap_id, $current_time, $response);
        # 记录操作日志
        $memo = $title . '：' . $response['rsp'];
        if ($response['rsp'] == 'fail') {
            $memo .= '，原因：' . $response['msg'];
        }
        $oOperation_log->write_log('sap_refund@ome', $sap_id, $memo);
        return $response;
    }
}