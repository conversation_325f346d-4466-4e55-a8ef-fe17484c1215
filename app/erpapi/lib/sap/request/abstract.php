<?php

/**
 * ABSTRACT
 */
abstract class erpapi_sap_request_abstract
{
    /**
     * 渠道
     *
     * @var string
     **/
    protected $__channelObj;

    protected $__resultObj;

    protected $__original_bn = null;
    protected $__app_id = null;
    protected $__bmInfo = null;
    protected $__caller = null;
    protected $__esb_store = [];

    /**
     * 推送时间限制，只推送此日期之后的正向订单
     * @var string
     */
    protected $__push_time = '';

    /**
     * 推送sap类型
     * @var string
     */
    protected $__push_type = '';

    protected $__platform_cls = 'erpapi_sap_mapping_platform';

    final public function init(erpapi_channel_abstract $channel, erpapi_config $config, erpapi_result $result)
    {
        $this->__channelObj = $channel;

        $this->__resultObj = $result;

        // 默认以JSON格式返回
        $this->__caller = kernel::single('erpapi_caller', array('uniqid' => uniqid('sap')))
            ->set_config($config)
            ->set_result($result);
    }

    /**
     * 成功输出
     *
     * @return array
     * <AUTHOR>
    final public function succ($msg = '', $msgcode = '', $data = null)
    {
        return array('rsp' => 'succ', 'msg' => $msg, 'code' => $msgcode, 'data' => $data);
    }

    /**
     * 失败输出
     *
     * @return invoice
     * <AUTHOR>
    final public function error($msg, $msgcode = '', $data = null)
    {
        return array('rsp' => 'fail', 'msg' => $msg, 'err_msg' => $msg, 'code' => $msgcode, 'data' => $data);
    }

    /**
     * 生成唯一键
     *
     * @return string
     * <AUTHOR>
    final public function uniqid()
    {
        $microtime = utils::microtime();
        $unique_key = str_replace('.', '', strval($microtime));
        $randval = uniqid('', true);
        $unique_key .= strval($randval);
        return md5($unique_key);
    }

    /**
     * 回调
     * @param $response Array
     * @param $callback_params Array
     * @return array
     **/
    public function callback($response, $callback_params)
    {
        return $response;
    }

    /**
     * 保存sap明细数据到中间表
     * @return void
     */
    protected function _saveSapItemsData($params, $order_data)
    {
        $orderItemsMdl = app::get('sales')->model('sap_order_items');
        $goodItemsMdl = app::get('sales')->model('sap_good_items');
        $paymentsMdl = app::get('sales')->model('sap_payments');
        $couponsMdl = app::get('sales')->model('sap_coupons');

        # 单据ID
        $bill_id = $order_data['bill_id'];
        # 先删除数据
        $del_filter = [
            'sap_id' => $order_data['sap_id']
        ];
        $orderItemsMdl->delete($del_filter);
        $goodItemsMdl->delete($del_filter);
        $paymentsMdl->delete($del_filter);
        $couponsMdl->delete($del_filter);

        foreach ($params['orderItems'] as $item) {
            $item['order_id'] = $bill_id;
            $item['sap_id'] = $order_data['sap_id'];
            # 保存订单明细
            $orderItemsMdl->save($item);
        }

        foreach ($params['goodItems'] as $item) {
            $item['order_id'] = $bill_id;
            $item['sap_id'] = $order_data['sap_id'];
            # 保存sku明细
            $goodItemsMdl->save($item);
        }

        if (!empty($params['paymentItems'])) {
            foreach ($params['paymentItems'] as $item) {
                $item['sap_id'] = $order_data['sap_id'];
                $item['order_id'] = $bill_id;
                $item['addon'] = serialize($item['addon']);
                # 保存支付明细
                $paymentsMdl->save($item);
            }
        }

        if (!empty($params['couponItems'])) {
            foreach ($params['couponItems'] as $item) {
                $item['sap_id'] = $order_data['sap_id'];
                $item['order_id'] = $bill_id;
                # 保存优惠券明细
                $couponsMdl->save($item);
            }
        }

        return true;
    }

    /**
     * 记录同步状态
     * @param $time
     * @param $response
     * @return void
     */
    protected function _log_sync_status($sap_id, $time, $response)
    {
        if (empty($sap_id)) {
            return false;
        }

        $ordersMdl = app::get('sales')->model('sap_orders');

        # 日志
        $log_data = [
            'sync_status' => $response['rsp'],
            'sync_time' => $time,
            'sync_msg' => null
        ];
        if ($response['rsp'] == 'fail') {
            $log_data['sync_msg'] = $response['msg'];
        }
        $result = $ordersMdl->update($log_data, array('sap_id' => $sap_id));
        return $result;
    }

    /**
     * 获取orderSource映射关系
     * @param $shop_type
     * @return mixed
     */
    protected function getOrderSource($shop_type, $extend = [])
    {
        if (empty($shop_type)) {
            return '';
        }

        $objCls = sprintf('%s_%s', $this->__platform_cls, erpapi_sap_func::getShopType($shop_type));
        $result = kernel::single($objCls)->getOrderSource($extend);
        if (empty($result)) {
            return '';
        }
        return $result;
    }

    /**
     * 获取orderType映射关系
     * @param $shop_type
     * @return mixed
     */
    protected function getOrderType($shop_type, $type)
    {
        if (empty($shop_type)) {
            return '';
        }

        $objCls = sprintf('%s_%s', $this->__platform_cls, erpapi_sap_func::getShopType($shop_type));
        $result = kernel::single($objCls)->getOrderType($type);
        if (empty($result)) {
            return '';
        }
        return $result;
    }

    /**
     * 获取appId映射关系
     * @param $shop_type
     * @return mixed
     */
    protected function getAppId($shop_type)
    {
        if (empty($shop_type)) {
            return '';
        }

        $objCls = sprintf('%s_%s', $this->__platform_cls, erpapi_sap_func::getShopType($shop_type));
        $result = kernel::single($objCls)->getAppId();
        if (empty($result)) {
            return '';
        }
        return $result;
    }

    /**
     * 运费分摊到订单明细
     * @param $objects
     * @param $cost_freight
     * @return mixed|void
     */
    protected function _split_cost_freight_items($objects, $cost_freight)
    {
        if (floatval($cost_freight) <= 0) {
            return $objects;
        }

        # 分摊总金额
        $total_amount = 0;
        $new_objects = [];
        foreach ($objects as $object) {
            # 过滤0金额明细
            if (floatval($object['divide_order_fee']) <= 0) {
                continue;
            }
            $total_amount = bcadd($total_amount, $object['divide_order_fee'], 2);
            # 保存新的分摊数据
            $new_objects[] = $object;
        }

        # 分摊比例
        $split_rate = bcdiv($cost_freight, $total_amount, 6);
        # 明细条数
        $item_count = count($new_objects);
        $last_amount = $cost_freight;
        foreach ($new_objects as $k => $object) {
            if ($k + 1 == $item_count) {
                $new_objects[$k]['cost_freight'] = $last_amount;
            } else {
                $cost_freight = bcmul($object['divide_order_fee'], $split_rate, 2);
                $new_objects[$k]['cost_freight'] = $cost_freight;
                $last_amount = bcsub($last_amount, $cost_freight, 2);
            }
        }
        return $new_objects;
    }

    /**
     * 获取基础物料所属门店和运营组织
     * @param $bn
     * @param $is_get_org
     * @return mixed
     */
    protected function _getBMStoreAndOrg($bn, $is_get_org = false)
    {
        if (empty($bn)) {
            return [];
        }

        if (empty($this->__bmInfo[$bn])) {
            $basicMaterialMdl = app::get('material')->model('basic_material');
            $this->__bmInfo[$bn] = $basicMaterialMdl->getMStoreAndOrg($bn, $is_get_org);
        }

        return $this->__bmInfo[$bn];
    }

    /**
     * 获取基础物料
     * @param $bn
     * @return mixed
     */
    protected function getMaterial($bn, $field = '*')
    {
        if (empty($bn)) {
            return [];
        }

        $basicMaterialMdl = app::get('material')->model('basic_material');
        $material = $basicMaterialMdl->dump(['material_bn' => $bn], $field);
        return $material;
    }

    /**
     * 获取原单的支付单信息
     * @param $order_id
     * @return mixed
     */
    protected function _getSouceOrderPayments($order_ids = [], $oids = [])
    {
        $order_ids = is_array($order_ids) ? $order_ids : explode(',', $order_ids);
        if (empty($order_ids)) {
            return false;
        }

        # 原始支付单支付信息
        $payments = $this->_getSourceOrderPayedList($order_ids, $oids);
        if (empty($payments)) {
            return false;
        }

        # 返回数据
        $result = [];
        foreach ($payments as $item) {
            $payment = [
                'eshopOrderSn' => $item['eshopOrderSn'], // 业务订单编号
                'paymentType' => $item['paymentType'], // 金蝶支付方式名称(DP支付名称)
                'paymentCode' => $item['paymentCode'], // 金蝶支付方式编码，包含平台补贴(金蝶支付编码)
                'originPaymentType' => $item['originPaymentType'], // 原始支付方式名称
                'originPaymentCode' => $item['originPaymentCode'], // 原始支付方式编码
                'amount' => floatval($item['amount']), // 支付金额
                'subsidyAmount' => floatval($item['subsidyAmount']), // 平台补贴金额
                'addon' => []
            ];
            # 券号
            if (!empty($item['couponCode'])) {
                $payment['couponCode'] = $item['couponCode'];
            }
            # 扩展参数
            if (!empty($item['addon'])) {
                $payment['addon'] = unserialize($item['addon']);
            }
            $result[] = $payment;
        }
        return $result;
    }

    /**
     * 读取原始支付单的明细
     * @param $order_ids
     * @param $oids
     * @return array|false
     */
    protected function _getSourceOrderPayedList($order_ids, $oids = [])
    {
        $order_ids = is_array($order_ids) ? $order_ids : explode(',', $order_ids);
        if (empty($order_ids)) {
            return false;
        }

        # 查询SQL
        $sql = "SELECT b.* FROM sdb_sales_sap_orders a LEFT JOIN sdb_sales_sap_payments b ON a.sap_id=b.sap_id"
            . " WHERE a.bill_type = 'payed'";
        if (count($order_ids) > 1) {
            $sql .= " AND a.bill_id IN (" . implode(',', $order_ids) . ")";
        } else {
            $sql .= " AND a.bill_id = " . current($order_ids);
        }
        if (!empty($oids)) {
            $oids = is_array($oids) ? $oids : explode(',', $oids);
            if (count($oids) > 1) {
                $sql .= " AND b.eshopOrderSn IN ('" . implode("','", $oids) . "')";
            } else {
                $sql .= " AND b.eshopOrderSn = '" . current($oids) . "'";
            }
        }
        return kernel::database()->select($sql);
    }

    /**
     * 获取某个平台支付方式编码
     * @param $shop_type
     * @return mixed
     */
    public function _getPlatformPaymentCodeList($shop_type)
    {
        if (empty($shop_type)) {
            return [];
        }

        # 重新映射平台编码
        $shop_type = erpapi_sap_func::getShopType($shop_type);
        # 读取平台配置
        $objCls = sprintf('erpapi_sap_mapping_platform_%s', $shop_type);
        $config = kernel::single($objCls)->getPaymentsCfg();
        if (empty($config)) {
            return [];
        }

        # 读取支付方式对应的code
        $result = [];
        foreach ($config as $item) {
            $result[] = end($item);
        }
        return array_unique($result);
    }

    protected function _getSourceOrderPushDiscountTotal($shop_type, $order_ids = [], $oids = [])
    {
        $order_ids = is_array($order_ids) ? $order_ids : explode(',', $order_ids);
        if (empty($order_ids)) {
            return false;
        }
    }

    /**
     * 读取原始支付单的优惠明细
     * @param $order_id
     * @param $oid
     * @return mixed
     */
    public function _getSouceOrderDiscountList($shop_id, $shop_type, $order_id)
    {
        if (empty($shop_id) || empty($order_id)) {
            return false;
        }

        $result = ['payed' => [], 'all_item_id' => []];
        # 查询支付单列表
        $sql = "SELECT b.* FROM sdb_sales_sap_orders a LEFT JOIN sdb_sales_sap_payments b ON a.sap_id=b.sap_id"
            . " WHERE a.shop_id = '{$shop_id}' AND a.source_bill_id = '{$order_id}' AND a.bill_type = 'payed'";
        $paymentList = kernel::database()->select($sql);
        if (empty($paymentList)) {
            return false;
        }

        foreach ($paymentList as $item) {
            unset($item['sap_id']);
            $result['payed'][$item['eshopOrderSn']][] = $item;
            $result['all_item_id'][] = $item['item_id'];
        }
        return $result;
    }

    /**
     * 获取原始订单的优惠券信息
     * @param $order_ids
     * @return mixed
     */
    protected function _getSourceOrderCoupons($order_ids, $shop_type, $coupon_id = [], $is_oid = false)
    {
        if (empty($order_ids)) {
            return false;
        }

        $shop_type = erpapi_sap_func::getShopType($shop_type);

        # 是否oid字段读取优惠券了吧
        if ($is_oid) {
            $result = kernel::single(sprintf('ome_sap_data_platform_%s', $shop_type))->get_coupons($order_ids, $coupon_id);
        } else {
            $result = kernel::single(sprintf('ome_sap_data_platform_%s', $shop_type))->get_palateform_coupons($order_ids, $coupon_id);
        }
        return $result;
    }

    /**
     * 检查参数
     * @param $params
     * @param $error_msg
     * @return bool
     */
    protected function _check_params($params, &$new_msg)
    {
        if (empty($params)) {
            $new_msg = '参数转换失败';
            return false;
        }

        if (empty($params['orderItems'])) {
            $new_msg = '订单明细不能为空';
            return false;
        }

        if (empty($params['paymentItems'])) {
            $new_msg = '支付详情不能为空';
            return false;
        }

        $result = $oids = [];
        foreach ($params['orderItems'] as $item) {
            $oid = $item['eshopOrderSn'];
            $oids[] = $oid;
            $result[$oid]['order_amount'] = bcadd($result[$oid]['order_amount'], $item['orderPrice'], 2);
            $result[$oid]['pay_amount'] = bcadd($result[$oid]['pay_amount'], $item['payAmount'], 2);
            $result[$oid]['disc_amount'] = bcadd($result[$oid]['disc_amount'], $item['discAmount'], 2);
        }
        foreach ($params['paymentItems'] as $item) {
            $oid = $item['eshopOrderSn'];
            $oids[] = $oid;
            $result[$oid]['payments_amount'] = bcadd($result[$oid]['payments_amount'], $item['amount'], 2);
            #支付优惠
            if ($item['originPaymentCode'] == 'promotion_pay_amount') {
                $result[$oid]['promotion_pay_amount'] = bcadd($result[$oid]['promotion_pay_amount'], $item['amount'], 2);
            }
        }

        # 支付订单获取原始订单明细
        if (isset($params['extend']['push_type']) && $params['extend']['push_type'] == 'payed') {
            $objects_data = [];
            $objectsList = app::get('ome')->model('order_objects')->getList('order_id,oid,part_mjz_discount,amount', array('order_id' => $params['extend']['order_id']));
            if (!empty($objectsList)) {
                $class_name = sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($params['extend']['shop_type']));
                foreach ($objectsList as $item) {
                    # 重新获取平台子单号
                    $oid = kernel::single($class_name)->getOid($item['order_id'], $item['oid']);
                    $item['oid'] = $oid;
                    $objects_data[$oid] = $item;
                }
            }
        }

        # oid去重
        $oids = array_unique($oids);
        foreach ($oids as $oid) {
            if (bccomp($result[$oid]['order_amount'], $result[$oid]['pay_amount'], 2) != 0) {
                $new_msg = '订单信息里子单号[' . $oid . ']的订单金额不等于订单信息里的支付金额';
                return false;
            }
            if (bccomp($result[$oid]['pay_amount'], $result[$oid]['payments_amount'], 2) != 0) {
                $new_msg = '订单信息里的子单号[' . $oid . ']支付金额[' . $result[$oid]['pay_amount'] . ']不等于支付明细里的支付金额[' . $result[$oid]['payments_amount'] . ']';
                return false;
            }

            # 校验原始订单明细
            if (!empty($objects_data) && $params['extend']['shop_type'] != 'wxshipin') {
                $part_mjz_discount = isset($objects_data[$oid]) ? $objects_data[$oid]['part_mjz_discount'] : 0;
                $amount = isset($objects_data[$oid]) ? $objects_data[$oid]['amount'] : 0;

                if (bccomp($result[$oid]['pay_amount'], $amount, 2) != 0) {
                    $new_msg = '订单信息里子单号[' . $oid . ']的实际付款金额金额[' . $result[$oid]['pay_amount'] . ']不等于原始订单明细里的销售金额[' . $amount . ']';
                    return false;
                }
                $disc_amount = bcsub($result[$oid]['disc_amount'], $result[$oid]['promotion_pay_amount'] ?? 0, 2);
                if (bccomp($disc_amount, $part_mjz_discount, 2) != 0) {
                    $new_msg = '订单信息里子单号[' . $oid . ']的折扣金额金额金额[' . $result[$oid]['disc_amount'] . ']不等于原始订单明细里的优惠金额[' . $part_mjz_discount . ']';
                    return false;
                }
                if (bccomp($result[$oid]['payments_amount'], $amount, 2) != 0) {
                    $new_msg = '订单支付信息里子单号[' . $oid . ']的支付金额金额[' . $result[$oid]['payments_amount'] . ']不等于原始订单明细里的销售金额[' . $amount . ']';
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查是否推送sap
     * @return string
     */
    protected function check_push_sap()
    {
        # 检查是否推送ESB，默认开启
        if (defined('ESB_PUSH') && constant('ESB_PUSH')) {
            $is_push_esb = constant('ESB_PUSH');
        } else {
            $is_push_esb = 'ON';
        }
        return strtoupper($is_push_esb);
    }

    /**
     * 检查支付订单是否成功推送esb
     * @param $order_id
     * @return bool
     */
    protected function check_push_order($order_id)
    {
        if (empty($order_id)) {
            return false;
        }

        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $orderMdl = app::get('ome')->model('orders');
        # 判断原始订单时存在历史订单
        $orderList = $orderMdl->getList('order_id', array('order_id' => $order_id, 'is_history' => 'true'));
        if (!empty($orderList)) {
            return true;
        }

        $filter = [
            'bill_id' => $order_id,
            'bill_type' => 'payed',
        ];
        $sapOrder = $sapOrderMdl->getList('bill_id,sync_status', $filter);
        if (empty($sapOrder)) {
            return false;
        }
        $sap_status = array_unique(array_column($sapOrder, 'sync_status'));
        if (count($sap_status) > 1) {
            return false;
        }
        return current($sap_status) == 'succ';
    }

    /**
     * 获取某个订单的原始订单的商品金额
     * @param $order_id
     * @param $source_order_id
     * @return int|mixed
     */
    protected function getSourceOrderCostItem($order_id, $source_order_id)
    {
        if (empty($order_id)) {
            return 0;
        }

        $orderMdl = app::get('ome')->model('orders');

        # 获取原始订单的cost_item金额
        $orderList = $orderMdl->getList('order_id,order_bn,cost_item,init_cost_item', array('order_id' => $source_order_id));
        if (!empty($orderList)) {
            $sourceOrderData = array_column($orderList, null, 'order_id');
        }
        if (empty($sourceOrderData)) {
            return 0;
        }
        # 如果是换货单，取原始订单ID
        if (!empty($source_order_id)) {
            $order_id = is_array($source_order_id) ? current($source_order_id) : $source_order_id;
        }
        return $sourceOrderData[$order_id]['init_cost_item'] ?? $sourceOrderData[$order_id]['cost_item'];
    }

    /**
     * 获取哦某个订单的原始订单oid金额列表
     * @param $source_order_id
     * @return mixed
     */
    protected function getSourceOrderObjects($source_order_id, $shop_type)
    {
        if (empty($source_order_id) || empty($shop_type)) {
            return [];
        }

        # 获取订单明细
        $orderObjectMdl = app::get('ome')->model('order_objects');
        $objectsList = $orderObjectMdl->getList('order_id,oid,amount', array('order_id' => $source_order_id));
        if (empty($objectsList)) {
            return [];
        }

        $class_name = sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($shop_type));
        $result = [];
        foreach ($objectsList as $item) {
            # 重新获取平台子单号
            $oid = kernel::single($class_name)->getOid($item['order_id'], $item['oid']);
            $item['oid'] = $oid;
            $result[$oid] = $item;
        }
        return $result;
    }

    /**
     * 防并发处理
     * @param $push_type
     * @param $bill_id
     * @return bool
     */
    protected function check_push_time($push_type, $bill_id)
    {
        if (empty($bill_id)) {
            return true;
        }

        $key = sprintf('%s_%d', $push_type, $bill_id);
        base_kvstore::instance('esb/push/check')->fetch($key, $lasttime);
        if (!empty($lasttime) && time() - $lasttime < 30) {
            return true;
        }

        # 缓存只保存30秒
        base_kvstore::instance('esb/push/check')->store($key, time(), 40);
        return false;
    }

    /**
     * 检查是否历史订单
     * @param $bill
     * @return bool
     */
    protected function is_history_order($bill): bool
    {
        if (!isset($bill['is_history'])) {
            return false;
        }

        return $bill['is_history'] == 'true';
    }

    /**
     * 数据校验
     * @param $params
     * @param $error_msg
     * @return bool
     */
    protected function _check_refund_params($params, &$error_msg)
    {
        $order_id = $params['extend']['order_id'];
        $shop_id = $params['extend']['shop_id'];
        # 获取原始订单的cost_item金额
        $source_cost_item = $this->getSourceOrderCostItem($order_id, $params['extend']['source_order_id']);

        $result = ['total_amount' => 0, 'oid_list' => []];
        # 获取当前订单的所有退款申请单
        $apply_filter = [
            'order_id' => $order_id,
            'status|noequal' => '3'
        ];
        $applyList = app::get('ome')->model('refund_apply')->getList('apply_id,reship_id', $apply_filter);
        if (!empty($applyList)) {
            $apply_ids = [];
            foreach ($applyList as $apply) {
                $apply_ids[] = $apply['apply_id'];
                # 如果存在退货单，记录退货单ID
                if (!empty($apply['reship_id'])) {
                    $apply_ids[] = $apply['reship_id'];
                }
            }
            $apply_ids = array_unique($apply_ids);
            # 查询esb所有退款单记录
            $sql = "select b.oriFlowNo,SUM(b.payAmount) AS payAmount from sdb_sales_sap_orders a"
                . " left join sdb_sales_sap_order_items b on a.sap_id =b.sap_id"
                . " WHERE a.bill_type in ('refund','return') and a.bill_id in (" . implode(',', $apply_ids) . ")"
                . " AND a.shop_id = '{$shop_id}'";
            if (is_array($params['extend']['source_order_id'])) {
                $sql .= " AND a.source_bill_id in (" . implode(',', $params['extend']['source_order_id']) . ")";
            } else {
                $sql .= " AND a.source_bill_id = {$params['extend']['source_order_id']}";
            }
            $sql .= " GROUP BY b.oriFlowNo";
            $refundList = kernel::database()->select($sql);
            # 获取esb总的退款金额
            if (!empty($refundList)) {
                $result['total_amount'] = array_sum(array_column($refundList, 'payAmount'));
                $result['oid_list'] = $refundList;
            }
        }
        if (empty($result['total_amount'])) {
            $error_msg = '订单明细不能为空';
            return false;
        }

        # 所有推送 ESB 的退款单实付金额之和不要超过原始订单的总商品金额
        if ($this->__push_type != 'return' && bccomp($result['total_amount'], $source_cost_item, 2) > 0) {
            $error_msg = 'ESB退款单总实付金额[' . $result['total_amount'] . ']不能大于原始订单的总商品金额[' . $source_cost_item . ']';
            return false;
        }

        # 获取原始订单中oid金额明细
        $source_order_objects = $this->getSourceOrderObjects($params['extend']['source_order_id'], $params['extend']['shop_type']);
        # 根据退款单的子单号匹配订单的商品金额（amount）
        foreach ($result['oid_list'] as $item) {
            $oid = $item['oriFlowNo'];
            # 订单oid的金额
            $order_amount = $source_order_objects[$oid]['amount'] ?? 0;
            # 当前推送的oid的退款实付金额之和不能大于订单原明细的 amount
            if (bccomp($item['payAmount'], $order_amount, 2) > 0) {
                $error_msg = 'ESB退款单子商品的实付金额[' . $item['payAmount'] . ']不能大于原始订单明细的子商品[oid=' . $oid . ']商品金额[' . $order_amount . ']';
                return false;
            }
        }

        return true;
    }

    protected function _check_delivery_params($params, &$error_msg)
    {
        $dlyMdl = app::get('ome')->model('delivery');

        foreach ($params['extend']['orders'] as $order) {
            # 获取当前订单所有退货单id
            $delivery_ids = $dlyMdl->getDeliverIdByOrderId($order['order']['order_id']);
            if (empty($delivery_ids)) {
                $error_msg = '通过订单号[' . $order['order']['order_bn'] . ']查询发货单信息为空';
                return false;
            }

            $result = ['total_amount' => 0, 'oid_list' => []];
            # 查询所有发货单记录
            $sql = "SELECT b.eshopOrderSn,SUM(b.payAmount) AS payAmount FROM sdb_sales_sap_orders a"
                . " LEFT JOIN sdb_sales_sap_order_items b ON a.sap_id = b.sap_id"
                . " WHERE a.bill_type = 'delivery' AND a.bill_id in (" . implode(',', $delivery_ids) . ")"
                . " GROUP BY b.eshopOrderSn";
            $deliveryList = kernel::database()->select($sql);
            # 获取esb总的退款金额
            if (!empty($deliveryList)) {
                # 商城需要增加其他过滤的优惠金额，用于校验金额
                if ($params['extend']['shop_type'] == 'ecos.ecshopx' && !empty($params['extend']['discount_list'])) {
                    foreach ($deliveryList as $key => $item) {
                        $oid = $item['eshopOrderSn'];
                        $amount = $params['extend']['discount_list'][$oid]['amount'] ?? 0;
                        $deliveryList[$key]['payAmount'] = bcadd($item['payAmount'], $amount, 2);
                    }
                }
                $result['total_amount'] = array_sum(array_column($deliveryList, 'payAmount'));
                $result['oid_list'] = $deliveryList;
            }

            if (bccomp($result['total_amount'], $order['order']['cost_item'], 2) > 0) {
                $error_msg = 'ESB发货单总实付金额[' . $result['total_amount'] . ']与订单的物料总额[' . $order['order']['cost_item'] . ']不匹配';
                return false;
            }

            # 获取原始订单中oid金额明细
            $source_order_objects = $this->getSourceOrderObjects($order['order']['order_id'], $order['order']['shop_type']);
            # 根据发货单的子单号匹配订单的商品金额（amount）
            foreach ($result['oid_list'] as $item) {
                $oid = $item['eshopOrderSn'];
                # 订单oid的金额
                $order_amount = $source_order_objects[$oid]['amount'] ?? 0;
                # 当前推送的oid的退款实付金额之和不能大于订单原明细的 amount
                if (bccomp($item['payAmount'], $order_amount, 2) != 0) {
                    $error_msg = 'ESB发货单子商品的实付金额[' . $item['payAmount'] . ']与原始订单明细[oid=' . $oid . ']的商品金额[' . $order_amount . ']不匹配';
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取基础物料和门店编码
     * @param $order_id
     * @param $bn
     * @return mixed
     */
    protected function _getOrderBasicMaterial($order_id, $is_format = false)
    {
        if (empty($order_id)) {
            return [];
        }

        # 基础物料信息
        $sql = "select a.obj_id,a.bn AS material_bn,b.material_name,b.material_spu_id,a.fulfillment_store_id from sdb_ome_order_items a"
            . " LEFT JOIN sdb_material_basic_material b ON a.bn = b.material_bn"
            . " WHERE a.order_id = {$order_id}";
        $itemList = kernel::database()->select($sql);
        if (empty($itemList)) {
            return [];
        }

        $storeMdl = app::get('o2o')->model('store');
        $result = ['branch_store_id' => [], 'normal_store_id' => [], 'all_store_list' => []];

        # 获取门店ID
        $storeIds = array_unique(array_column($itemList, 'fulfillment_store_id'));
        # 获取门店信息
        $storeList = $storeMdl->getList('store_id,store_bn,delivery_type', ['store_id' => $storeIds]);
        foreach ($storeList as $store) {
            if ($store['delivery_type'] == 'branch') {
                $result['branch_store_id'][$store['store_bn']] = $store['store_id'];
            } else {
                $result['normal_store_id'][] = $store['store_id'];
            }
        }

        # 线下门店发货模式
        if (!empty($result['normal_store_id'])) {
            # 获取门店编码和运营组织编码
            $sql = "SELECT a.store_id,a.store_bn,a.name,a.branch_id,a.delivery_type,b.org_no FROM sdb_o2o_store a"
                . " LEFT JOIN sdb_organization_organization b ON a.org_id = b.org_id"
                . " WHERE a.store_id IN (" . implode(',', $result['normal_store_id']) . ")";
            $storeList = kernel::database()->select($sql);
            if (!empty($storeList)) {
                $result['all_store_list'] = array_merge($result['all_store_list'], $storeList);
            }
        }

        # 云仓门店发货模式
        if (!empty($result['branch_store_id'])) {
            # 获取云仓门店编码
            $branch_store_bn = array_keys($result['branch_store_id']);
            # 获取云仓门店信息
            $sql = "SELECT a.store_id,a.store_bn,a.name,a.branch_id,a.delivery_type,a.wms_store_code,b.org_no FROM sdb_o2o_store a"
                . " LEFT JOIN sdb_organization_organization b ON a.org_id = b.org_id"
                . " WHERE a.wms_store_code IN ('" . implode("','", $branch_store_bn) . "')";
            $storeList = kernel::database()->select($sql);
            if (!empty($storeList)) {
                $wms_store_code = [];
                # 记录历史云仓门店的ID
                foreach ($storeList as $key => $store) {
                    # 如果云仓门店编码重复，则不记录
                    if (!empty($wms_store_code) && in_array($store['wms_store_code'], $wms_store_code)) {
                        unset($storeList[$key]);
                        continue;
                    }
                    # 记录云仓门店编码
                    $wms_store_code[] = $store['wms_store_code'];
                    # 记录线下门店ID
                    $storeList[$key]['new_store_id'] = $store['store_id'];
                    # 记录云仓门店ID
                    $storeList[$key]['store_id'] = $result['branch_store_id'][$store['wms_store_code']];
                }
                $result['all_store_list'] = array_merge($result['all_store_list'], $storeList);
            }
        }

        # 合并数据
        if (!empty($result['all_store_list'])) {
            $storeList = array_column($result['all_store_list'], null, 'store_id');
            foreach ($itemList as $key => $item) {
                if (empty($storeList[$item['fulfillment_store_id']])) {
                    continue;
                }
                # 门店信息
                $storeInfo = $storeList[$item['fulfillment_store_id']];
                $itemList[$key]['store_bn'] = $storeInfo['store_bn'];
                $itemList[$key]['store_name'] = $storeInfo['name'];
                $itemList[$key]['branch_id'] = $storeInfo['branch_id'];
                $itemList[$key]['org_no'] = $storeInfo['org_no'];
            }
        }

        if (!$is_format) {
            return $itemList;
        }

        $result = [];
        # 格式化数据
        foreach ($itemList as $item) {
            $key = sprintf('%d_%s', $item['obj_id'], $item['material_bn']);
            $result[$key] = $item;
        }
        return $result;
    }

    /**
     * 通过 order_id 和 bn 获取订单明细门店信息
     * @param $order_id
     * @param $bn
     * @return array
     */
    protected function _getfulfillment_store($order_id, $bn)
    {
        if (empty($order_id) || empty($bn)) {
            return [];
        }

        //基础物料信息
        $sql = "select fulfillment_store_id from sdb_ome_order_items  WHERE order_id = {$order_id} and bn = '$bn' ";
        $item_info = kernel::database()->selectrow($sql);
        if (empty($item_info)) {
            return [];
        }

        $store_info = app::get('o2o')->model('store')->dump(['store_id' => $item_info['fulfillment_store_id']]);

        return $store_info;
    }

    /**
     * 通过订单的门店ID获取线下门店信息
     * @param $store_id
     * @return array
     */
    protected function getEsbStoreByOrder($order_id, $bn, $store_id = null)
    {
        if (empty($order_id) || empty($bn)) {
            return [];
        }

        $storeMdl = app::get('o2o')->model('store');
        $orgMdl = app::get('organization')->model('organization');

        # 如果门店ID不存在，则读取订单明细获取门店ID
        if (empty($store_id)) {
            $sql = "SELECT fulfillment_store_id FROM sdb_ome_order_items WHERE order_id = {$order_id} and bn = '{$bn}'";
            $order_items = kernel::database()->selectrow($sql);
            if (empty($order_items) || empty($order_items['fulfillment_store_id'])) {
                return [];
            }
            $store_id = $order_items['fulfillment_store_id'];
        }

        # 检查缓存门店信息是否存在，如果存在则直接返回
        if (!empty($this->__esb_store[$store_id])) {
            return $this->__esb_store[$store_id];
        }

        # 获取线下门店信息
        $store_info = $storeMdl->dump(['store_id' => $store_id], 'store_id,store_bn,delivery_type,org_id');
        if (empty($store_info)) {
            return [];
        }

        # 判断下线门店的发货模式，如果是云仓发货，则需要获取云仓门店信息
        if ($store_info['delivery_type'] == 'branch') {
            # 获取云仓门店信息
            $store_info = $storeMdl->dump(['wms_store_code' => $store_info['store_bn']], 'store_id,store_bn,delivery_type,org_id');
            if (empty($store_info)) {
                return [];
            }
        }

        # 获取运营组织信息
        $org_info = $orgMdl->dump(['org_id' => $store_info['org_id']], 'org_id,org_no');
        if (empty($org_info)) {
            return [];
        }

        $this->__esb_store[$store_id] = [
            'fulfillment_store_id' => $store_id,
            'store_id' => $store_info['store_id'],
            'store_bn' => $store_info['store_bn'],
            'delivery_type' => $store_info['delivery_type'],
            'org_no' => $org_info['org_no'],
        ];
        return $this->__esb_store[$store_id];
    }
}
