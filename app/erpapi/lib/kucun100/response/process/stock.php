<?php

class erpapi_kucun100_response_process_stock
{
    public function notify($sdf)
    {
        if (empty($sdf['stock'])) {
            return array('rsp' => 'fail', 'msg' => '库存信息不能为空');
        }

        # 加入队列处理
        $queueParams = array(
            'data' => array(
                'params' => json_encode($sdf['stock'], JSON_UNESCAPED_UNICODE),
                'log_id' => time(),
                'task_type' => 'updatekucun100stock',
            ),
            'url' => kernel::openapi_url('openapi.autotask', 'service'),
        );
        kernel::single('taskmgr_interface_connecter')->push($queueParams);
        return array('rsp' => 'succ');
    }
}