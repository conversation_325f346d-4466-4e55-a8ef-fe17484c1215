<?php
/**
 * User: jintao Date: 2016/7/6
 */
class erpapi_logistics_matrix_hqepay_config extends erpapi_logistics_matrix_config
{
    protected $_to_node_id = '1995170839';
    public function get_query_params($method, $params)
    {
        list($pay_type,$exp_type,)      = explode('|||', $this->__channelObj->channel['shop_id']);
        $query_params = array(
            'customer_pwd'  => '', #电子面单密码
            'pay_type'      => $pay_type, #邮费支付方式,1 现金 2 到付 3 第三方支付
            'exp_type'      => $exp_type, #快递类型
            'send_site'     => '', #网点
            'node_type'     => 'hqepay',
            'to_node_id'    => $this->_to_node_id,
        );

        // 查询绑定的快递鸟
        $channel = app::get('channel')->model('channel')->dump([
            'channel_type' => 'kuaidi',
            'node_type'    => 'kdn',
            'filter_sql'   => 'node_id IS NOT NULL AND node_id!=""',
        ]);

        $query_params['node_type']  = 'kdn';
        $query_params['to_node_id'] = $channel['node_id'];

        $pqp          = parent::get_query_params($method, $params);
        $query_params = array_merge($pqp, $query_params);
        return $query_params;
    }
}
