<?php

/**
 * 检查超过24小时客服未处理的非7天无理由退货退款
 * create by yzq
 * Class o2o_autotask_timer_twentyfourcheck
 */

class o2o_autotask_timer_twentyfourcheck{

    public function process($params, &$error_msg=''){
        set_time_limit(0);
        ignore_user_abort(1);
        $returnMdl = app::get("ome")->model("return_product");
        $storeMdl = app::get("o2o")->model("store");
        $shopMdl = app::get("ome")->model("shop");
        $check_time = time() - 86400;//24小时之前
        $filter = array(
            "belong_type" => 'customer',
            "twentyfour_hours_check" => '1',
            "status" => ['1','2'],
            "add_time|lthan" => $check_time,
        );
        $returnList = $returnMdl->getList("return_id,return_bn,belong_store_id,return_type,shop_id", $filter);
        $oOperation_log = app::get("ome")->model('operation_log');
        $raMdl = app::get("ome")->model("refund_apply");
        foreach ($returnList as $k => $v){
            //检查是否已生成退款申请单，有则不下放到门店，仍交给客服处理
            $raInfo = $raMdl->dump(array("refund_apply_bn" => $v['return_bn']), "apply_id,status");
            if(!empty($raInfo)){
                continue;
            }

            $data = array(
                'twentyfour_hours_check' => '2',
                'belong_type' => 'store',
            );
            $filter = array(
                'return_id' => $v['return_id']
            );
            $returnMdl->update($data, $filter);
            $oOperation_log->write_log('return@ome', $v['return_id'], "客服超过24小时未处理，下发至门店处理");
            if($v['belong_store_id']){
                $shopInfo = $shopMdl->dump($v['shop_id'], "auto_agree_return,shop_type");
                $storeInfo = $storeMdl->dump($v['belong_store_id'], "auto_agree_return");
                if (($shopInfo['shop_type'] == 'ecos.ecshopx' && $shopInfo['auto_agree_return'] == 'true') ||
                    ($shopInfo['shop_type'] != 'ecos.ecshopx' && ($shopInfo['auto_agree_return'] == 'true' || $storeInfo['auto_agree_return'] == 'true') )) {
                    $this->dealReship($v['return_id']);
                }
            }

        }
        return true;
    }

    /**
     * 处理生成退货单
     * @param $sdf
     * @return mixed
     */
    public function dealReship($return_id, $type = 'auto'){
        $returnMdl = app::get("ome")->model("return_product");
        $returnInfo = $returnMdl->dump($return_id, "return_type");
        $return_type = $returnInfo['return_type'];
        $operateLog = app::get('ome')->model('operation_log');
        $db = kernel::database();
        $status = '3';
        $upData = array(
            'last_modified' => time(),
            'store_check_status' => '1'
        );
        $title = '';
        if($type == 'auto'){
            $title = '自动';
        }
        $result = $returnMdl->update($upData, array('return_id' => $return_id, 'status|noequal' => $status));
        if(!$result){
            $operateLog->write_log('return@ome', $return_id, '门店'.$title.'审核失败，状态更新失败');
            return false;
        }
        $return_type_map = array(
            'return' => '1',
            'change' => '2',
        );
        $error_msg = '';
        $aData = array(
            'status' => $status,
            'choose_type' => $return_type_map[$return_type],
            'choose_type_flag' => '1',
            'return_id' => $return_id,
            'memo' => '门店同意售后申请',
        );
        $res = $returnMdl->tosave($aData, false, $error_msg);
        if(!$res){
            $operateLog->write_log('return@ome', $return_id, '门店'.$title.'审核失败，'.$error_msg);
        }else{
            $operateLog->write_log('return@ome', $return_id, '门店'.$title.'审核通过，生成退货单');
        }

        return $res;
    }

}