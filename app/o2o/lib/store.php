<?php

/**
 * 门店数据处理Lib类
 *
 * @access public
 * <AUTHOR>
 * @version $Id: import.php 2016-07-26 15:00
 */
class o2o_store
{
    /**
     * create_store 门店创建
     *
     * @param $sdf
     * @return mixed
     */
    function create_store($sdf, &$errmsg)
    {
        $storeObj = app::get('o2o')->model('store');
        $organizationObj = app::get('organization')->model('organization');
        $serverObj = app::get('o2o')->model('server');
        $regionObj = app::get('o2o')->model('store_regions');

        //$smsLib = kernel::single('taoexlib_request_sms');
        $channelLib = kernel::single('channel_interface_channel');
        $branchLib = kernel::single('ome_interface_branch');
        $shopLib = kernel::single('ome_interface_shop');
        $regionLib = kernel::single('o2o_store_regions');

        error_log('$sdf:'.json_encode($sdf,JSON_UNESCAPED_UNICODE).PHP_EOL,3,DATA_DIR.'/test_store.log');
        //开启事务
        $storeObj->db->exec('begin');

        //如果设定了门店服务端类型获取对应的虚拟仓储类型
        $now_wms_id = 0;
        $serverInfo = $serverObj->dump(array('type' => 'wap'), 'server_id,type');
        if ($serverInfo['type']) {
            $wms_type = $channelLib->dump(array('channel_type' => 'wms', 'node_type' => $serverInfo['type']), 'channel_id');
            $now_wms_id = $wms_type['channel_id'];
        }

        //门店
        $store_sdf = array(
            'store_bn' => $sdf['store_bn'],
            'name' => $sdf['store_name'],
            'server_id' => $serverInfo['server_id'],
            'org_id' => $sdf['org_id'],
            'addr' => $sdf['addr'],
            'area' => $sdf['area'],
            'contacter' => $sdf['contacter'],
            'mobile' => $sdf['mobile'],
            'tel' => $sdf['tel'],
            'status' => $sdf['status'],
            'machine_code' => $sdf['machine_code'],
            'open_hours' => $sdf['open_hours'],
            'source' => $sdf['source'],
            'store_mode' => $sdf['store_mode'],
            'performance_type' => $sdf['performance_type'],
            'auto_agree_return' => $sdf['auto_agree_return'],
            'is_receive_order' => $sdf['is_receive_order'],
            'receive_order_start_time' => strtotime($sdf['receive_order_start_time']),
        );
        # 门店资料存在就修改
        $storeInfo = $storeObj->dump(array('store_bn' => $sdf['store_bn']), 'store_id');
        if (!empty($storeInfo)) {
            $store_sdf['store_id'] = $storeInfo['store_id'];
        } else {
            $store_sdf['create_time'] = time();
        }
        $is_save = $storeObj->save($store_sdf);
        if (!$is_save) {
            #事务回滚
            $storeObj->db->rollBack();
            $errmsg .= '保存门店信息出错,门店编码:' . $store_sdf['store_bn'] . '。';
            return false;
        }

        //生成对应的虚拟仓
        $new_branch = array(
            'branch_bn' => $sdf['store_bn'],
            'name' => $sdf['store_name'],
            'storage_code' => $sdf['store_bn'],
            'b_type' => 2,
            'b_status' => $sdf['status'],
            'wms_id' => $now_wms_id,
        );
        # 仓库资料
        $branchList = $branchLib->getList('branch_id', array('branch_bn' => $sdf['store_bn']));
        if (!empty($branchList)) {
            $new_branch['branch_id'] = $branchList[0]['branch_id'];
        }
        $save_branch = $branchLib->save($new_branch);
        if (!$save_branch) {
            #事务回滚
            $storeObj->db->rollBack();
            $errmsg .= '门店关联虚拟仓保存失败,门店编码:' . $store_sdf['store_bn'] . '。';
            return false;
        }

        //生成对应的虚拟店铺
        $new_shop = array(
            'shop_bn' => $sdf['store_bn'],
            'name' => $sdf['store_name'],
            's_type' => 2,
            's_status' => $sdf['status'],
        );
        $shopList = $shopLib->getList('shop_id', array('shop_bn' => $sdf['store_bn']));
        if (!empty($shopList)) {
            $new_shop['shop_id'] = $shopList[0]['shop_id'];
        }
        $save_shop = $shopLib->save($new_shop);
        if (!$save_shop) {
            #事务回滚
            $storeObj->db->rollBack();
            $errmsg .= '门店关联线下店铺保存失败,门店编码:' . $store_sdf['store_bn'] . '。';
            return false;
        }

        //更新相关的仓库和店铺
        $update_data = array('branch_id' => $new_branch['branch_id'], 'shop_id' => $new_shop['shop_id']);
        $update_store = $storeObj->update($update_data, array('store_id' => $store_sdf['store_id']));
        if (!$update_store) {
            #事务回滚
            $storeObj->db->rollBack();
            $errmsg .= '门店关联仓库、店铺信息更新失败,门店编码:' . $store_sdf['store_bn'] . '。';
            return false;
        }

        #新增门店关联地区数据信息
        list(,, $region_id) = explode(':', $sdf['area']);
        $region_data = $regionLib->getRegionById($region_id);

        $save_data = array(
            'store_id' => $store_sdf['store_id'],
            'region_1' => intval($region_data[1]),
            'region_2' => intval($region_data[2]),
            'region_3' => intval($region_data[3]),
            'region_4' => intval($region_data[4]),
            'region_5' => intval($region_data[5]),
        );
        $save_region = $regionObj->save($save_data);
        if (!$save_region) {
            #事务回滚
            $storeObj->db->rollBack();
            $errmsg .= '门店关联地区信息保存失败,门店编码:' . $store_sdf['store_bn'] . '。';
            return false;
        }

        //如果最新保存的信息有父节点，更新父节点为有下级仓库或门店节点信息
        if ($sdf['parent_id'] > 0) {
            $p_org_info = $organizationObj->dump(array('org_id' => $sdf['parent_id']), 'haschild');

            $child_arr = $organizationObj->getList('org_id', array('parent_id' => $sdf['parent_id'], 'org_type' => 2), 0, -1);

            if (count($child_arr) > 0) {
                $org_save_parent_data['haschild'] = $p_org_info['haschild'] | 2;
            } else {
                $org_save_parent_data['haschild'] = $p_org_info['haschild'] ^ 2;
            }

            $organizationObj->update($org_save_parent_data, array('org_id' => $sdf['parent_id']));
        }

        //事务确认
        $storeObj->db->commit();

        return true;
    }

    /**
     * 门店数据有效性检查Lib类
     * @param unknown $params
     * @param unknown $err_msg
     * @return Array
     */
    public function checkAddParams(&$params, &$err_msg)
    {
        $organizationObj = app::get('organization')->model('organization');

        //固定电话与手机必填一项
        $gd_tel = str_replace(" ", "", $params['tel']);
        $mobile = str_replace(" ", "", $params['mobile']);
        if (!$gd_tel && !$mobile) {
            $err_msg = '固定电话与手机号码必需填写一项';
            return false;
        }

        $pattern = "/^400\d{7}$/";
        $pattern1 = "/^\d{1,4}-\d{7,8}(-\d{1,6})?$/i";
        if ($gd_tel) {
            $_rs = preg_match($pattern, $gd_tel);
            $_rs1 = preg_match($pattern1, $gd_tel);
            if ((!$_rs) && (!$_rs1)) {
                $err_msg = '请填写正确的固定电话号码';
                return false;
            }
        }

        $pattern2 = "/^\d{8,15}$/i";
        if ($mobile) {
            if (!preg_match($pattern2, $mobile)) {
                $err_msg = '请输入正确的手机号码';
                return false;
            }
            if ($mobile[0] == '0') {
                $err_msg = '手机号码前请不要加0';
                return false;
            }
        }
        return true;
    }

    //根据门店编码找到对应的仓库ID
    public function getBranchIdByStoreBn($store_bn)
    {
        $storeObj = app::get('o2o')->model('store');
        $storeInfo = $storeObj->getList('branch_id', array('store_bn' => $store_bn), 0, 1);
        if ($storeInfo) {
            return $storeInfo[0]['branch_id'];
        }
    }

    /**
     * 根据订单扩展表上的门店编码查找到门店信息
     *
     * @param intval $order_id 订单号
     * @return Array
     */
    public function getOrderIdByStore($order_id)
    {
        $o2o_order = array();
        $o2o_order['is_omnichannel'] = true;

        //指定门店
        $orderExtendObj = app::get('ome')->model('order_extend');
        $ordExtRow = $orderExtendObj->dump(array('order_id' => $order_id), 'order_id, store_dly_type, store_bn');

        $store_bn = $ordExtRow['store_bn'];
        $store_dly_type = $ordExtRow['store_dly_type'];
        $dly_corp_type = ($store_dly_type == 1 ? 'o2o_ship' : 'o2o_pickup');

        if ($store_dly_type && $store_bn) {
            //默认选择的门店物流公司
            $corpObj = app::get('ome')->model('dly_corp');
            $corpRow = $corpObj->dump(array('type' => $dly_corp_type, 'd_type' => 2), 'corp_id');

            $o2o_order['select_corp_id'] = $corpRow['corp_id'];

            //根据订单扩展表上的门店编码查找到门店信息
            $o2oStoreObj = app::get('o2o')->model('store');
            $storeRow = $o2oStoreObj->dump(array('store_bn' => $store_bn), 'store_id, name, branch_id, area');

            $o2o_order['store_bn'] = $store_bn;
            $o2o_order['branch_id'] = $storeRow['branch_id'];
            $o2o_order['store_area'] = $storeRow['area'];

            $store_msg = array(1 => '客户要求指定【{store_name}】门店进行配送', '客户要求指定到【{store_name}】门店自提');
            $o2o_order['store_recommend_msg'] = str_replace('{store_name}', $storeRow['name'], $store_msg[$store_dly_type]);
        }

        return $o2o_order;
    }

    /**
     * undocumented function
     *
     * @return void
     * <AUTHOR>
    public function create($post)
    {
        // 前后去空格
        foreach ($post as $key => $value) {
            if (is_string($value)) {
                $post[$key] = trim($value);
            }
        }


        $store_id = (int)$post['store_id'];

        // 更新企业结构
        $orgMdl = app::get('organization')->model('organization');
        $storeMdl = app::get('o2o')->model('store');

        $org = $orgMdl->dump(['org_no' => $post['store_bn']]);

        if (!$store_id && $org) {
            $msg = $org['org_type'] == '2' ? '门店编码已经存在' : '此编码被企业组织占用';

            return [false, $msg];
        }

        if ($post['delivery_type'] == 'branch') {
            $branchMdl = app::get('ome')->model('branch');
            $branch = $branchMdl->dump(['branch_bn' => $post['store_bn']]);
            if (empty($branch)) {
                return [false, '请先创建发货仓库，发货仓库编码需要和门店编码一致'];
            }
        }

        # 检查云仓门店编码
        if (!empty($post['wms_store_code'])) {
            $storeInfo = $storeMdl->dump(array('store_bn' => $post['wms_store_code']), 'store_id');
            if (empty($storeInfo)) {
                return [false, '云仓门店编码对应的门店信息不存在'];
            }

            # 检查云仓门店是否存在
            $storeInfo = $storeMdl->dump(['wms_store_code' => $post['wms_store_code']], 'store_id,store_bn,name');
            if (!empty($storeInfo) && !empty($store_id) && $storeInfo['store_id'] != $store_id) {
                return [false, '云仓门店编码已被其他门店【' . $storeInfo['store_bn'] . '/' . $storeInfo['name'] . '】占用'];
            }
        }

        # 校验地址格式  mainland:广东/佛山市/南海区:444
        $is_correct_region = kernel::single('ome_func')->is_correct_region($post['area']);
        if ($is_correct_region) {
            $area = $post['area'];
        } else {
            $tmp_area = [];
            $tmp_area[] = $post['province'];
            $tmp_area[] = $post['city'];
            $tmp_area[] = $post['area'];
            if (!empty($post['town'])) {
                $tmp_area[] = $post['town'];
            }
            $area = sprintf('mainland:%s:%d', implode('/', $tmp_area), mt_rand(100, 999));
            $post['area'] = $area;
        }

        $upOrgData = array(
            'org_id' => (int)$org['org_id'],
            'org_name' => $post["name"],
            'org_type' => 2,
            'org_no' => $post["store_bn"],
            'status' => $post['status'],
            'area' => $area,
            'org_parents_structure' => $post['org_parents_structure'],
        );

        list(,, $upOrgData['parent_id']) = explode(':', $post['org_parents_structure']);

        $pOrg = $orgMdl->dump($upOrgData['parent_id'], 'org_level_num');

        $upOrgData['org_level_num'] = $pOrg['org_level_num'] + 1;

        if (!$orgMdl->save($upOrgData)) {
            return [false, $orgMdl->db->errorinfo()];
        }

        // 保存门店
        $store = $storeMdl->dump(['store_bn' => $post['store_bn']]);
        if (!$store_id && $store) {
            return [false, '门店编码已经存在'];
        }

        if ($post['receive_order_start_time']) {
            $post['receive_order_start_time'] = strtotime($post['receive_order_start_time'] . " " . $post['_DTIME_']['H']['receive_order_start_time'] . ":" . $post['_DTIME_']['M']['receive_order_start_time'] . ":00");
            unset($post['_DTIME_']['H']['receive_order_start_time']);
            unset($post['_DTIME_']['M']['receive_order_start_time']);
        } else {
            unset($post['receive_order_start_time']);
        }

        if ($post['delivery_type'] == 'branch') {
            $post['branch_id'] = $branch['branch_id'];
        }
        //新建
        if ($post['new_receive_order_start_time']) {
            $post['receive_order_start_time'] = $post['new_receive_order_start_time'];
            unset($post['new_receive_order_start_time']);
        }

        if ($post['delivery_type'] == 'branch') {
            $post['branch_id'] = $branch['branch_id'];
        }

        $post['store_id'] = $store_id;
        if (!$store_id) {
            $post['create_time'] = time();
        }
        $post['sync_status'] = '0';
        if (!$storeMdl->save($post)) {
            return [false, $storeMdl->db->errorinfo()];
        }

        if ($post['delivery_type'] == 'branch') {
            // 记录操作日志
            app::get('ome')->model('operation_log')->write_log('store_upsert@o2o', $post['store_id'], ($store_id ? '编辑' : '创建') . '门店');

            app::get('ome')->model('branch')->update(['store_id' => $post['store_id'],], ['branch_id' => $branch['branch_id']]);
            return [true, '保存成功'];
        }

        $server = app::get('o2o')->model('server')->dump($post['server_id']);

        // 保存店铺
        $upShopData = array(
            'shop_bn' => $post['store_bn'],
            'name' => $post['name'],
            's_type' => 2,
            's_status' => $post['status'],
            'node_type' => $server['node_type'],
            'shop_type' => $server['node_type'],
            'node_id' => $post['store_bn'],
        );

        $shop = app::get('ome')->model('shop')->dump([
            'shop_bn' => $post['store_bn'],
            's_type' => 2,
        ]);
        $upShopData['shop_id'] = $shop['shop_id'];

        $rs = kernel::single('ome_interface_shop')->save($upShopData);
        if (!$rs) {
            return [false, '门店关联线下店铺保存失败，编码可能被占用'];
        }

        // 主仓
        $upBranchData = array(
            'branch_bn' => $post['store_bn'],
            'name' => $post['name'],
            'storage_code' => $post['storage_codes']['main'],
            'b_type' => 2,
            'b_status' => $post['status'],
            'weight' => $post['priority'],
            'area' => $area,
            'address' => $post['addr'],
            'zip' => $post['zip'],
            'phone' => $post['tel'],
            'uname' => $post['contacter'],
            'mobile' => $post['mobile'],
            'store_id' => $post['store_id'],
        );


        $branch = app::get('ome')->model('branch')->dump([
            'branch_bn' => $post['store_bn'],
            'b_type' => 2,
            'check_permission' => 'false',
        ]);
        $upBranchData['branch_id'] = (int)$branch['branch_id'];

        if ($upBranchData['branch_id']) {
            // 更新
            $rs = kernel::single('ome_interface_branch')->update($upBranchData, [
                'branch_id' => $upBranchData['branch_id'],
            ]);
        } else {
            // 插入
            $rs = kernel::single('ome_interface_branch')->save($upBranchData);
        }
        if (!$rs) {
            return [false, sprintf('门店关联虚拟仓保存失败：%s', kernel::database()->errorinfo())];
        }

        kernel::single('console_map_branch')->getLocation($upBranchData['branch_id']);

        kernel::single('ome_shop_onoffline')->doSave($upShopData['shop_id'], $post['online_id']);


        //更新相关的仓库和店铺
        $rs = $storeMdl->update([
            'branch_id' => $upBranchData['branch_id'],
            'shop_id' => $upShopData['shop_id'],
        ], ['store_id' => $post['store_id']]);
        if (!$rs) {
            return [false, '门店关联仓库、店铺信息更新失败'];
        }

        // 记录操作日志
        app::get('ome')->model('operation_log')->write_log('store_upsert@o2o', $post['store_id'], ($store_id ? '编辑' : '创建') . '门店');

        return [true, '保存成功'];
    }

    /**
     *
     * @param $store_id
     * @param $logi_code
     * @return mixed
     */
    public function getStoreWaybill($store_id, $logi_code)
    {
        if (empty($store_id) || empty($logi_code)) {
            return false;
        }

        $storeCorpMdl = app::get('o2o')->model('store_corp');

        $filter = [
            'store_id' => $store_id,
            'corp_code' => $logi_code,
            'corp_month_account_default' => '门店'
        ];
        $result = $storeCorpMdl->dump($filter, 'store_id,corp_month_customer,corp_month_account');
        if (empty($result)) {
            return false;
        }
        return $result;
    }
}
