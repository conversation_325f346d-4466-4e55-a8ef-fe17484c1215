<?php

/**
 * create by yzq
 * 2025-1-12
 * Class o2o_store_material
 */
class o2o_store_material
{

    /**
     * 获取物料的门店信息
     * @param $bn
     * @return array
     */
    public function getBmStore($bn)
    {
        if (empty($bn)) return [];
        $bmMdl = app::get("material")->model("basic_material");
        $storeMdl = app::get('o2o')->model('store');
        $bmInfo = $bmMdl->dump(array("material_bn" => $bn), "store_id");
        if (!$bmInfo['store_id']) return [];

        //关系转换
        $bmInfo['store_id'] = app::get("o2o")->model("store_relation")->get_store_by_oldid($bmInfo['store_id']);

        $storeInfo = $storeMdl->dump(array("store_id" => $bmInfo['store_id']));
        return $storeInfo;
    }

    /**
     * 获取物料的发货门店
     * @param $bn
     * @param $order_id
     * @return array
     */
    public function getBmDelivStore($bn, $order_id)
    {
        $odMdl = app::get("ome")->model("delivery");
        $storeMdl = app::get("o2o")->model("store");
        $odiMdl = app::get("ome")->model("delivery_items_detail");
        $basicMdl = app::get('material')->model('basic_material');
        $order_item_obj = app::get("ome")->model("order_items");

        $filter = array(
            "bn" => $bn,
            "order_id" => $order_id,
        );
        $odiList = $odiMdl->getList("delivery_id,order_item_id,product_id", $filter);
        if (empty($odiList)) {
            return [];
        }

        $field_name = 'store_id,name,performance_type,store_bn,branch_id,status,auto_agree_return,wms_store_code';
        $odiInfo = current($odiList);
        # 发货单
        $odInfo = $odMdl->dump($odiInfo['delivery_id'], "delivery_id,delivery_bn,branch_id");
        if (!empty($odInfo['branch_id'])) {
            $storeInfo = $storeMdl->dump(array("branch_id" => $odInfo['branch_id']), $field_name);
        } else {
            # 如果发货单仓库ID为空，这直接读取订单明细上的
            $itemInfo = $order_item_obj->dump(array('order_id' => $order_id, 'item_id' => $odiInfo['order_item_id']), 'fulfillment_store_id');
            if (!empty($itemInfo['fulfillment_store_id'])) {
                $storeInfo = $storeMdl->dump(array("store_id" => $itemInfo['fulfillment_store_id']), $field_name);
            } else {
                # 如果订单上的门店ID还是为空，这读取基础物料上的
                $materialInfo = $basicMdl->dump(array('bm_id' => $odiInfo['product_id']), 'store_id,supply_model');
                if (!empty($materialInfo['store_id'])) {
                    $storeInfo = $storeMdl->dump(array('store_id' => $materialInfo['store_id']), $field_name);
                }

                # 判断发货模式，云仓发货取云仓门店编码
                if (!empty($materialInfo['supply_model']) && in_array($materialInfo['supply_model'], ome_func::branch_model()) && !empty($storeInfo['wms_store_code'])) {
                    $storeInfo = $storeMdl->dump(array('store_bn' => $storeInfo['wms_store_code']), $field_name);
                }
            }
        }
        return $storeInfo ?? [];
    }

    /**
     * 获得订单商品对应的门店
     * @param $bn
     * @param $order_id
     * @return array
     */
    public function getOrderStore($bn, $order_id)
    {
        $order_item_obj = app::get("ome")->model("order_items");
        $store_mdl = app::get('o2o')->model('store');
        $filter = array(
            "bn" => $bn,
            "order_id" => $order_id,
            "delete" => 'false'
        );
        $items = $order_item_obj->getList("*", $filter);
        $item_info = $items[0];

        $storeInfo = $store_mdl->dump(array("store_id" => $item_info['fulfillment_store_id']), "store_id,name,performance_type,store_bn,branch_id,status");
        return $storeInfo ?: [];
    }

    /**
     * Notes: 根据物料编码获取机构ID
     * User: 七月
     * DateTime: 2025/2/21 10:48
     * @param $bn
     * @return int|mixed
     */
    public function getOrgByMaterialBn($bn): mixed
    {
        $sql = "SELECT d.org_id FROM sdb_material_basic_material AS a LEFT JOIN sdb_o2o_store AS b ON b.store_id = a.store_id LEFT JOIN sdb_organization_organization AS c ON c.org_id = b.org_id LEFT JOIN sdb_ome_operation_organization AS d ON d.code = c.org_no WHERE a.material_bn = '%s'";
        $sql = sprintf($sql, $bn);
        $db = kernel::database();
        $data = $db->selectrow($sql);
        return $data['org_id'] ?? 0;
    }

}