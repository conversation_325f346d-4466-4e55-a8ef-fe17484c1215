<?php
/**
 * 微信消息服务类
 */
class monitor_wechat_message
{

    /**
     * 获取支持发送微信消息的业务场景
     * 
     * @return array 业务场景列表
     */
    public function getBusinessScenes()
    {
        // 定义支持的业务场景
        $scenes = [
            'undefined' => [
                'name' => '请选择发送消息类型',
                'template_code' => 'undefined',
                'description' => '未定义的默认场景'
            ],
            'cancel_order' => [
                'name' => '取消订单通知',
                'template_code' => 'cancel_order',
                'description' => '未发货仅退款，订单自动变为已取消状态'
            ],
            'order_delivery_timeout' => [
                'name' => '订单已超时提醒',
                'template_code' => 'order_delivery_timeout',
                'description' => '48小时未发货订单'
            ],
            'order_refund' => [
                'name' => '退款申请提醒',
                'template_code' => 'order_refund',
                'description' => '已发货仅退款，待手动拦截订单或者买家申请退换货'
            ],
        ];
        return $scenes;
    }
    
    /**
     * 根据业务场景获取模板编码
     * 
     * @param string $scene_code 业务场景编码
     * @return string|null 模板编码，不存在时返回null
     */
    public function getTemplateCodeByScene($scene_code)
    {
        $scenes = $this->getBusinessScenes();
        
        if (isset($scenes[$scene_code])) {
            return $scenes[$scene_code]['template_code'];
        }
        
        return null;
    }

    /**
     * 根据模板名称获取模板编码
     * 
     * @param string $template_name 模板名称
     * @return string|null 模板编码，不存在时返回null
     */
    public function getTemplateCodeByName($template_name)
    {
        if (empty($template_name)) {
            return null;
        }
        
        $scenes = $this->getBusinessScenes();
        foreach($scenes as $val) {
            if ($val['name'] == $template_name) {
                return $val['template_code'];
            }
        }
        
        return null;
    }

    /**
     * 根据门店ID获取需要发送的openid
     */
    public function getOpenIdByBranchId($branchId) {

        if (empty($branchId)) {
            return [];
        }

        // 获取门店关联的操作员
        $branchOpsModel = app::get('o2o')->model('branch_ops');
        $branchOps = $branchOpsModel->getList('op_id', ['branch_id' => $branchId]);

        if (empty($branchOps)) {
            kernel::log('getOpenIdByBranchId: No operators found for branch_id: ' . $branchId);
            return [];
        }

        // 提取用户ID
        $userIds = array_column($branchOps, 'op_id');
        
        // 获取用户关联的微信openid
        $wechatUserModel = app::get('monitor')->model('wechat_users');

        $wechatUsers = $wechatUserModel->getList('openid', ['user_id' => $userIds, 'enabled' => 'true']);
        
        if (empty($wechatUsers)) {
            kernel::log('getOpenIdByBranchId: No wechat users found for user_ids: ' . implode(',', $userIds));
            return [];
        }
        
        // 返回openid数组
        return array_column($wechatUsers, 'openid');
    }

    /**
     * 根据门店ID获取需要发送的openid
     */
    public function getOpenIdByStoreId($storeId) {

        if (empty($storeId)) {
            return [];
        }

        // 获取门店信息
        $storeObj = app::get('o2o')->model('store');
        $storeInfo = $storeObj->dump(['store_id' => $storeId], 'branch_id');

        if (empty($storeInfo) || empty($storeInfo['branch_id'])) {
            kernel::log('getOpenIdByStoreId: Store not found or branch_id is empty for store_id: ' . $storeId);
            return [];
        }

        // 获取门店关联的操作员
        $branchOpsModel = app::get('o2o')->model('branch_ops');
        $branchOps = $branchOpsModel->getList('op_id', ['branch_id' => $storeInfo['branch_id']]);

        if (empty($branchOps)) {
            kernel::log('getOpenIdByStoreId: No operators found for branch_id: ' . $storeInfo['branch_id']);
            return [];
        }

        // 提取用户ID
        $userIds = array_column($branchOps, 'op_id');
        
        // 获取用户关联的微信openid
        $wechatUserModel = app::get('monitor')->model('wechat_users');
        $wechatUsers = $wechatUserModel->getList('openid', ['user_id' => $userIds, 'enabled' => 'true']);
        
        if (empty($wechatUsers)) {
            kernel::log('getOpenIdByStoreId: No wechat users found for user_ids: ' . implode(',', $userIds));
            return [];
        }
        
        // 返回openid数组
        return array_column($wechatUsers, 'openid');
    }

    /**
     * 发送微信模板消息
     *
     * @param string $template_code 模板编码
     * @param string $openid 接收用户OpenID
     * @param array $data 消息数据
     * @param string $url 跳转链接
     * @param string $related_id 关联业务单号
     * @return array 发送结果
     */
    public function addMessage($template_code, $openid, $data, $url = '', $related_id = '')
    {
        if (empty($template_code) || empty($openid) || empty($data)) {
            return [
                'rsp' => 'fail',
                'message' => '参数不完整'
            ];
        }

        // 查询模板信息
        $wechatTemplateMdl = app::get('monitor')->model('wechat_template');
        $template = $wechatTemplateMdl->dump(['template_code' => $template_code, 'status' => '1'], '*');
        
        if (!$template) {
            return [
                'rsp' => 'fail',
                'message' => "模板不存在或未启用: {$template_code}"
            ];
        }
        
        // 准备记录数据
        $logData = [
            'template_id' => $template['template_id'],
            'template_code' => $template['template_code'],
            'template_name' => $template['template_name'],
            'openid' => $openid,
            'related_id' => $related_id,
            'status' => '0'
        ];
        
        // 根据模板参数组装消息数据
        $templateParams = json_decode($template['template_params'], true);
        if (!$templateParams) {
            return [
                'rsp' => 'fail',
                'message' => '模板参数格式错误'
            ];
        }
        
        $messageData = [];
        foreach ($templateParams as $key => $desc) {
            if (isset($data[$key])) {
                $messageData[$key] = [
                    'value' => $data[$key]
                ];
            }
        }
        
        // 组装完整的消息数据
        $sendData = [
            'touser' => $openid,
            'template_id' => $template['template_id'],
            'data' => $messageData
        ];
        
        if (!empty($url)) {
            $sendData['url'] = $url;
        }

        if (!is_array($openid)) {
           $openid = [$openid]; 
        }

        foreach ($openid as $touser) {
            $insertData = $logData;
            $insertData['openid'] = $touser;
            $sendData['touser'] = $touser;
            $insertData['data'] = json_encode($sendData, JSON_UNESCAPED_UNICODE);
            // 记录消息发送日志
            $wechatNotifyMdl = app::get('monitor')->model('wechat_notify');
            $wechatNotifyMdl->insert($insertData);
        }
        return true;
    }

    public function sendMessage($notify_id)
    {
        $wechatNotifyMdl = app::get('monitor')->model('wechat_notify');
        $notifyData = $wechatNotifyMdl->dump(['notify_id' => $notify_id], '*');

        if (!$notifyData) {
            return [
                'status' => false,
                'message' => '找不到对应的消息记录'
            ];
        }

        if ($notifyData['status'] == '2') {
            return [
                'status' => false,
                'message' => '发送成功的不需要重新发送'
            ];
        }

        $sendData = json_decode($notifyData['data'], true);

        $urlData = [
            'openid' => $sendData['touser'],
            'redirect' => $sendData['url']
        ];
        $jsonData = kernel::single('base_rsa_functions')->rsaEncode(json_encode($urlData, JSON_UNESCAPED_UNICODE), 'wap');
        $sendData['url'] = kernel::base_url(true) . '/index.php/wap/passport/wxMessageRedirectLogin?data=' . $jsonData;

        // 发送微信消息
        try {
            // 获取access_token
            $accessToken = kernel::single('monitor_wechat_token')->getAccessToken();
            if (empty($accessToken)) {
                $this->updateLogStatus($notify_id, '获取access_token失败');
                return [
                    'rsp' => 'fail',
                    'message' => '获取access_token失败'
                ];
            }
            
            // 发送模板消息
            $api_url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$accessToken}";
            $result = $this->httpRequest($api_url, json_encode($sendData, JSON_UNESCAPED_UNICODE));
            
            // 处理响应结果
            $response = json_decode($result, true);
            if (isset($response['errcode']) && $response['errcode'] == 0) {
                // 发送成功，更新日志状态
                $this->updateLogStatus($notify_id, '', $response['msgid'], true);
                return [
                    'rsp' => 'succ',
                    'message' => '发送成功',
                    'data' => [
                        'notify_id' => $notify_id,
                        'msg_id' => $response['msgid']
                    ]
                ];
            } else {
                // 发送失败，更新日志状态
                $errorMsg = isset($response['errmsg']) ? $response['errmsg'] : '未知错误';
                $errorMsg .= '，错误代码：' . $response['errcode'];
                $this->updateLogStatus($notify_id, $errorMsg);
                return [
                    'rsp' => 'fail',
                    'message' => "发送失败: {$errorMsg}"
                ];
            }
        } catch (Exception $e) {
            // 发送异常，更新日志状态
            $this->updateLogStatus($notify_id, $e->getMessage());
            return [
                'rsp' => 'fail',
                'message' => "发送异常: " . $e->getMessage()
            ];
        }
    }

    /**
     * 初始化公众号模板消息
     * 
     * 从微信公众号拉取消息模板并保存到数据库
     * 
     * @return array 操作结果
     */
    public function initTemplates()
    {
        try {
            // 获取access_token
            $accessToken = kernel::single('monitor_wechat_token')->getAccessToken();
            if (empty($accessToken)) {
                return [
                    'success' => false,
                    'message' => '获取access_token失败，请检查微信配置'
                ];
            }
            
            // 获取模板列表
            $api_url = "https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token={$accessToken}";
            $result = $this->httpRequest($api_url);
            $response = json_decode($result, true);
            
            if (isset($response['errcode']) && $response['errcode'] != 0) {
                return [
                    'success' => false,
                    'message' => '获取模板列表失败：' . ($response['errmsg'] ?? '未知错误')
                ];
            }
            
            if (empty($response['template_list']) || !is_array($response['template_list'])) {
                return [
                    'success' => false,
                    'message' => '未找到任何模板，请先在微信公众平台添加模板消息'
                ];
            }
            
            // 获取模板模型
            $wechatTemplateMdl = app::get('monitor')->model('wechat_template');
            
            // 记录处理结果
            $result = [
                'success' => true,
            ];
            
            // 处理每个模板
            foreach ($response['template_list'] as $template) {
                // 检查模板是否已存在
                $existTemplate = $wechatTemplateMdl->dump(['template_id' => $template['template_id']], '*');

                $template_code = $this->getTemplateCodeByName($template['title']);
                
                // 准备保存的数据
                $templateData = [
                    'template_id' => $template['template_id'],
                    'template_name' => $template['title'],
                    'template_code' => $template_code ? $template_code : 'undefined',
                    'template_content' => $template['content'],
                    'template_params' => json_encode($this->preTemplateParams($template['content']), JSON_UNESCAPED_UNICODE),
                    'status' => $existTemplate['status'] ? $existTemplate['status'] : "0",
                    'updated_time' => time(),
                ];

                if (empty($existTemplate)) {
                    $templateData['created_time'] = time();
                }
                
                // 保存模板
                $wechatTemplateMdl->save($templateData);
                $result['added']++;
            }
            
            $result['message'] = "成功更新{$result['added']}个模板";
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '初始化模板异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 预处理模板参数
     * 
     * @param string $template_content 模板内容
     * @return array 预处理后的模板参数
     */
    public function preTemplateParams($template_content)
    {
        // 解析微信模板内容，提取变量并转换为参数映射
        $params = [];
        
        // 使用正则表达式匹配所有模板变量 {{xxx.DATA}}
        preg_match_all('/\{\s*\{([^\.]+)\.DATA\}\s*\}/', $template_content, $matches);
        
        if (!empty($matches[1])) {
            // 提取变量名和对应的描述
            $lines = explode("\n", $template_content);
            foreach ($matches[1] as $key) {
                $key = trim($key);
                $desc = '';
                
                // 尝试从内容中找到这个变量对应的描述
                foreach ($lines as $line) {
                    if (strpos($line, "{{" . $key . ".DATA}}") !== false) {
                        // 提取描述（变量前面的文字）
                        $parts = explode("{{" . $key . ".DATA}}", $line);
                        if (!empty($parts[0])) {
                            $desc = trim(str_replace([':', '：'], '', $parts[0]));
                            break;
                        }
                    }
                }
                
                // 如果没找到描述，使用变量名作为描述
                if (empty($desc)) {
                    $desc = $key;
                }
                
                $params[$key] = $desc;
            }
        }
        
        return $params;
    }

    /**
     * 更新日志状态
     *
     * @param int $log_id 日志ID
     * @param string $error_msg 错误信息
     * @param string $msg_id 消息ID
     * @param bool $is_success 是否成功
     * @return bool 更新结果
     */
    protected function updateLogStatus($notify_id, $error_msg = '', $msg_id = '', $is_success = false)
    {
        $wechatNotifyMdl = app::get('monitor')->model('wechat_notify');
        
        $data = [
            'status' => $is_success ? '2' : '1'
        ];
        
        if (!empty($error_msg)) {
            $data['error_msg'] = $error_msg;
        }
        
        if (!empty($msg_id)) {
            $data['msg_id'] = $msg_id;
        }
        
        return $wechatNotifyMdl->update($data, ['notify_id' => $notify_id]);
    }

    public function validateTemplateParam($value, $type)
    {
        // 微信模板参数类型长度限制
        $typeMaxLen = [
            'thing' => 20,
            'character_string' => 32,
            'phone_number' => 17,
            'car_number' => 8,
        ];

        // 只处理已知类型
        if (isset($typeMaxLen[$type])) {
            // 多字节安全截取
            return mb_substr($value, 0, $typeMaxLen[$type]);
        }
        return $value;
    }
    
    /**
     * 发送HTTP请求
     *
     * @param string $url 请求URL
     * @param string $postData POST数据
     * @return string 响应结果
     */
    public function httpRequest($url, $postData = null)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        
        if ($postData) {
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);
        }
        
        $result = curl_exec($curl);
        curl_close($curl);
        
        return $result;
    }
} 