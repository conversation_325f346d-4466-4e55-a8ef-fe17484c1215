<?php

/**
 * @Author: <EMAIL>
 * @Vsersion: 2022/10/13
 * @Describe: 监控预警模板Lib类
 */
class monitor_event_template
{
    /**
     * 获取预警事件类型
     * @Author: xueding
     * @Vsersion: 2022/10/13 下午4:57
     * @return string[]
     */
    public function getEventType()
    {
        $eventType = array(
            'wms_bill_fail_warning'    => 'WMS单据请求失败报警',
            'wms_delivery_consign'     => 'WMS发货失败报警',
            'wms_reship_finish'        => 'WMS退货失败报警',
            'wms_stockin_finish'       => 'WMS入库失败报警',
            'wms_stockout_finish'      => 'WMS出库失败报警',
            'wms_stock_change'         => 'WMS异动失败报警',
            'wms_stockprocess_confirm' => 'WMS加工单确认失败报警',
            'wms_transferorder_finish' => 'WMS移库失败报警',

            'process_undelivery'       => '未发货订单通知',

            'stock_sync'               => '平台库存同步失败报警',
            'under_safty_inventory'    => '低于安全库存报警',
            'stock_diff_alarm'         => '实物库存差异报警',

            'pos_stock_sync'           => 'POS库存同步失败报警',
            'pos_o2oundelivery'        => 'POS现货订单未发货报警',

            'system_message'           => '系统消息通知',
            'invoice_result_error'     => '发票处理失败报警',
            'delivery_cancel_fail'     => '发货单取消失败报警',
            'interception_logi_fail'   => '物流拦截失败预警',
            'interception_fail_while'  => '物流拦截失败轮循预警',
            'order_delivery_timer'     => '未发货订单定时预警',
            'order_consign_fail'       => '发货回传店铺失败预警',
            'receive_order_warning' => '订单接单异常',
            'refunded_nostock_timer'   => '已退款未入库定时预警',
            'stored_norefund_timer'    => '已入库未退款定时预警',
            'ordertoesb_check_timer'   => '未推送ESB数据检查',
            'h5_confirm_delivery_warning' => '门店发货库存不足预警',
            'order_receive_fail' => '失败订单预警',
            'invoice_fail_warning'  => '开票失败预警',
            'interception_succ_cancel' => '拦截成功后取消退款申请预警',
            'storereceivewarning'      => '门店未开启接单和配置接单时间预警',
            'retry_sync_kucun100'      => '重试获取库存100的商品和库存接口',
            'store_update_email'       => '门店店铺编码更换'
        );
        return $eventType;
    }
}
