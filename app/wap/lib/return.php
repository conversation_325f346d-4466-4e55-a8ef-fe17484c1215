<?php
/**
 * 门店售后申请类
 * 20250114
 * @author: <EMAIL>
 */
class wap_return{

    function __construct($app){
        $this->app = $app;
    }

    //获取并格式化售后申请的数据（包括主表和明细表）
    public function getList($filter, $offset=0, $limit=1, $orderby='', $menu_type = ''){
        $rpMdl = app::get("ome")->model("return_product");
        $rpiMdl = app::get("ome")->model("return_product_items");
        $odidMdl = app::get("ome")->model("delivery_items_detail");
        $odMdl = app::get("ome")->model("delivery");
        $odiMdl = app::get("wap")->model("delivery_items");
        $wdMdl = app::get("wap")->model("delivery");
        $shopMdl = app::get("ome")->model("shop");
        $ordMdl = app::get("ome")->model("orders");
        $bmeMdl = app::get("material")->model("basic_material_ext");
        $bmMdl = app::get("material")->model("basic_material");
        $reshipMdl = app::get("ome")->model("reship");
        $refundApplyMdl = app::get("ome")->model("refund_apply");
        $storeMdl = app::get('o2o')->model('store');
        $wbMdl = app::get("logisticsmanager")->model("waybill");
        $reship_list = [];
        $refund_apply_list = [];

        switch ($menu_type){
            //售后申请表
            case 'store_pending':
            case 'customer_pending':
            case 'closed':
                break;
            //退换货单表
            case 'wait_return':
                $reshipList = $reshipMdl->getList("reship_id,reship_bn,return_id,return_logi_no,logi_status,sign_time", $filter, $offset, $limit, $orderby);
                if (empty($reshipList)){
                    return array();
                }
                $filter = array(
                    'return_id' => array_column($reshipList, "return_id"),
                    'status|noequal' => '5',
                );
                $reship_list = array_column($reshipList, null, "return_id");
                $orderby = "";
                $limit = 10; //默认显示10条
                $offset = 0;
                break;
            //退款申请单表
            case 'wait_refund':
            case 'refunded':
                $filter['is_part_refund'] = '0';//过滤部分退款
                $refundApplyList = $refundApplyMdl->getList("apply_id,refund_apply_bn,return_id,status", $filter, $offset, $limit, $orderby);
                if (empty($refundApplyList)){
                    return array();
                }
                $filter = array(
                    'return_id' => array_column($refundApplyList, "return_id"),
                );
                $refund_apply_list = array_column($refundApplyList, null, "return_id");
                $orderby = "";
                $limit = 10; //默认显示10条
                $offset = 0;
                break;
        }

        //主表数据
        $dataList = $rpMdl->getList("*", $filter, $offset, $limit, $orderby);
        if (empty($dataList)){
            return array();
        }

        //统一获取order_bn
        $order_ids= array();
        foreach ($dataList as $var_d){
            if(!in_array($var_d["order_id"],$order_ids)){
                $order_ids[] = $var_d["order_id"];
            }
        }
        if (!empty($order_ids)){
            $mdl_ome_orders = app::get('ome')->model("orders");
            $rs_order = $mdl_ome_orders->getList("order_id,order_bn,logi_id,createtime",array("order_id|in"=>$order_ids));
            $rl_order_id_info = array();
            foreach ($rs_order as $var_o){
                $rl_order_id_info[$var_o["order_id"]]["order_bn"] = $var_o["order_bn"];
                $rl_order_id_info[$var_o["order_id"]]["createtime"] = $var_o["createtime"];
            }
        }
        $shop_type_map = ome_shop_type::get_shop_type();
        $logi_status_map = array(
            '0' => '无',
            '1' => '已揽收',
            '2' => '在途中',
            '3' => '已签收',
            '4' => '退件/问题件',
            '5' => '待取件',
            '6' => '待派件',
            '7' => '待揽收',
        );
        $menu_type_map = array(

        );
        foreach ($dataList as &$var_data){
            //获取明细表数据
            $current_return_items = $rpiMdl->getList("*",array("return_id"=>$var_data["return_id"]));
            foreach($current_return_items as &$ritems){
                $specifications = $this->getProductAttr($ritems['bn'], $var_data['order_id']);
                $bmeInfo = $bmeMdl->dump(array("bm_id" => $ritems['product_id']), "specifications");
                $bmInfo = $bmMdl->dump(array("bm_id" => $ritems['product_id']), "busness_material_bn");
                $ritems['specifications'] = $specifications ? $specifications : $bmeInfo['specifications'];
                $ritems['busness_material_bn'] = $bmInfo['busness_material_bn'];
                $ritems['goods_img_url'] = $this->getImgUrl($var_data['order_id'], $ritems['order_item_id']);
                $ritems['name'] = $this->getOrderObjectsName($var_data['order_id'], $ritems['order_item_id'], $ritems['bn']);
                # 商城处理金额，使用四舍五入方法
                if($var_data['shop_type'] == 'ecos.ecshopx') {
                    $ritems['amount'] = sprintf('%.2f', round($ritems['num'] * $ritems['price'], 2));
                }else{
                    $ritems['amount'] = bcmul($ritems['num'], $ritems['price'],2);
                }
            }
            $var_data['return_product_items'] = $current_return_items;
            $total_amount = array_sum(array_column($current_return_items, "amount"));
            # 商城的总价直接取申请总金额
            if($var_data['shop_type'] == 'ecos.ecshopx'){
                $var_data['total_amount'] = sprintf('%.2f', $var_data['tmoney'] ?? $var_data['money']);
                # 如果申请的总金额还是0，则直接取明细的总金额
                if(floatval($var_data['total_amount']) <= 0 && floatval($total_amount) > 0){
                    $var_data['total_amount'] = $total_amount;
                }
            }else{
                $var_data['total_amount'] = $total_amount;
            }
            //获取换货明细
            $change_items = $this->getReturnChangeItems($var_data);
            if($change_items){
                $var_data['change_items'] = $change_items;
            }
            //获取status_text
            $var_data["status_text"] = $this->getStatusText($var_data['status'], $var_data['store_check_status'], $var_data['belong_type']);
            //获取订单bn
            $var_data["order_bn"] = $rl_order_id_info[$var_data["order_id"]]["order_bn"];
            if ($menu_type == 'wait_return') {
                $var_data["order_create_date"] = date("Y-m-d H:i:s", $rl_order_id_info[$var_data["order_id"]]["createtime"]);
            }

            $is_change = app::get('ome')->model('reship')->is_change_order($var_data['order_id']);
            $var_data['is_change_order'] = $is_change ? true : false;

            //获取发货单号
            $dFilter = array(
                'order_id' => $var_data['order_id'],
                'bn' => $current_return_items[0]['bn'],
            );
            $dList = $odidMdl->getList("delivery_id", $dFilter);
            $dids = array_column($dList, "delivery_id");
            $d_filter = array(
                'delivery_id' => $dids,
                'status|notin' => ['cancel','back'],
                'disabled|noequal' => 'true',
            );
            $odList = $odMdl->getList("delivery_bn,logi_status,logi_no,logi_id", $d_filter);
            $wdInfo = $wdMdl->dump(array("outer_delivery_bn" => $odList[0]['delivery_bn']), "*");
            $var_data['logi_status'] = $odList[0]['logi_status'];
            $var_data['logi_status_name'] = $logi_status_map[$odList[0]['logi_status']];
            $var_data['logi_no'] = $odList[0]['logi_no'];
            $var_data['delivery_bn'] = $wdInfo['delivery_bn'];
            $var_data['delivery_id'] = $wdInfo['delivery_id'];
            $var_data['logi_id'] = $odList[0]['logi_id'];

            $shopInfo = $shopMdl->dump($var_data['shop_id'], "name,shop_type");
            $var_data['shop_name'] = $shopInfo['name'];
            $var_data['shop_type_name'] = $shop_type_map[$shopInfo['shop_type']];

            //所属门店信息
            $storeInfo = $storeMdl->dump(array("store_id" => $var_data['belong_store_id']), "store_id,name,performance_type,store_bn,branch_id,status");
            $var_data['store_name'] = $storeInfo['name'];

            $ordInfo = $ordMdl->dump($var_data['order_id'], "createtime,mark_text,custom_mark");

            //备注
            $order_remark_text = '';
            if ($ordInfo['mark_text']) {
                $mark_text = unserialize($ordInfo['mark_text']);
                if ($mark_text) {
                    foreach ($mark_text as $k => $v) {
                        $order_remark_text .= $v['op_content']." ".$v['op_time']." by ".$v['op_name']."<br>";
                    }
                }
            }

            //客户备注
            $custom_remark_text = '';
            if ($ordInfo['custom_mark']) {
                $custom_mark = unserialize($ordInfo['custom_mark']);
                if ($custom_mark) {
                    foreach ($custom_mark as $k => $v) {
                        $custom_remark_text .= $v['op_content']." ".$v['op_time']." by ".$v['op_name']."<br>";
                    }
                }
            }
            $var_data['order_remark_text'] = $order_remark_text;
            $var_data['custom_remark_text'] = $custom_remark_text;

            $var_data['createtime_format'] = date("Y-m-d H:i:s", $ordInfo['createtime']);
            $var_data['add_time_format'] = date("Y-m-d H:i:s", $var_data['add_time']);
            $address = $this->decryptAddress($var_data['order_id'], false);
            $var_data['ship_name'] = $address['ship_name'];
            $var_data['ship_mobile'] = $address['ship_mobile'];
            $var_data['ship_tel'] = $address['ship_tel'];
            $var_data['ship_addr'] = $address['ship_area'].$address['ship_addr'];
            $var_data['decrypt_ship_name'] = $address['decrypt_ship_name'];
            $var_data['decrypt_ship_tel'] = $address['decrypt_ship_tel'];
            $var_data['decrypt_ship_mobile'] = $address['decrypt_ship_mobile'];
            $var_data['decrypt_ship_addr'] = $address['ship_area'].$address['decrypt_ship_addr'];
            //商城脱敏
            if($shopInfo['shop_type'] == 'ecos.ecshopx'){
                $var_data['ship_name'] = kernel::single('base_view_helper')->modifier_cut($var_data['ship_name'],-1,'*',false,true);
                $var_data['ship_mobile'] = kernel::single('base_view_helper')->modifier_cut($var_data['ship_mobile'],-1,'****',false,true);
                $var_data['ship_tel'] = kernel::single('base_view_helper')->modifier_cut($var_data['ship_tel'],-1,'****',false,true);
                $var_data['ship_addr'] = $address['ship_area']."******";
            }
            //根据平台售后申请最后更新时间
            if(!is_null($var_data['outer_lastmodify']) && $var_data['outer_lastmodify'] > $var_data['add_time']){
                $last_modify_time = $var_data['outer_lastmodify'];
            }else{
                $last_modify_time = $var_data['add_time'];
            }
            if($var_data['belong_type'] == 'store' && in_array($var_data['status'], ['1','2']) && $var_data['store_check_status'] == '0' && $var_data['intercept_status'] == '0'){
                $var_data["show_pending_button"] = true;
                $var_data['status_name'] = '待审核';
                $var_data['deadline'] = ($last_modify_time+48*3600)*1000;
                $content = $var_data['content'];
                if($var_data['return_type'] == 'change'){
                    #$var_data['deadline'] = ($var_data['add_time']+36*3600)*1000;//换货36小时
                    $var_data['content'] = "用户换货理由：".$content;
                }else{
                    $var_data['content'] = "用户退款理由：".$content;
                }
            }
            if($var_data['belong_type'] == 'store' && in_array($var_data['intercept_status'], ['1','4']) && $var_data['status'] != '5'){
                $var_data["show_intercept_button"] = true;
                $var_data["intercept_href"] = app::get('wap')->router()->gen_url(array('ctl'=>'return_apply','act'=>'addIntercept'), true)."?return_id=".$var_data["return_id"];
                $var_data['status_name'] = '待拦截';
                $var_data['deadline'] = ($last_modify_time+36*3600)*1000;
                if($var_data['intercept_status'] == '1'){
                    $corpInfo = app::get("ome")->model("dly_corp")->dump($var_data['logi_id'], "*");
                    if(in_array($corpInfo['type'], array('SF','JD'))){
                        if($wbMdl->dump(array("delivery_id" => $var_data['delivery_id'], 'status'=>'1'), "*")){
                            $var_data['status_name'] = '消费者申请仅退款，系统拦截中';
                            $var_data["show_intercept_button"] = false;
                        }else{
                            $var_data['status_name'] = '消费者申请仅退款，需手动拦截';
                        }
                    }else{
                        $var_data['status_name'] = '消费者申请仅退款，需手动拦截';
                    }
                }
            }
            if($var_data['belong_type'] == 'customer' && $var_data['wait_customer_check'] == '1'){
                $var_data['status_name'] = '待客服处理';
            }
            if(isset($reship_list[$var_data['return_id']])){
                $var_data["show_pending_button"] = false;
                $var_data["show_intercept_button"] = false;
                $var_data['deadline'] = false;
                $var_data['status_name'] = '商品待退回';
                $var_data["reship_bn"] = $reship_list[$var_data['return_id']]['reship_bn'];
                $var_data["reship_id"] = $reship_list[$var_data['return_id']]['reship_id'];
                $var_data['return_logi_status_name'] = "未签收";
                if(!empty($reship_list[$var_data['return_id']]['return_logi_no'])){
                    $var_data['show_logistics_button'] = true;//有退回物流单号了，展示查看物流按钮
                    $var_data["show_reship_button"] = true;//展示退货审核按钮
                    $var_data['status_name'] = '待退货审核';
                    $var_data['return_logi_no'] = $reship_list[$var_data['return_id']]['return_logi_no'];
                    if($reship_list[$var_data['return_id']]['logi_status'] == '3'){
                        $var_data['return_logi_status_name'] = "已签收";
                        if($reship_list[$var_data['return_id']]['sign_time'] > 0){
                            $var_data['deadline'] = ($reship_list[$var_data['return_id']]['sign_time']+48*3600)*1000;
                        }else{
                            $var_data['deadline'] = ($last_modify_time+48*3600)*1000;
                        }
                    }
                }

                if ($var_data['return_type'] == 'refund' && $var_data['logi_status'] == '8') {
                    $var_data['status_name']               = '未揽件仅退款';
                    $var_data['show_logistics_button']     = false;
                    $var_data["show_reship_button"]        = false;
                    $var_data["show_reship_refund_button"] = true;//确认退货退款按钮
                }
                $var_data["reship_href"] = app::get('wap')->router()->gen_url(array('ctl'=>'return_apply','act'=>'addReship?return_id='.$var_data["return_id"].'&reship_id='.$reship_list[$var_data['return_id']]['reship_id']), true);

            }
            if(isset($refund_apply_list[$var_data['return_id']])){
                $var_data["show_pending_button"] = false;
                $var_data["show_intercept_button"] = false;
                $var_data['deadline'] = false;
                $refundApplyInfo = $refund_apply_list[$var_data['return_id']];
                if($refundApplyInfo['status'] == '4'){
                    if ($var_data['delivery_id'] > 0) {
                        $delivery_items = $odiMdl->getList('*', ['delivery_id' => $var_data['delivery_id']]);
                        $delivery_items = array_column($delivery_items, null, 'bn');
                        foreach ($var_data['return_product_items'] as &$return_product_item) {
                            if (isset($delivery_items[$return_product_item['bn']])) {
                                $return_product_item['esb_amount']     = $delivery_items[$return_product_item['bn']]['esb_amount'];
                                $return_product_item['esb_pmt_amount'] = $delivery_items[$return_product_item['bn']]['esb_pmt_amount'];
                            }
                        }
                        $var_data['esb_amount']     = array_sum(array_column($var_data['return_product_items'], 'esb_amount'));
                        $var_data['esb_pmt_amount'] = array_sum(array_column($var_data['return_product_items'], 'esb_pmt_amount'));
                        $var_data['total_amount']   = $var_data['esb_amount'] - $var_data['esb_pmt_amount'];
                    }
                    $var_data['status_name'] = '已退款';
                    $reshipInfo = $reshipMdl->dump(array("return_id" => $var_data['return_id']), "return_logi_no");
                    if(isset($reshipInfo['return_logi_no']) && !empty($reshipInfo['return_logi_no'])){
                        $var_data['return_logi_no'] = $reshipInfo['return_logi_no'];
                    }

                }else{
                    $var_data['status_name'] = '待客服审核退款';
                }

            }
            if($var_data['status'] == '5'){
                $var_data['status_name'] = '已关闭';
            }
        }
        unset($var_data);
        return $dataList;
    }

    public function getOrderObjectsName($order_id, $order_item_id, $bn =''){
        if ($order_item_id) {
            $oiMdl = app::get("ome")->model("order_items");
            $oiInfo = $oiMdl->dump(array("item_id" => $order_item_id, 'order_id' => $order_id), "obj_id");
            $obj_id = $oiInfo['obj_id'];
        }

        if ($obj_id) {
            $orderObjectsObj = app::get("ome")->model("order_objects");
            $orderObjects = $orderObjectsObj->dump(array('obj_id' => $obj_id, 'order_id' => $order_id), 'name');
            return $orderObjects['name'];
        }

        if ($bn) {
            $oiMdl = app::get("ome")->model("order_objects");
            $oiInfo = $oiMdl->dump(array("bn" => $bn, 'order_id' => $order_id), "name");
            return $oiInfo['name'];
        }
    }

    /**
     * 获取售后详情换货商品
     * @param $data
     * @return array|false
     */
    public function getReturnChangeItems($data){
        if($data['return_type'] != 'change'){
            return false;
        }
        $change_items = [];
        $bmMdl = app::get("material")->model("basic_material");
        $bmeMdl = app::get("material")->model("basic_material_ext");
        if ($data['source'] == 'matrix' )
        {
            if($data['shop_type'] == 'tmall'){
                $tmall_detail = kernel::single('ome_service_aftersale')->get_return_type(array('return_id'=>$data['return_id']));
            }else if(in_array($data['shop_type'],['luban','pinduoduo','wxshipin'])){
                $tmall_detail = app::get('ome')->model('return_product_'.$data['shop_type'])->dump(array('return_id'=>$data['return_id'],'refund_type'=>'change'),'*');
            } else {
                $tmall_detail = app::get('ome')->model('return_apply_special')->db_dump(array('return_id'=>$data['return_id']),'*');
                if($tmall_detail['special'] && is_array(json_decode($tmall_detail['special'], 1))) {
                    $tmall_detail = array_merge($tmall_detail, json_decode($tmall_detail['special'], 1));
                }
            }

            $smMdl = app::get("material")->model("sales_material");
            $smInfo = $smMdl->dump(array("sales_material_bn" => $tmall_detail['exchange_sku']), "sales_material_name");
            $bmMdl = app::get("material")->model("basic_material");
            $bmInfo = $bmMdl->dump(array("material_bn" => $tmall_detail['exchange_sku']), "bm_id,material_name");
            $bmeInfo = $bmeMdl->dump(array("bm_id" => $bmInfo['bm_id']), "specifications");
            $change_items[] = array(
                'bn' => $tmall_detail['exchange_sku'],
                'name' => $smInfo['sales_material_name'] ? $smInfo['sales_material_name'] : $bmInfo['material_name'],
                'num' => $tmall_detail['exchange_num'],
                'specifications' => $bmeInfo['specifications'],
                'goods_img_url' => $this->getImgUrl($data['order_id'], '', $tmall_detail['oid'], $tmall_detail['exchange_sku']),
            );
        }
        return $change_items;
    }

    public function getProductAttr($bn, $order_id){
        if (empty($bn) || empty($order_id)) {
            return false;
        }
        $order_item_mdl = app::get("ome")->model("order_items");
        $order_items = $order_item_mdl->getList('*', array('order_id' => $order_id), 0, -1);
        $order_items = array_column($order_items, null, 'bn');
        $addon_str = $order_items[$bn]['addon'];
        $addon = [];
        $product_attr = unserialize(data: $addon_str);
        if (!empty($product_attr['product_attr'])) {
            foreach ($product_attr['product_attr'] as $attr) {
                if (isset($attr['original_str'])) {
                    $addon[] = $attr['original_str'];
                    break;
                } else {
                    $addon[] = $attr['label'] . ":" . $attr['value'];
                }
            }
        }
        if ($addon) {
            $addon = implode(';', $addon);
        } else {
            $addon = '-';
        } 
        return $addon;
    }

    /**
     * 获取退货单信息
     * @param $reship_id
     * @return mixed
     */
    public function getReshipInfo($reship_id){
        $reshipMdl = app::get("ome")->model("reship");
        $riMdl = app::get("ome")->model("reship_items");
        $bmeMdl = app::get("material")->model("basic_material_ext");
        $reshipInfo = $reshipMdl->dump($reship_id, "*");
        $reship_items = $riMdl->getList("*", array("reship_id" => $reship_id, "return_type" => "return"));
        foreach($reship_items as $k => $v){
            $specifications = $this->getProductAttr($v['bn'], $reshipInfo['order_id']);
            $bmeInfo = $bmeMdl->dump(array("bm_id" => $v['product_id']), "specifications");
            $reship_items[$k]['specifications'] = $specifications ? $specifications : $bmeInfo['specifications'];
            $reship_items[$k]['goods_img_url'] = $this->getImgUrl($reshipInfo['order_id'], $v['order_item_id']);
            $reship_items[$k]['amount'] = bcmul($v['num'], $v['price'], 2);
            $reship_items[$k]['name'] = $this->getOrderObjectsName($reshipInfo['order_id'], $v['order_item_id'], $v['bn']);
        }
        $reshipInfo['items'] = $reship_items;
        return $reshipInfo;
    }

    /**
     * 获取拦截退货 售后申请信息
     * @param $return_id
     * @return mixed
     */
    public function getInterceptInfo($return_id){
        $rpMdl = app::get("ome")->model("return_product");
        $rpiMdl = app::get("ome")->model("return_product_items");
        $odidMdl = app::get("ome")->model("delivery_items_detail");
        $odMdl = app::get("ome")->model("delivery");
        $wdMdl = app::get("wap")->model("delivery");

        $logi_status_map = array(
            '0' => '无',
            '1' => '已揽收',
            '2' => '在途中',
            '3' => '已签收',
            '4' => '退件/问题件',
            '5' => '待取件',
            '6' => '待派件',
        );
        //主表数据
        $var_data = $rpMdl->dump($return_id, "*");
        $current_return_items = $rpiMdl->getList("*",array("return_id"=>$var_data["return_id"]));
        //获取发货单号
        $dFilter = array(
            'order_id' => $var_data['order_id'],
            'bn' => $current_return_items[0]['bn'],
        );
        $dList = $odidMdl->getList("delivery_id", $dFilter);
        $dids = array_column($dList, "delivery_id");
        $d_filter = array(
            'delivery_id' => $dids,
            'status|noequal' => 'cancel',
            'disabled|noequal' => 'true',
        );
        $odList = $odMdl->getList("delivery_bn,logi_status,logi_no", $d_filter);
        $wdInfo = $wdMdl->dump(array("outer_delivery_bn" => $odList[0]['delivery_bn']), "*");
        $var_data['logi_status'] = $odList[0]['logi_status'];
        $var_data['logi_status_name'] = $logi_status_map[$odList[0]['logi_status']];
        $var_data['logi_no'] = $odList[0]['logi_no'] ?: '';
        $var_data['delivery_bn'] = $wdInfo['delivery_bn'];
        return $var_data;
    }

    /**
     * 获取商品图片地址
     * @param $order_id
     * @param $order_item_id
     * @return string
     */
    public function getImgUrl($order_id, $order_item_id = '', $oid = '', $bn =''){
        // 显示图片
        $ordMdl = app::get("ome")->model("orders");
        $objMdl = app::get("ome")->model("order_objects");
        $oiMdl = app::get("ome")->model("order_items");
        if($oid){
            $oiInfo = $objMdl->dump(array("oid" => $oid), "shop_goods_id,bn,obj_id");
            $itemInfo = $oiMdl->dump(array("obj_id" => $oiInfo['obj_id']), "shop_goods_id,obj_id,img");
        } else {
            $itemInfo = $oiMdl->dump(array("item_id" => $order_item_id), "shop_goods_id,obj_id,img");
            $oiInfo = $objMdl->dump(array("obj_id" => $itemInfo['obj_id']), "shop_goods_id,bn");
        }

        if (empty($oiInfo['bn'])) {
            $oiInfo['bn'] = $bn;
        }

        $ordInfo = $ordMdl->dump($order_id, "shop_id,shop_type");


        if($ordInfo['shop_type'] == 'ecos.ecshopx'){
            $default_img_url = $itemInfo['img'];
        }else{

            $material_info = app::get('material')->model('basic_material')->dump(array('material_bn' => $oiInfo['bn']), 'bm_id');
            if ($material_info) {
                $material_info_ext = app::get('material')->model('basic_material_ext')->dump(array('bm_id' => $material_info['bm_id']), 'banner');
                if ($material_info_ext['banner']) {
                    $default_img_url = $material_info_ext['banner'];
                }
            }

            $inventorydepthSkuObj = app::get('inventorydepth')->model('shop_skus');
            $objectsItems = $inventorydepthSkuObj->dump(array('shop_product_bn' => $oiInfo['bn'], 'shop_id' => $ordInfo['shop_id']), 'default_img_url');
            if ($objectsItems['default_img_url']) {
                $default_img_url = $objectsItems['default_img_url'];
            } else {
                #$inventorydepthItemsObj = app::get('inventorydepth')->model('shop_items');
                #$inventorydepthItems = $inventorydepthItemsObj->dump(array('iid' => $objectsItems['shop_iid'], 'shop_id' => $ordInfo['shop_id']), 'default_img_url');
                #$default_img_url = '';
                #if ($inventorydepthItems) {
                #    $default_img_url = $inventorydepthItems['default_img_url'];
                #}
            }

            if ($default_img_url && $ordInfo['shop_type'] == '360buy') {
                $default_img_url = 'https://img13.360buyimg.com/n2' . $objectsItems['default_img_url'];
            }
        }

        if (empty($default_img_url)) {
            $default_img_url = kernel::base_url() . '/app/wap/statics/img/nopic.jpg';
        }

        return $default_img_url;
    }

    /**
     * 获取订单加解密收货地址信息
     * @param $orderId
     * @return array|string[]
     */
    public function decryptAddress($orderId, $decrypt = true)
    {
        $field = 'order_bn,shop_id,shop_type,ship_area,ship_tel,ship_mobile,ship_addr,ship_name';
        $data = app::get('ome')->model('orders')->db_dump(array('order_id' => $orderId), $field);
        if (!$data) {
            return [
                'ship_name' => '',
                'ship_tel' => '',
                'ship_mobile' => '',
                'ship_addr' => '',
                'ship_area' => '',
                'decrypt_ship_name' => '',
                'decrypt_ship_tel' => '',
                'decrypt_ship_mobile' => '',
                'decrypt_ship_addr' => '',
            ];
        }
        $decrypt_data = [];
        if($decrypt){
            $decrypt_data = kernel::single('ome_security_router', $data['shop_type'])->decrypt(array(
                'ship_tel'    => $data['ship_tel'],
                'ship_mobile' => $data['ship_mobile'],
                'ship_addr'   => $data['ship_addr'],
                'shop_id'     => $data['shop_id'],
                'order_bn'    => $data['order_bn'],
                'ship_name' => $data['ship_name'],
            ), 'order');
        }
        $ship_area_arr = explode(":", $data['ship_area']);
        $ship_area = str_replace("/", "", $ship_area_arr[1]);
        if(strpos($data['ship_addr'], ">>") !== false){
            $data['ship_addr'] = explode(">>", $data['ship_addr'])[0];
        }
        if(strpos($data['ship_mobile'], ">>") !== false){
            $data['ship_mobile'] = explode(">>", $data['ship_mobile'])[0];
        }
        if(strpos($data['ship_name'], ">>") !== false){
            $data['ship_name'] = explode(">>", $data['ship_name'])[0];
        }

        $res = [
            'ship_name' => $data['ship_name'],
            'ship_tel' => $data['ship_tel'],
            'ship_mobile' => $data['ship_mobile'],
            'ship_addr' => $data['ship_addr'],
            'ship_area' => $ship_area,
            'decrypt_ship_name' => isset($decrypt_data['ship_name']) ? $decrypt_data['ship_name'] : '',
            'decrypt_ship_tel' => isset($decrypt_data['ship_tel']) ? $decrypt_data['ship_tel'] : '',
            'decrypt_ship_mobile' => isset($decrypt_data['ship_mobile']) ? $decrypt_data['ship_mobile'] : '',
            'decrypt_ship_addr' => isset($decrypt_data['ship_addr']) ? $decrypt_data['ship_addr'] : '',
        ];
        return $res;
    }

    public function getStatusText($status, $store_check_status, $belong_type){
        if($status == '5'){
            return "客服已拒绝";
        }
        switch ($belong_type){
            case 'store':
                if($store_check_status == '1'){
                    return "门店已同意";
                }elseif($store_check_status == '2'){
                    return "门店已拒绝待客服审核";
                }else{
                    return "待门店审核";
                }
                break;
            case 'customer':

                break;
        }
    }

}
