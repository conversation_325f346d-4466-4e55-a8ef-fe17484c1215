<?php
/**
 * 更新库存逻辑
 *
 * <AUTHOR>
 * @version $2012-8-6 17:22Z
 */
class inventorydepth_logic_stock extends inventorydepth_logic_abstract
{
    /* 当前的执行时间 */
    public static $now;
    /* 更新的商品列表 */
    private $product_list = array();
    /* 更新商品的时间 */
    private $readStoreLastmodify = 0;

    /**
     * undocumented class variable
     *
     * @var string
     **/
    private $_errmsg = '';

    /* 执行的间隔时间 */
    const intervalTime = 40; //修改为40秒

    function __construct($app)
    {
        $this->app = $app;
        self::$now = time();
    }

    public function start()
    {
        @set_time_limit(0);
        @ini_set('memory_limit','1024M');
        ignore_user_abort(true);
        
        base_kvstore::instance('inventorydepth/apply/stock')->fetch('apply-lastexectime',$lastExecTime);
        if($lastExecTime && ($lastExecTime+self::intervalTime)>self::$now) {
            return false;
        }
        base_kvstore::instance('inventorydepth/apply/stock')->store('apply-lastexectime',self::$now);
        
        //获取货品
        $products = $this->getChgProducts();
        base_kvstore::instance('inventorydepth/apply')->store('read_store_lastmodify',self::$now);
        if(empty($products)){
            return false;
        }
        
        $this->do_sync_products_stock($products);
    }

    public function getStock($product_bn,$shop_id,$shop_bn,$node_type='') 
    {
        # 读取商品要执行的规则
        $quantity = $this->dealWithRegu($product_bn,$shop_id,$shop_bn);
        if ($quantity === false) { return false; }

        $params = array(
            'shop_product_bn' => $product_bn,
            'shop_bn'         => $shop_bn,
            'shop_id'         => $shop_id,
        );
        # 店铺冻结
        $stockCalLib = kernel::single('inventorydepth_calculation_salesmaterial');
        list($store_freeze, ) = $stockCalLib->get_shop_freeze($params);
        $memo = array(
                'store_freeze' => $store_freeze,
                'last_modified' => time(),
        );
        
        // 受1号店回写库存限制
        if($node_type == 'yihaodian' && $quantity >= 3000){
            $quantity = 2999;
        }

        list($actual_stock,) = $stockCalLib->get_actual_stock($params);
        $stock = array(
            'bn' => $product_bn,
            'quantity' => $quantity,
            'actual_stock' => $actual_stock,
            'memo'     => json_encode($memo),
            'regulation' => $this->regulationShow
        );
        // 查询增量库存
        if (kernel::single('inventorydepth_sync_set')->isModeSupportInc($node_type)) {        
            $stockLogMdl   = app::get('ome')->model('api_stock_log');
            $last_quantity = $stockLogMdl->getLastStockLog($shop_id, $product_bn);
            if ($last_quantity) {
                $stock['inc_quantity'] = $quantity - $last_quantity['store'];
            }
        }

        return $stock;
    }

    public function dealWithRegu($pbn,$shop_id,$shop_bn,&$apply_regulation=array()) {
        $sales_material = kernel::single('inventorydepth_calculation_basicmaterial')->getSalesMaterial($pbn);
        $sm_id = $sales_material['sm_id'];
        $this->regulationShow = [];
        $regu = $this->getRegu($shop_id);
        foreach($regu as $r){
            if(empty($r['regulation'])) continue;
            $smBool = true;
            if($sales_material['sales_material_type'] == 2){ //促销
                $smBool = in_array($sm_id,$r['apply_pkg']) || $r['apply_pkg'][0]=='_ALL_';
            }elseif($sales_material['sales_material_type'] == 5){ //多选一
                $smBool = in_array($sm_id,$r['apply_pko']) || $r['apply_pko'][0]=='_ALL_';
            }else{
                $smBool = in_array($sm_id,$r['apply_goods']) || $r['apply_goods'][0]=='_ALL_';
            }
            if($smBool && (in_array($shop_id,$r['shop_id']) || $r['shop_id'][0]=='_ALL_')) {

                if ($r['style'] == 'fix') {
                    $this->reguUpdateFilter['id'][] = $r['id'];
                }
                
                /*
                if (isset($this->stock_quantity[$r['id']][$product_id]) && $this->stock_quantity[$r['id']][$product_id] >= 0) {
                    $quantity = (int)$this->stock_quantity[$r['id']][$product_id];
                    break;
                }*/

                # 判断是否满足规则
                $params = array(
                    'shop_product_bn' => $pbn,
                    'shop_bn'             => $shop_bn,
                    'shop_id'              => $shop_id,
                );
                foreach ($r['regulation']['content']['filters'] as $filter) {
                    $allow_update = $this->check_condition($filter,$params);

                    if(!$allow_update){ continue 2;}
                }

                if ($r['regulation']['content']['stockupdate'] != 1) { 
                    $apply_regulation = $r['regulation'];
                    return false;
                }

                $quantity = kernel::single('inventorydepth_calculation_salesmaterial')->formulaRun($r['regulation']['content']['result'],$params,$msg);

                if ($quantity === false){ continue; }
               
                $this->regulationShow = ['规则名称'=>$r['regulation']['heading'], 'detail'=>$msg]; 
                if (!empty($this->product_list)) {
                    $new_product= array_column($this->product_list,null,'sm_id');
                    //如果是规则等于订单的并且 stock_status == false continue
                    if (isset($new_product[$sm_id]['exit_io']) && !$new_product[$sm_id]['exit_io'] && $r['style'] == 'order_change') {
                        return false;
                    }
                }
                //$this->stock_quantity[$r['id']][$product_id] = $quantity;
                
                $apply_regulation = $r['regulation'];
                break;
            }
        }

        return is_null($quantity) ? false : $quantity;
    }

    public function set_stock_quantity($shop_id,$key,$data) {
        $this->stock_quantity[$shop_id][$data['product_id']] = $data['quantity'];
    }

    /**
     * @description 获取指定店铺的所有规则
     * @access public
     * @param void
     * @return void
     */
    public function getRegu($shop_id) {
        if(!$this->regu) {
            $filter = array(
                'start_time|sthan' =>self::$now,
                'end_time|bthan' =>self::$now,
                'using' =>'true',
                'al_exec' => 'false',
                'condition' => 'stock',
                'type'             => ['0','1','2'],
                'filter_sql' => "(shop_id='_ALL_' || FIND_IN_SET('{$shop_id}',shop_id) )",
            );
            $this->regu = $this->app->model('regulation_apply')->getList('*',$filter,0,-1,'type desc,priority desc');

            foreach($this->regu as $key=>$value){
                $this->regu[$key]['shop_id'] = explode(',',$value['shop_id']);
                $this->regu[$key]['apply_goods'] = explode(',',$value['apply_goods']);
                $this->regu[$key]['apply_pkg'] = explode(',',$value['apply_pkg']);
                $this->regu[$key]['apply_pko'] = explode(',',$value['apply_pko']);
                $this->regu[$key]['regulation'] = &$regulation[$value['regulation_id']];
                
                //回写方式
                $this->regu[$key]['sync_mode'] = $value['sync_mode'];
            }

            if($regulation){
                $rr = $this->app->model('regulation')->getList('*',array('regulation_id'=>array_keys($regulation),'using'=>'true'));
                foreach($rr as $r){
                    $regulation[$r['regulation_id']] = $r;
                }
            }
        }

        return $this->regu;
    }

    /**
     * @description 获取真正执行的规则
     * @access public
     * @param void
     * @return void
     */
    public function getExecRegu($pbn,$shop_id,$shop_bn) 
    {
        $product = kernel::single('inventorydepth_stock_products')->fetch_products($pbn);
        if(!$product) return '';
        $product_id = $product['sm_id'];

        $regu = $this->getRegu($shop_id);
        foreach($regu as $r){
            if(empty($r['regulation'])) continue;

            if((in_array($product_id,$r['apply_goods']) || $r['apply_goods'][0]=='_ALL_') && (in_array($shop_id,$r['shop_id']) || $r['shop_id'][0]=='_ALL_')) {

                # 判断是否满足规则
                //$valid = $this->valid();
                $params = array(
                    'shop_product_bn' => $pbn,
                    'shop_bn'         => $shop_bn,
                    'shop_id'         => $shop_id,
                );
                foreach ($r['regulation']['content']['filters'] as $filter) {
                    $allow_update = $this->check_condition($filter,$params);

                    if(!$allow_update){ continue 2;}
                }
                
                $exec_regu = array(
                    'regulation_id' => $r['regulation']['regulation_id'],
                    'heading' => $r['regulation']['heading'],
                );

                break;
            }
        }

        return $exec_regu;
    }

    /**
     * @description 获取5分钟内库存变更的货品
     * @access public
     * @param void
     * @return void
     */
    public function getChgProducts(){
        base_kvstore::instance('inventorydepth/apply')->fetch('read_store_lastmodify',$read_store_lastmodify);
        if (!$read_store_lastmodify || $read_store_lastmodify>self::$now) {
            $read_store_lastmodify = self::$now-self::intervalTime;
            base_kvstore::instance('inventorydepth/apply')->store('read_store_lastmodify',$read_store_lastmodify);
        }
        $filter = array(
            'max_store_lastmodify|between' => array(
                0 => $read_store_lastmodify,
                1 => self::$now,
            )
        );

        $this->_errmsg .= sprintf('s:%s,e:%s,',date('Y-m-d H:i:s', $read_store_lastmodify),date('Y-m-d H:i:s',self::$now));

        $this->readStoreLastmodify = $read_store_lastmodify;

        $products = array();
        $queue_limit = 200;
        $salesMaterialObj = app::get('material')->model('sales_material');
        $basicMaterialStockObj = app::get('material')->model('basic_material_stock');
        //根据变化的基础物料找到变化的销售物料
        $bm_ids = $basicMaterialStockObj->getList('bm_id', $filter);
        $bm_ids = array_map('current', $bm_ids);
        if($bm_ids){
            #获取绑定的销售物料sm_id
            $salesBasicMaterialObj = app::get('material')->model('sales_basic_material');
            $sm_ids = $salesBasicMaterialObj->getList('sm_id,bm_id', array('bm_id'=>$bm_ids));
            $filter_sm_ids = array();
            if(!empty($sm_ids)){
                foreach($sm_ids as $var_si){
                    if(!in_array($var_si["sm_id"],$filter_sm_ids)){
                        $filter_sm_ids[] = $var_si["sm_id"];
                    }
                }
            }
            //多选一类型的sm_ids获取
            $mdl_ma_pickone_ru = app::get('material')->model('pickone_rules');
            $rs_pickone = $mdl_ma_pickone_ru->getlist("sm_id",array("bm_id"=>$bm_ids));
            if(!empty($rs_pickone)){
                foreach($rs_pickone as $var_rp){
                    if(!in_array($var_rp["sm_id"],$filter_sm_ids)){
                        $filter_sm_ids[] = $var_rp["sm_id"];
                    }
                }
            }
            if(count($filter_sm_ids) > $queue_limit){ //走队列
                $queue_title = "定时变化库存自动回写";
                $queue_sm_ids = array_chunk($filter_sm_ids, $queue_limit);
                foreach($queue_sm_ids as $var_qsi){
                    $params = array(
                        "sm_ids"                => $var_qsi,
                        'read_store_lastmodify' => $read_store_lastmodify,
                    );
                    kernel::single('inventorydepth_queue')->timed_stock_sync_queue($queue_title,$params);
                }
            }else{
                if(!empty($filter_sm_ids)){
                    $products = $salesMaterialObj->getList('sm_id,sales_material_name,sales_material_bn, sales_material_type,shop_id',array('sm_id'=>$filter_sm_ids));
                }
            }
        }

        $this->_errmsg .= sprintf('apply_sm:%s,',implode('、', array_column($products, 'sales_material_bn')));

        return $products;
    }

    /**
     * @description 根据传入的货品推送前端店铺库存
     * @access public
     * @param void
     * @return void
     */
    public function syncShopStock($bm_ids){
        $products = array();
        $salesMaterialObj = app::get('material')->model('sales_material');
        if($bm_ids){
            #获取绑定的销售物料sm_id
            $salesBasicMaterialObj = app::get('material')->model('sales_basic_material');
            $sm_ids = $salesBasicMaterialObj->getList('sm_id,bm_id', array('bm_id'=>$bm_ids));
            $filter_sm_ids = array();
            if(!empty($sm_ids)){
                foreach($sm_ids as $var_si){
                    if(!in_array($var_si["sm_id"],$filter_sm_ids)){
                        $filter_sm_ids[] = $var_si["sm_id"];
                    }
                }
            }
            //多选一类型的sm_ids获取
            $mdl_ma_pickone_ru = app::get('material')->model('pickone_rules');
            $rs_pickone = $mdl_ma_pickone_ru->getlist("sm_id",array("bm_id"=>$bm_ids));
            if(!empty($rs_pickone)){
                foreach($rs_pickone as $var_rp){
                    if(!in_array($var_rp["sm_id"],$filter_sm_ids)){
                        $filter_sm_ids[] = $var_rp["sm_id"];
                    }
                }
            }
            if(!empty($filter_sm_ids)){
                $products = $salesMaterialObj->getList('sm_id,sales_material_name,sales_material_bn, sales_material_type,shop_id',array('sm_id'=>$filter_sm_ids));
            }
        }
        $this->do_sync_products_stock($products);
        return true;
    }
    
    public function do_sync_products_stock($products)
    {
        if(empty($products)){
            return;
        }
        
        //“触发类型”增加配置开关，是否需过滤掉“销售出库”类型下产生的库存异动
        $this->filterUpdateStockMaterial($products);
        
        $products_normal = $products_pkg = $products_pko = array();
        foreach ($products as $key => $val){
            if($val['sales_material_type'] == 2){ //促销
                $products_pkg[]    = $val;
            }elseif($val['sales_material_type'] == 5){ //多选一
                $products_pko[] = $val;
            }else{
                $products_normal[] = $val;
            }
        }
        kernel::single('inventorydepth_calculation_basicmaterial')->init($products);
        kernel::single('inventorydepth_calculation_salesmaterial')->init($products);
        # 获取已经连接的店铺
        $filter = array(
            'filter_sql' =>'{table}node_id is not null and {table}node_id !=""',
        );
        $shops = $this->app->model('shop')->getList('shop_id,shop_bn,node_type,business_type',$filter);
                
        foreach($shops as $shop)
        {
            //是否安装drm模块
            if (app::get('drm')->is_installed()) {
                //获取淘管店铺信息
                $channelShopObj = app::get('drm')->model('channel_shop');
                $binds = array();
                $binds = $channelShopObj->getList('channel_id',array('shop_id'=>$shop['shop_id']),0,1);
                if(is_array($binds) && !empty($binds)) {
                    continue;
                }
            }
            
            # 店铺未开启回写
            $request = kernel::single('inventorydepth_shop')->getStockConf($shop['shop_id']);
            if($request != 'true') { continue; }
            kernel::single('inventorydepth_offline_queue')->store_update($products_normal, $shop);
            
            # 仓库为该店铺供货
            $bra = kernel::single('inventorydepth_shop')->getBranchByshop($shop['shop_bn']);
            if (!$bra) { continue; }
            
            # 读取已经匹配，但不需要回写的货品
            $unRequest = kernel::single('inventorydepth_sync_set')->getUnRequestBn($shop, $products);
            
            //[抖音平台]获取分仓独立更新库存
            $warehouse_mode = $this->isWarehouseRegu($shop);
            if($warehouse_mode){
                //按仓库回写库存
                $this->syncWarehouseStocks($shop, $products_normal, $unRequest);
            }else{
                //默认方式
                $this->syncProductStocks($shop, $products, $unRequest);
            }
        }
        
        if ($this->reguUpdateFilter) {
            $this->app->model('regulation_apply')->update(array('al_exec'=>'true','exec_time'=>self::$now), $this->reguUpdateFilter);
        }
    }
    
    /**
     * 回写商品库存
     * 
     * @param array $shop 单个店铺信息
     * @param array $products_normal 基础物料数组
     * @param array $unRequest 不用回写的商品数组
     * @return bool
     */
    public function syncProductStocks($shop, $products, $unRequest)
    {
        //清除上一个店铺的规则
        unset($this->regu);
        
        $skusObj = app::get('inventorydepth')->model('shop_skus');
        
        $sales_bn_list = array();
        
        $stocks = $omnichannel_stocks = $omnichannel_bns = array();
        
        //根据普通销售物料识别是否是全渠道商品
        if(app::get('tbo2o')->is_installed()){
            //获取全渠道主店铺shop_id
            $tbo2o_shop = kernel::single('tbo2o_common')->getTbo2oShopInfo();
            //判断当前店铺是否是全渠道主店铺
            if($shop['shop_id'] == $tbo2o_shop["shop_id"]){
                foreach($products as $product){
                    if(in_array($val['sales_material_type'], ['2','5'])){ //促销,多选一
                        continue;
                    }
                    $tmp_sm_ids[] = $product['sm_id'];
                    $tmp_sm_arr[$product['sm_id']] = $product['sales_material_bn'];
                }
                //取销售物料对应的基础物料
                $salesMaterialLib = kernel::single('material_sales_material');
                $sm_and_bms = $salesMaterialLib->getBmIdsBySmIds($tmp_sm_ids);
                foreach($sm_and_bms as $sm => $bm){
                    $tmp_bm_ids[] = $bm;
                }
                //识别基础物料是否是全渠道 拿出是全渠道的基础物料
                $basicMaterialLib = kernel::single('material_basic_material');
                $omnichannel_bm_ids = $basicMaterialLib->isOmnichannelBms($tmp_bm_ids);
                //识别销售物料是否全渠道
                foreach($sm_and_bms as $sm => $bm){
                    if(in_array($bm, $omnichannel_bm_ids)){
                        $omnichannel_bns[] = $tmp_sm_arr[$sm];
                    }
                }
            }
        }
        foreach($products as $product){
            if ($unRequest && in_array($product['sales_material_bn'],$unRequest)) { continue; }
            $st = $this->getStock($product['sales_material_bn'],$shop['shop_id'],$shop['shop_bn'],$shop['node_type']);
            if ($st === false) { continue; }
            //存在全渠道并且当前物料属于全渠道
            if($omnichannel_bns && in_array($product['sales_material_bn'], $omnichannel_bns)){
                $omnichannel_stocks[] = $st;
            }else{
                //普通线上商品
                $stocks[] = $st;
            }
            
            //普通商品列表
            $sales_bn_list[] = $product['sales_material_bn'];
        }
        
        $stocks = $this->resetChangeStocks($stocks, $shop, $sales_bn_list);
        
        //回写个数
        $stock_nums = 50;
        if($shop['node_type'] == 'dewu'){
            $stock_nums = 10;
        }elseif($shop['node_type'] == 'aikucun'){
            //爱库存限制每次最多30个
            $stock_nums = 30;
        }
        
        //往前端回写库存
        if ($stocks) {
            $new_stocks = array_chunk($stocks, $stock_nums);
            foreach ($new_stocks as $stock) {
                kernel::single('inventorydepth_shop')->doStockRequest($stock,$shop['shop_id']);
            }
        }
        
        //全渠道商品电商仓库存回写
        if($omnichannel_stocks){
            //全量阿里全渠道主店铺库存
            $new_omnichannel_stocks = array_chunk($omnichannel_stocks,50);
            foreach ($new_omnichannel_stocks as $omnichannel_stock){
                $param = kernel::single('tbo2o_common_tbo2oapi')->getO2oInvInitialRequestParam($omnichannel_stock,$tbo2o_shop);
                kernel::single('tbo2o_event_trigger_store')->storeinventoryIteminitial($param);
            }
        }
        
        return true;
    }
    
    /**
     * 获取分仓回写库存规则(按仓库纬度回写)
     *
     * @param array $shopInfo
     * @return array
     */
    public function isWarehouseRegu($shopInfo)
    {
        //支持按仓库回写的平台
        $nodeTypeList = array('luban', 'vop');
        
        //shop
        $shop_id = $shopInfo['shop_id'];
        $node_type = $shopInfo['node_type'];
        
        //check允许按仓库回写的平台
        if($node_type == 'taobao') {
            if($shopInfo['business_type'] !='maochao') {
                return false;
            }
        } elseif(!in_array($node_type, $nodeTypeList)){
            return false;
        }
        
        //获取指定店铺的所有规则
        $filter = array(
                'start_time|sthan' => self::$now,
                'end_time|bthan' => self::$now,
                'using' => 'true',
                'al_exec' => 'false',
                'condition' => 'stock',
                'sync_mode' => 'warehouse', //指定仓回写库存
                'filter_sql' => "(shop_id='_ALL_' || FIND_IN_SET('{$shop_id}',shop_id) )",
        );
        $regu = $this->app->model('regulation_apply')->getList('*', $filter, 0, 1, 'type desc,priority desc');
        if(empty($regu)){
            return false;
        }
        
        return true;
    }
    
    /**
     * 回写商品库存
     *
     * @param array $shop 单个店铺信息
     * @param array $products_normal 基础物料数组
     * @param array $unRequest 不用回写的商品数组
     * @return bool
     */
    public function syncWarehouseStocks($shop, $products_normal, $unRequest)
    {
        //清除上一个店铺的规则
        unset($this->regu);
        
        $skusObj = app::get('inventorydepth')->model('shop_skus');
        
        $stocks = array();
        $sales_bn_list = array();
        
        //暂时没有全渠道商品逻辑
        
        //普通商品
        foreach($products_normal as $product)
        {
            if ($unRequest && in_array($product['sales_material_bn'], $unRequest)) { continue; }
            
            $stList = $this->getWarehouseStock($product['sales_material_bn'], $shop);
            if ($stList === false) { continue; }
            
            //普通商品库存结果
            foreach ($stList as $key => $items)
            {
                $stocks[] = $items;
            }
            
            //普通商品列表
            $sales_bn_list[] = $product['sales_material_bn'];
        }
        
        //促销捆绑类
        if (is_array(inventorydepth_stock_pkg::$pkg)) {
            foreach (inventorydepth_stock_pkg::$pkg as $pkgValue)
            {
                if ($unRequest && in_array($pkgValue['sales_material_bn'], $unRequest)) { continue; }
                
                $stList = kernel::single('inventorydepth_logic_pkgstock')->getWarehouseStock($pkgValue['sales_material_bn'], $shop);
                if($stList === false) { continue; }
                
                //捆绑商品库存结果
                foreach ($stList as $key => $items)
                {
                    $stocks[] = $items;
                }
                
                //捆绑商品列表
                $sales_bn_list[] = $pkgValue['sales_material_bn'];
            }
        }
        
        //多选一
        if(is_array(inventorydepth_stock_pko::$pko)){
            foreach (inventorydepth_stock_pko::$pko as $pkoValue)
            {
                if ($unRequest && in_array($pkoValue['sales_material_bn'], $unRequest)) { continue; }
                
                $stList = kernel::single('inventorydepth_logic_pkostock')->getWarehouseStock($pkoValue['sales_material_bn'], $shop);
                if($stList === false) { continue; }
                
                //多选一商品库存结果
                foreach ($stList as $key => $items)
                {
                    $stocks[] = $items;
                }
                
                //多选一商品列表
                $sales_bn_list[] = $pkoValue['sales_material_bn'];
            }
        }
        
        $stocks = $this->resetChangeStocks($stocks, $shop, $sales_bn_list);
        
        //往前端回写库存
        if ($stocks) {

            $this->_errmsg .= sprintf('run_sm:%s,',implode('、',array_column($stocks, 'bn')));
            $new_stocks = array_chunk($stocks,50);
            foreach ($new_stocks as $stock) {
                kernel::single('inventorydepth_shop')->doStockRequest($stock, $shop['shop_id']);
            }
        }
        
        return true;
    }
    
    /**
     * [仓库级]计算普通商品库存
     *
     * @param string $product_bn 普通货号
     * @param array $shopInfo 店铺信息
     * @return array
     */
    public function getWarehouseStock($product_bn, $shopInfo)
    {
        $shop_id = $shopInfo['shop_id'];
        $shop_bn = $shopInfo['shop_bn'];
        $node_type = $shopInfo['node_type'];
        
        //读取商品要执行的规则
        $storeList = $this->dealWarehouseWithRegu($product_bn, $shop_id, $shop_bn);
        if ($storeList === false) { return false; }
        
        $params = array(
                'shop_product_bn' => $product_bn,
                'shop_bn' => $shop_bn,
                'shop_id' => $shop_id,
        );
        
        //店铺冻结
        $stockCalLib = kernel::single('inventorydepth_stock_calculation');
        $store_freeze = call_user_func_array(array($stockCalLib,'get_shop_freeze'), $params);
        $tmp_product = kernel::single('inventorydepth_stock_products')->fetch_products($product_bn);
        
        $memo = array(
                'store_freeze' => $store_freeze,
                'last_modified' => $tmp_product['last_modified'],
        );
        
        //stocks
        $stocks = array();
        foreach ($storeList as $warehouse_bn => $quantity)
        {
            // 受1号店回写库存限制
            if($node_type == 'yihaodian' && $quantity >= 3000){
                $quantity = 2999;
            }
            
            //按仓库纬度回写
            if(!empty($warehouse_bn)){
                $stock = array(
                        'bn' => $product_bn,
                        'quantity' => $quantity,
                        'branch_bn' => $warehouse_bn, //仓库编码
                );
            }else{
                //默认方式回写
                $stock = array(
                        'bn' => $product_bn,
                        'quantity' => $quantity,
                        'memo' => json_encode($memo),
                );
            }

            // 查询增量库存
            if (kernel::single('inventorydepth_sync_set')->isModeSupportInc($node_type)) {
                $stockLogMdl   = app::get('ome')->model('api_stock_log');
                $last_quantity = $stockLogMdl->getLastStockLog($shop_id, $product_bn);
                if ($last_quantity) {
                    $stock['inc_quantity'] = $quantity - $last_quantity['store'];
                }
            }
            $stocks[] = $stocks;
        }
        
        return $stocks;
    }
    
    /**
     * [库存级]根据库存规则读取商品库存
     * 
     * @param string $pbn
     * @param string $shop_id
     * @param string $shop_bn
     * @param array $apply_regulation
     * @return array
     */
    public function dealWarehouseWithRegu($pbn, $shop_id, $shop_bn, &$apply_regulation=array())
    {
        $product = kernel::single('inventorydepth_stock_products')->fetch_products($pbn);
        $product_id = $product['sm_id'];
        
        $regu = $this->getRegu($shop_id);
        
        //stocks
        $storeList = array();
        foreach($regu as $r)
        {
            //不是按仓库回写,则跳过
            if($r['sync_mode'] != 'warehouse'){
                continue;
            }
            
            if(empty($r['regulation'])) continue;
            
            //check店铺和商品是否满足规则
            $shop_flag = false;
            if($r['shop_id'][0] == '_ALL_'){
                $shop_flag = true;
            }elseif(in_array($shop_id, $r['shop_id'])){
                $shop_flag = true;
            }
            
            $goods_flag = false;
            if($r['apply_goods'][0] == '_ALL_'){
                $goods_flag = true;
            }elseif(in_array($product_id, $r['apply_goods'])){
                $goods_flag = true;
            }
            
            if($shop_flag && $goods_flag){
                //定时回写
                if ($r['style'] == 'fix') {
                    $this->reguUpdateFilter['id'][] = $r['id'];
                }
                
                //判断是否满足规则
                $params = array(
                        'shop_product_bn' => $pbn,
                        'shop_bn' => $shop_bn,
                        'shop_id' => $shop_id,
                );
                
                foreach ($r['regulation']['content']['filters'] as $filter)
                {
                    $allow_update = $this->check_condition($filter,$params);
                
                    if(!$allow_update){ continue 2;}
                }
                
                if ($r['regulation']['content']['stockupdate'] != 1) { return false;}
                
                //按仓库纬度回写
                $params['sync_mode'] = 'warehouse'; //按仓库纬度回写标识
                $type = 'warehouse_'; //todo:要带入_下划线
                
                $branchQuantity = kernel::single('inventorydepth_stock')->formulaRun($r['regulation']['content']['result'], $params, $msg, $type);
                if ($branchQuantity === false){ continue; }
                
                if (!empty($this->product_list)) {
                    $new_product= array_column($this->product_list,null,'sm_id');
                    //如果是规则等于订单的并且 stock_status == false continue
                    if (isset($new_product[$product_id]['exit_io']) && !$new_product[$product_id]['exit_io'] && $r['style'] == 'order_change') {
                        return false;
                    }
                }
                
                $storeList = $branchQuantity; //一维数组,以仓库编码为下标
                
                $apply_regulation = $r['regulation'];
                
                break;
            }
        }
        
        return empty($storeList) ? false : $storeList;
    }
    
    /**
     * 库存规则应用页面，“触发类型”增加配置开关，是否需过滤掉“销售出库”类型下产生的库存异动
     * @param $products
     * @return bool
     */
    public function filterUpdateStockMaterial($products)
    {
        if (empty($products)) {
            return false;
        }
        $read_store_time = $this->readStoreLastmodify + 3600 > self::$now ? $this->readStoreLastmodify : self::$now-self::intervalTime;

        // if ($read_store_time == 0) {
        //     $read_store_time = self::$now-self::intervalTime;
        // }


        $basicMaterialObj       = app::get('material')->model('basic_material');
        $ioStockObj             = app::get('ome')->model('iostock');
        $salesBasicMaterialObj  = app::get('material')->model('sales_basic_material');

        $sm_ids         = $salesBasicMaterialObj->getList('bm_id,sm_id', array('sm_id'=>array_column($products,'sm_id')));
        $basicList      = $basicMaterialObj->getList('material_bn,bm_id', array('bm_id' => array_column($sm_ids,'bm_id')));

        if ($basicList) {
            $bm_id_list   = array_column($basicList, null, 'bm_id');
            $material_bn  = array_column($basicList, 'material_bn');
            $sm_id_list   = array_column($sm_ids, null, 'sm_id');

            $iostock_list = $ioStockObj->getList('distinct bn', array(
                'create_time|sthan' => self::$now,
                'create_time|bthan' => $read_store_time,
                'bn'                  => $material_bn,
                'type_id|noequal'     => 3
            ));
            $smChange = app::get('inventorydepth')->model('shop_skustockset')->getList('shop_product_bn',[
                'shop_product_bn'=>array_column($products, 'sales_material_bn'),
                'last_modify|sthan' => self::$now,
                'last_modify|bthan' => $read_store_time,
            ]);
            $smChange = array_column($smChange, 'shop_product_bn');
        
            $iostock_bn = array();
            if ($iostock_list) {
                $iostock_bn    = array_column($iostock_list, 'bn');
            }
        }
        foreach ($products as $key => $value) {
            $exit_io = false;
            if($smChange) {
                if(in_array($value['sales_material_bn'], $smChange)) {
                    $exit_io = true;
                }
            }
            //查到销售物料和基础物料关联
            if (isset($sm_id_list[$value['sm_id']])) {
                $sm_id = $sm_id_list[$value['sm_id']];
                //查询基础物料编码
                if (isset($bm_id_list[$sm_id['bm_id']])) {
                    $bm_id = $bm_id_list[$sm_id['bm_id']];
                    //判断当前基础物料编码是否在iostock存在
                    if (in_array($bm_id['material_bn'], $iostock_bn)) {
                        $exit_io = true;
                    }
                }
            }
            $products[$key]['exit_io'] = $exit_io;

            $this->_errmsg .= sprintf('io_sm:%s-%s,',$value['sales_material_bn'],(int)$exit_io);
        }
        $this->product_list = $products;
    }

    public function get_errmsg()
    {
        return $this->_errmsg;
    }

    public function set_readStoreLastmodify($readStoreLastmodify)
    {
        $this->readStoreLastmodify = $readStoreLastmodify;

        return $this;
    }

    public function resetChangeStocks($stocks, $shop, $sales_bn_list) {
        $skusObj = app::get('inventorydepth')->model('shop_skus');
        #剔除请求成功得相同数据物料
        $stocks = $this->eliminateSameNumber($stocks, $shop, $sales_bn_list);
        if(empty($stocks)) {
            return [];
        }
        //[抖音平台]加入sku_id字段
        if(in_array($shop['node_type'], array('luban'))
            || kernel::single('inventorydepth_sync_set')->isUseSkuid($shop)
        ){
            $tempList = $skusObj->getList('shop_product_bn,shop_sku_id,id,request', array('shop_id'=>$shop['shop_id'], 'shop_product_bn'=>$sales_bn_list));
            
            $skusList = array();
            foreach ((array)$tempList as $key => $val)
            {
                if($val['request'] != 'true') {
                    continue;
                }
                $shop_product_bn = $val['shop_product_bn'];
                
                //一个货号对应多个sku_id的场景
                $skusList[$shop_product_bn][] = $val;
            }
            
            $stockList = array();
            if($stocks && $skusList){
                foreach ($stocks as $key => $val)
                {
                    $shop_product_bn = $val['bn'];
                    
                    if($skusList[$shop_product_bn]){
                        foreach ($skusList[$shop_product_bn] as $skuKey => $skuVal)
                        {
                            $tmp = $val;
                            $tmp['sku_id'] = $skuVal['shop_sku_id'];
                            if($val['branch_bn']) {
                                $plateSet = app::get('inventorydepth')->model('shop_skustockset')->db_dump(['branch_bn'=>$val['branch_bn'], 'skus_id'=>$skuVal['id']], 'stock_only');
                                if($plateSet) {
                                    $tmp['stock_only'] = $plateSet['stock_only'];
                                }
                            }
                            $stockList[] = $tmp;
                        }
                    }else{
                        $stockList[] = $val;
                    }
                }
                
                //重新赋值
                $stocks = $stockList;
            }
            
            //去除下标
            $stocks = array_values($stocks);
            
            unset($tempList, $skusList, $stockList);
        } elseif (in_array($shop['node_type'], array('vop'))) {
            // 判断是否为唯品会省仓，如果是省仓，找到省仓对应关系

            // $shop = [shop_id,shop_bn,node_type,business_type]
            $tmp_stocks = [];
            list($stocks, $tmp_stocks) = [$tmp_stocks, $stocks];

            $branchBnArr = array_column($tmp_stocks, 'branch_bn');
            if (!$branchBnArr) {
                return $tmp_stocks;
            }
            $branchList  = app::get('ome')->model('branch')->getList('*', ['branch_bn|in' => $branchBnArr, 'check_permission' => 'false']);
            $branchList  = array_column($branchList, null, 'branch_bn');

            // 仓库关联的店铺
            $branchRelationList = app::get('ome')->getConf('shop.branch.relationship');
            $branchRelationList = $branchRelationList[$shop['shop_bn']];

            $branchIdArr = array_keys($branchRelationList);
            $relation    = app::get('ome')->model('branch_relation')->getList('*', ['branch_id|in' => $branchIdArr, 'type' => 'vopjitx']);
            if ($relation) {
                $relation = array_column($relation, 'relation_branch_bn', 'branch_id');

                // 获取唯品会省仓列表
                $warehouseMdl  = app::get('console')->model('warehouse');
                $warehouseList = $warehouseMdl->getList('*', ['branch_bn|in' => $relation, 'warehouse_type' => '2']);
                $warehouseList = array_column($warehouseList, null, 'branch_bn');

                // 获取商品条码
                $materialCodeLib = kernel::single('material_codebase');
                $materCodeList   = $materialCodeLib->getBarcodeBybn(array_column($tmp_stocks, 'bn'));

                foreach ($tmp_stocks as $k => $v) {
                    $branch_id      = $branchList[$v['branch_bn']]['branch_id'];
                    $vop_branch_bn  = $relation[$branch_id];
                    $warehouse_type = $warehouseList[$vop_branch_bn]['warehouse_type'];
                    $cooperation_no = $warehouseList[$vop_branch_bn]['cooperation_no'];
                    if ($vop_branch_bn && $warehouse_type == '2' && $cooperation_no) {
                        $stocks[$k]                     = $v;
                        $stocks[$k]['warehouse_code']   = $vop_branch_bn;
                        $stocks[$k]['warehouse_type']   = $warehouse_type;
                        $stocks[$k]['cooperation_no']   = $cooperation_no;
                        $stocks[$k]['barcode']          = $materCodeList[$v['bn']] ? $materCodeList[$v['bn']] : ''; // 回传匹配店铺资源的商品用barcode去匹配
                        $stocks[$k]['warehouse_flag']   = '1'; // 仓库标识 0：全国逻辑仓或7大仓 1：省仓 不填默认0
                    }
                }
                $stocks = array_values($stocks);
            }

            // 如果stocks是空，说明没有有效的省仓，继续返回原始数据
            if (!$stocks) {
                list($stocks, $tmp_stocks) = [$tmp_stocks, $stocks];
            }
        }
        return $stocks;
    }

    public function eliminateSameNumber($stocks, $shop, $sales_bn_list) {
        $stockApi = [];
        $stockApiModel = app::get('ome')->model('api_stock_log');
        foreach ($stockApiModel->getList('shop_id,product_bn,store,actual_stock,msg,last_modified,status',array('product_bn'=>$sales_bn_list, 'shop_id'=>$shop['shop_id'])) as $value) {
            $index = $value['shop_id'] . "-" .$value['product_bn'];
            $stockApi[$index] = $value;
        }
        foreach($stocks as $k => $st) {
            $stockapi_code = $shop['shop_id'] . "-" . $st['bn'];
            if(isset($stockApi[$stockapi_code]['actual_stock'])
                && $stockApi[$stockapi_code]['actual_stock'] == $st['actual_stock']
                && $stockApi[$stockapi_code]['status'] == 'success'
                && !kernel::single('inventorydepth_stock')->getNeedUpdateSku($shop['shop_id'], $st['bn'])
            ) {
                unset($stocks[$k]);
            }
        }
        return $stocks;
    }
}
