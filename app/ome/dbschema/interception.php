<?php

$db['interception'] = array(
    'columns' =>
        array(
            'id' =>
                array(
                    'type' => 'int unsigned',
                    'required' => true,
                    'pkey' => true,
                    'editable' => false,
                    'extra' => 'auto_increment',
                ),
            'delivery_id' =>
                array(
                    'type' => 'table:delivery@ome',
                    'required' => true,
                    'default' => 0,
                    'editable' => false,
                ),
            'delivery_bn' =>
                array(
                    'type' => 'varchar(32)',
                    'required' => true,
                    'label' => '发货单号',
                    'comment' => '配送流水号',
                    'editable' => false,
                    'width' => 140,
                    'searchtype' => 'nequal',
                    'filtertype' => 'yes',
                    'filterdefault' => true,
                    'in_list' => true,
                    'default_in_list' => true,
                    'is_title' => true,
                ),
            'order_id' =>
                array(
                    'type' => 'table:orders@ome',
                    'required' => true,
                    'default' => 0,
                    'editable' => false,
                ),
            'order_bn' =>
                array(
                    'type' => 'varchar(32)',
                    'required' => true,
                    'label' => '订单号',
                    'comment' => '订单号',
                    'editable' => false,
                    'width' => 140,
                    'searchtype' => 'nequal',
                    'filtertype' => 'yes',
                    'filterdefault' => true,
                    'in_list' => true,
                    'default_in_list' => true,
                    'is_title' => true,
                ),
            'reship_id' =>
                array(
                    'type' => 'table:reship@ome',
                    'required' => false,
                    'default' => 0,
                    'editable' => false,
                ),
            'reship_bn' =>
                array(
                    'type' => 'varchar(32)',
                    'required' => false,
                    'default' => '',
                    'label' => '退货单号',
                    'comment' => '退货单号',
                    'editable' => false,
                    'width' => 140,
                    'searchtype' => 'nequal',
                    'filtertype' => 'yes',
                    'filterdefault' => true,
                    'in_list' => true,
                    'default_in_list' => true,
                    'is_title' => true,
                ),
            'logi_id' =>
                array(
                    'type' => 'table:dly_corp@ome',
                    'comment' => '物流公司ID',
                    'editable' => false,
                    'label' => '物流公司',
                    'filtertype' => 'normal',
                    'filterdefault' => true,
                ),
            'logi_name' =>
                array(
                    'type' => 'varchar(255)',
                    'label' => '物流公司',
                    'comment' => '物流公司名称',
                    'editable' => false,
                    'width' => 75,
                    'in_list' => true,
                    'default_in_list' => true,
                ),
            'logi_no' =>
                array(
                    'type' => 'varchar(100)',
                    'label' => '原物流单号',
                    'comment' => '原物流单号',
                    'editable' => false,
                    'width' => 110,
                    'in_list' => true,
                    'default_in_list' => true,
                    'filtertype' => 'normal',
                    'filterdefault' => true,
                    'searchtype' => 'nequal',
                ),
            'return_logi_no' =>
                array(
                    'type' => 'varchar(100)',
                    'label' => '退回物流单号',
                    'comment' => '退回物流单号',
                    'editable' => false,
                    'width' => 110,
                    'in_list' => true,
                    'default_in_list' => true,
                    'filtertype' => 'normal',
                    'filterdefault' => true,
                    'searchtype' => 'nequal',
                ),
            'status' =>
                array(
                    'type' =>
                        array(
                            'succ' => '拦截成功',
                            'failed' => '拦截失败',
                            'ready' => '待处理',
                            'running' => '拦截中',
                            'return_failed' => '退货单创建失败',
                            'cancel_succ' => '取消成功',
                            'cancel' => '拦截取消',
                        ),
                    'default' => 'ready',
                    'width' => 150,
                    'required' => true,
                    'comment' => '拦截状态',
                    'editable' => false,
                    'label' => '拦截状态',
                    'in_list' => true,
                    'default_in_list' => true,

                ),
            'memo' =>
                array(
                    'type' => 'longtext',
                    'label' => '备注',
                    'comment' => '备注',
                    'editable' => false,
                    'in_list' => true,
                ),
            'branch_id' =>
                array(
                    'type' => 'table:branch@ome',
                    'editable' => false,
                    'label' => '发货仓库',
                    'width' => 110,
                    'filtertype' => 'normal',
                    'filterdefault' => true,
                    'in_list' => true,
                    'panel_id' => 'delivery_finder_top',
                ),
            'delivery_time' =>
                array(
                    'type' => 'time',
                    'label' => '发货时间',
                    'comment' => '发货时间',
                    'editable' => false,
                    'filtertype' => 'time',
                    'filterdefault' => true,
                    'in_list' => true,
                    'default_in_list' => true,
                ),
            'shop_id' =>
                array(
                    'type' => 'table:shop@ome',
                    'label' => '来源店铺',
                    'width' => 75,
                    'editable' => false,
                    'in_list' => true,
                    'default_in_list' => true,
                    'filtertype' => 'normal',
                ),
            'interception_time' =>
                array(
                    'type' => 'time',
                    'label' => '拦截单发起时间',
                    'width' => 130,
                    'editable' => false,
                    'filtertype' => 'time',
                    'in_list' => true,
                    'default_in_list' => true,
                ),
            'email_status' =>
                array(
                    'type' => array(
                        '0' => '未发送',
                        '1' => '已发送',
                        '2' => '发送失败',
                    ),
                    'default' => '0',
                    'label' => '拦截失败邮件',
                    'editable' => false,
                    'in_list' => true,
                    'default_in_list' => false,
                    'filtertype' => 'normal',
                    'filterdefault' => true,
                    'searchtype' => 'nequal',
                ),
            'error_msg' =>
                array(
                    'type' => 'varchar(255)',
                    'label' => '错误信息',
                    'width' => 130,
                    'editable' => false,
                    'in_list' => true,
                    'default_in_list' => false,
                ),
            'shop_type' =>
                array(
                    'type' => 'varchar(50)',
                    'label' => '店铺类型',
                    'width' => 75,
                    'editable' => false,
                    'in_list' => true,
                    'default_in_list' => true,
                ),
            'retry_count' =>
                array(
                    'type' => 'number',
                    'label' => '重试次数',
                    'width' => 75,
                    'default' => 0,
                    'editable' => false,
                    'in_list' => true,
                    'default_in_list' => true,
                ),
            'create_time' =>
                array(
                    'type' => 'time',
                    'label' => '拦截单创建时间',
                    'comment' => '拦截单创建时间',
                    'editable' => false,
                    'filtertype' => 'time',
                    'in_list' => true,
                    'default_in_list' => true,
                ),
            'last_modified' =>
                array(
                    'label' => '最后更新时间',
                    'type' => 'last_modify',
                    'editable' => false,
                    'in_list' => true,
                ),
            'return_id' =>
                array(
                    'type' => 'table:return_product@ome',
                    'required' => false,
                    'default' => 0,
                    'editable' => false,
                ),
            'parent_return_id'=>array(
                'type' => 'int(11)',
                'label' => '父节点退货单',
                'width' => 130,
                'editable' => false,
                'in_list' => true,
                'default_in_list' => false,
                'default' => 0,
            ),
            ),
    'index' =>
        array(
            'ind_delivery_bn' =>
                array(
                    'columns' =>
                        array(
                            0 => 'delivery_bn',
                        ),
                ),
            'ind_order_bn' =>
                array(
                    'columns' =>
                        array(
                            0 => 'order_bn',
                        ),
                ),
            'ind_reship_bn' =>
                array(
                    'columns' =>
                        array(
                            0 => 'reship_bn',
                        ),
                ),
            'ind_status' =>
                array(
                    'columns' =>
                        array(
                            0 => 'status',
                        ),
                ),
            'ind_logi_no' =>
                array(
                    'columns' =>
                        array(
                            0 => 'logi_no',
                        ),
                ),
            'ind_return_logi_no' =>
                array(
                    'columns' =>
                        array(
                            0 => 'return_logi_no',
                        ),
                ),
            'ind_email_status' =>
                array(
                    'columns' =>
                        array(
                            0 => 'email_status',
                        ),
                ),
            'ind_shop_type' =>
                array(
                    'columns' =>
                        array(
                            0 => 'shop_type',
                        ),
                ),
            'ind_retry_count' =>
                array(
                    'columns' =>
                        array(
                            0 => 'retry_count',
                        ),
                ),
        ),
    'engine' => 'innodb',
    'version' => '$Rev: 41996',
);
