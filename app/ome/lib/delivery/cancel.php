<?php
/**
 * 取消发货单
 *
 * <AUTHOR>
 * @version 2012-5-10 14:03
 * @package delivery
 *
 */
class ome_delivery_cancel
{
    /**
     * 仅退款创建发货单
     * @param $params
     */
    public function refund_create($params,$esb_data)
    {
        $order_id = $params['order_id'];
        $refund_item_list = $params['refund_item_list'];

        $order_obj = app::get('ome')->model('orders');
        $order_objects_obj = app::get('ome')->model('order_objects');
        $order_items_obj = app::get('ome')->model('order_items');
        $delivery_order_obj = app::get('ome')->model('delivery_order');
        $delivery_obj = app::get('ome')->model('delivery');
        $delivery_items_detail_obj = app::get('ome')->model('delivery_items_detail');
        $delivery_items_obj = app::get('ome')->model('delivery_items');
        $wap_delivery_obj = app::get('wap')->model('delivery');
        $wap_delivery_items_obj = app::get('wap')->model('delivery_items');

        $order_info = $order_obj->dump(['order_id'=>$order_id]);

        $order_items = [];
        if($refund_item_list) {
            //1.先去对应 oid ,如果没有 oid ,就去匹配 bn
            foreach ($refund_item_list as $item_info) {
                $item_oid = $item_info['oid'];
                $item_bn = $item_info['bn'];
                if ($item_oid) {
                    $sql = " select * from sdb_ome_delivery_items_detail where order_id = $order_id and order_obj_id in 
                               (select obj_id from sdb_ome_order_objects where oid = '$item_oid' and order_id = $order_id ) ";

                    $delivery_details = $delivery_items_detail_obj->db->selectrow($sql);
                    if (!$delivery_details) {
                        //如果没有发货
                        $sql_items = " select * from sdb_ome_order_items where order_id = $order_id and obj_id in 
                               (select obj_id from sdb_ome_order_objects where oid = '$item_oid' and order_id = $order_id ) ";

                        $res_items = $order_items_obj->db->select($sql_items);
                        $order_items = array_merge($order_items, $res_items);
                    }

                } else if ($item_bn) {
                    $delivery_details = $delivery_items_detail_obj->getList('*', ['order_id' => $order_id, 'bn' => $item_bn]);
                    if (!$delivery_details) {
                        $res_items = $order_items_obj->getList('*', ['order_id' => $order_id, 'bn' => $item_bn]);
                        $order_items = array_merge($order_items, $res_items);
                    }
                }
            }
        }else{
            $delivery_order_list = $delivery_order_obj->getList('*', ['order_id' => $order_id]);
            if (!$delivery_order_list) {
                $order_items = $order_items_obj->getList('*', ['order_id' => $order_id]);
            }
        }

        if(!$order_items){
            return ['status'=>'succ','msg'=>'商品已经发货'];
        }


        $opInfo = kernel::single('ome_func')->get_system();
        $item_num = count($order_items);

        $bnsContentArr = [];
        $store_id = 0;
        foreach ($order_items as $item_info) {
            $bnsContentArr[$item_info['product_id']] = $item_info['bn'];
            $store_id = $item_info['fulfillment_store_id'];
        }

        $branch_info = app::get('ome')->model('branch')->dump(['store_id'=>$store_id]);
        if(!$branch_info){
            return array('rsp' => 'fail', 'msg' => '没有找到对应的发货仓库');
        }

        $cur_time = $order_info['createtime'];
        $bnsContent = serialize($bnsContentArr);
        $ome_delivery_data = [
            'delivery_bn'=>$delivery_obj->gen_id(),
            'skuNum'=>$item_num,
            'itemNum'=>$item_num,
            'status'=>'cancel',
            'op_id'=>$opInfo['op_id'],
            'sync_status'=>'5',
            'logi_status'=>'8',
            'bnsContent'=>$bnsContent,
            'branch_id'=>$branch_info['branch_id'],
            'shop_id'=>$order_info['shop_id'],
            'ship_name'=>$order_info['consignee']['name'],
            'ship_addr'=>$order_info['consignee']['addr'],
            'esb_pmt_amount'=>$esb_data['esb_pmt_amount'],
            'esb_amount'=>$esb_data['esb_amount'],
            'order_createtime'=>$cur_time,
            'create_time'=>$cur_time
        ];

        //wap_delivery
        $wap_delivery_data = [
            'delivery_bn'=>$delivery_obj->gen_id(),
            'skuNum'=>$item_num,
            'itemNum'=>$item_num,
            'status'=>'1',
            'op_id'=>$opInfo['op_id'],
            'sync_status'=>'5',
            'logi_status'=>'8',
            'outer_delivery_bn'=>$ome_delivery_data['delivery_bn'],
            'bnsContent'=>$bnsContent,
            'order_bn'=>$order_info['order_bn'],
            'branch_id'=>$branch_info['branch_id'],
            'shop_id'=>$order_info['shop_id'],
            'ship_name'=>$order_info['consignee']['name'],
            'ship_addr'=>$order_info['consignee']['addr'],
            'order_createtime'=>$cur_time,
            'create_time'=>$cur_time
        ];



        kernel::database()->beginTransaction();

        $ome_result = $delivery_obj->insert($ome_delivery_data);
        if (!$ome_result || !$ome_delivery_data['delivery_id']) {
            kernel::database()->rollBack();
            return array('rsp' => 'fail', 'msg' => 'oms 发货单生成失败');
        }

        $wap_result = $wap_delivery_obj->insert($wap_delivery_data);
        if (!$wap_result || !$wap_delivery_data['delivery_id']) {
            kernel::database()->rollBack();
            return array('rsp' => 'fail', 'msg' => '发货单生成失败');
        }

        //ome_items
        foreach ($order_items as $item_info) {
            $nums = $item_info['nums']?$item_info['nums']:$item_info['quantity'];
            $add_item_info = [
                'delivery_id'=>$ome_delivery_data['delivery_id'],
                'product_id'=>$item_info['product_id'],
                'shop_product_id'=>$item_info['shop_product_id'],
                'bn'=>$item_info['bn'],
                'product_name'=>$item_info['name'],
                'number'=>$nums,
                'esb_pmt_amount'=>$esb_data['esb_pmt_amount'],
                'esb_amount'=>$esb_data['esb_amount']
            ];

            $ome_item_res = $delivery_items_obj->insert($add_item_info);
            if (!$ome_item_res || !$add_item_info['item_id']) {
                kernel::database()->rollBack();
                return array('rsp' => 'fail', 'msg' => 'ome 发货单明细生成失败');
            }

            $order_obj_info = $order_objects_obj->dump(['obj_id'=>$item_info['obj_id']],'oid');

            $add_item_detail = [
                'delivery_id'=>$ome_delivery_data['delivery_id'],
                'delivery_item_id'=>$add_item_info['item_id'],
                'order_id'=>$item_info['order_id'],
                'order_item_id'=>$item_info['item_id'],
                'order_obj_id'=>$item_info['obj_id'],
                'item_type'=>$item_info['item_type'],
                'product_id'=>$item_info['product_id'],
                'bn'=>$item_info['bn'],
                'number'=>$nums,
                'price'=>$item_info['price'],
                'oid'=>$order_obj_info['oid'],
                'divide_order_fee'=>$order_obj_info['divide_order_fee']
            ];
            $detail_res = $delivery_items_detail_obj->insert($add_item_detail);
            if (!$detail_res || !$add_item_detail['item_detail_id']) {
                kernel::database()->rollBack();
                return array('rsp' => 'fail', 'msg' => 'ome 发货单明细生成失败');
            }
        }

        $add_delivery_order_data = ['delivery_id'=>$ome_delivery_data['delivery_id'],'order_id'=>$order_id];
        $delivery_order_obj->insert($add_delivery_order_data);

        //wms_items
        foreach ($order_items as $item_info) {
            $nums = $item_info['nums']?$item_info['nums']:$item_info['quantity'];
            $add_item_info = [
                'outer_item_id'=>$item_info['item_id'],
                'delivery_id'=>$wap_delivery_data['delivery_id'],
                'product_id'=>$item_info['product_id'],
                'shop_product_id'=>$item_info['shop_product_id'],
                'bn'=>$item_info['bn'],
                'product_name'=>$item_info['name'],
                'number'=>$nums,
                'esb_pmt_amount'=>$esb_data['esb_pmt_amount'],
                'esb_amount'=>$esb_data['esb_amount']
            ];
            $wap_item_res = $wap_delivery_items_obj->insert($add_item_info);
            if (!$wap_item_res || !$add_item_info['item_id']) {
                kernel::database()->rollBack();
                return array('rsp' => 'fail', 'msg' => 'wap 发货单明细生成失败');
            }
        }

        kernel::database()->commit();

        $operationLogObj = app::get('ome')->model('operation_log');
        $operationLogObj->write_log('delivery_modify@ome',$ome_delivery_data['delivery_id'],"退款自动补取消的发货单");

        return ['status'=>'succ','msg'=>'创建发货成功!'];
    }


    /**
     * 通过退款申请单号，生成取消的发货单
     * @param $refund_id
     * @return string[]
     */
    public function refund_create_by_refund_id($refund_id){
        $refund_apply_info = app::get('ome')->model('refund_apply')->dump(['apply_id'=>$refund_id]);
        if(!$refund_apply_info){
            return ['status'=>'fail','msg'=>'没有找到对应退款单'];
        }

        $product_data = $refund_apply_info['product_data'];
        if(!$product_data){
            return ['status'=>'fail','msg'=>'退款单没有商品'];
        }

        $product_data = unserialize($product_data);
        if(!$product_data){
            return ['status'=>'fail','msg'=>'退款单没有商品!'];
        }

        $esb_data = kernel::single('ome_sap_sap')->_esb_refund_amount($refund_id);

        //没有发货的订单，生成一个取消发货单
        $this->refund_create(['order_id'=>$refund_apply_info['order_id'],'refund_item_list'=>$product_data],$esb_data);

        return ['status'=>'succ','msg'=>'创建发货成功!!'];
    }
}