<?php

/**
 * 补录运单号订阅
 * Class ome_autotask_timer_insertexpresshqepay
 */
class ome_autotask_timer_insertexpresshqepay
{
    public function process($params, &$error_msg = '')
    {
        $cache_key = "timer_insertexpresshqepay";
        //加个缓存，别执行的太频繁了
        $have_cache = cachecore::fetch($cache_key);
        if($have_cache){
            kernel::single('base_customlog')->saveLog('insertexpresshqepay', ['error'=>'过于频繁']);
            return true;
        }

        //获取近7天的发货的订单
        $start_time = strtotime('-7 days');
        $end_now = time() - (3600 * 6);
        $waybill_start_time = strtotime('-10 days');
        $sql = "select delivery_id,delivery_bn,logi_no from sdb_ome_delivery where status = 'succ' and logi_status in('0','7') and delivery_time>$start_time and delivery_time < $end_now 
                                 and delivery_bn not in (select waybill_number from sdb_logisticsmanager_waybill where create_time > $waybill_start_time) ";

        kernel::single('base_customlog')->saveLog('insertexpresshqepay', ['sql'=>$sql]);

        $delivery_list = kernel::database()->select($sql);
        $has_logi_no = [];
        foreach ($delivery_list as $delivery_info) {

            if(in_array($delivery_info['logi_no'],$has_logi_no)){
                continue;
            }

            $delivery_id = $delivery_info['delivery_id'];
            kernel::single('ome_event_trigger_shop_hqepay')->hqepay_pub($delivery_id);

            $has_logi_no[] = $delivery_info['logi_no'];
            kernel::single('base_customlog')->saveLog('insertexpresshqepay', ['msg'=>$delivery_info['delivery_bn']."重新订阅"]);
        }

        cachecore::store($cache_key, 'runing', 60);
        return true;
    }
}