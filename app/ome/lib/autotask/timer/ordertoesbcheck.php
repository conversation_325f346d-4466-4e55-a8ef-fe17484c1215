<?php

class ome_autotask_timer_ordertoesbcheck
{
    protected $key = 'last_time';

    protected $billType = [
        'payed' => '支付订单',
        'delivery' => '发货单',
        'refund' => '退款单',
        'return' => '退货单'
    ];

    protected $statusList = [
        'succ' => '未生成ESB数据',
        'fail' => '推送ESB报错',
        'no_refund' => '无退款单',
    ];

    public function process($params = '', &$error_msg = ''): bool
    {
        base_kvstore::instance('esb_check')->fetch($this->key, $last_time);

        $cur_time = time();
        # 查询已绑定的店铺
        $shop_filter = [
            's_type' => '1',
            'node_id|noequal' => ''
        ];
        $shopList = app::get('ome')->model('shop')->getList('shop_id,shop_type,start_order_time', $shop_filter);
        if (empty($shopList)) {
            $error_msg = '没有绑定的店铺列表';
            return false;
        }

        # 保存当前时间
        base_kvstore::instance('esb_check')->store($this->key, $cur_time);

        $sapObj = kernel::single('ome_sap_sap');
        $reshipMdl = app::get('ome')->model('reship');
        $not_push_list = ['payed' => [], 'delivery' => [], 'refund' => [], 'return' => [], 'no_refund' => [], 'all_list' => []];
        $push_error_list = ['payed' => [], 'delivery' => [], 'refund' => [], 'return' => []];

        foreach ($shopList as $shop) {
            # 开始接单时间
            $start_order_time = $shop['start_order_time'];
            if (empty($last_time)) {
                $begintime = empty($start_order_time) ? $cur_time - 3600 : $start_order_time;
            } else {
                $begintime = $last_time;
            }
            $endtime = $cur_time;

            # 查询参数
            $data = [
                'shop_id' => $shop['shop_id'],
                'shop_type' => $shop['shop_type'],
                'start_time' => $begintime,
                'end_time' => $endtime,
            ];

            # 订单
            $order_result = $this->checkNotPushEsbOrders($data);
            if (!empty($order_result['not_list'])) {
                $not_push_list['payed'] = array_merge($not_push_list['payed'], $order_result['not_list']);
            }
            if (!empty($order_result['fail_list'])) {
                $push_error_list['payed'] = array_merge($push_error_list['payed'], $order_result['fail_list']);
            }

            # 发货单
            $delivery_result = $this->checkNotPushEsbDeliverys($data);
            if (!empty($delivery_result['not_list'])) {
                $not_push_list['delivery'] = array_merge($not_push_list['delivery'], $delivery_result['not_list']);
            }
            if (!empty($delivery_result['fail_list'])) {
                $push_error_list['delivery'] = array_merge($push_error_list['delivery'], $delivery_result['fail_list']);
            }

            # 仅退款和退货退款单
            $refund_result = $this->checkNotPushEsbRefunds($data);
            if (!empty($refund_result['refund']['not_list'])) {
                $not_push_list['refund'] = array_merge($not_push_list['refund'], $refund_result['refund']['not_list']);
            }
            if (!empty($refund_result['refund']['fail_list'])) {
                $push_error_list['refund'] = array_merge($push_error_list['refund'], $refund_result['refund']['fail_list']);
            }
            if (!empty($refund_result['return']['not_list'])) {
                $not_push_list['return'] = array_merge($not_push_list['return'], $refund_result['return']['not_list']);
            }
            if (!empty($refund_result['return']['fail_list'])) {
                $push_error_list['return'] = array_merge($push_error_list['return'], $refund_result['return']['fail_list']);
            }

            # 读取订单全额退款，但是无完成退款的退款申请单
            $result_apply = $this->checkNotPushEsbApply($data);
            if (!empty($result_apply['not_list'])) {
                $not_push_list['no_refund'] = array_merge($not_push_list['no_refund'], $result_apply['not_list']);
            }
        }

        # 推送订单
        if (!empty($not_push_list['payed'])) {
            foreach ($not_push_list['payed'] as $order) {
                $sapObj->push_order($order['order_id'], $order['shop_type']);
                # 记录订单
                $not_push_list['all_list'][] = ['bill_id' => $order['order_id'], 'bill_no' => $order['order_bn'], 'order_bn' => $order['order_bn'], 'bill_type' => 'payed', 'status' => 'succ'];
            }
        }
        if (!empty($push_error_list['payed'])) {
            foreach ($push_error_list['payed'] as $order) {
                $not_push_list['all_list'][] = ['bill_id' => $order['order_id'], 'bill_no' => $order['order_bn'], 'order_bn' => $order['order_bn'], 'bill_type' => 'payed', 'status' => 'fail'];
            }
        }
        # 推送发货单
        if (!empty($not_push_list['delivery'])) {
            foreach ($not_push_list['delivery'] as $delivery) {
                $sapObj->push_delivery($delivery['delivery_id'], $delivery['shop_type']);
                # 记录发货单
                $not_push_list['all_list'][] = ['bill_id' => $delivery['delivery_id'], 'bill_no' => $delivery['delivery_bn'], 'order_bn' => $delivery['order_bn'] ?? '', 'bill_type' => 'delivery', 'status' => 'succ'];
            }
        }
        if (!empty($push_error_list['delivery'])) {
            foreach ($push_error_list['delivery'] as $delivery) {
                $not_push_list['all_list'][] = ['bill_id' => $delivery['delivery_id'], 'bill_no' => $delivery['delivery_bn'], 'order_bn' => $delivery['order_bn'] ?? '', 'bill_type' => 'delivery', 'status' => 'fail'];
            }
        }
        # 推送退款单
        if (!empty($not_push_list['refund'])) {
            foreach ($not_push_list['refund'] as $refund) {
                $sapObj->push_refund($refund['apply_id'], $refund['shop_type']);
                # 记录仅退款单
                $not_push_list['all_list'][] = ['bill_id' => $refund['apply_id'], 'bill_no' => $refund['refund_apply_bn'], 'order_bn' => $refund['order_bn'] ?? '', 'bill_type' => 'refund', 'status' => 'succ'];
            }
        }
        if (!empty($push_error_list['refund'])) {
            foreach ($push_error_list['refund'] as $refund) {
                $not_push_list['all_list'][] = ['bill_id' => $refund['apply_id'], 'bill_no' => $refund['refund_apply_bn'], 'order_bn' => $refund['order_bn'] ?? '', 'bill_type' => 'refund', 'status' => 'fail'];
            }
        }
        # 推送退货单
        if (!empty($not_push_list['return'])) {
            foreach ($not_push_list['return'] as $return) {
                $sapObj->push_reship($return['reship_id'], $return['shop_type']);
                # 记录退货单
                $reshipInfo = $reshipMdl->dump(array('reship_id' => $return['reship_id']), 'reship_bn');
                $not_push_list['all_list'][] = ['bill_id' => $return['reship_id'], 'bill_no' => $reshipInfo['reship_bn'] ?? '', 'order_bn' => $return['order_bn'] ?? '', 'bill_type' => 'return', 'status' => 'succ'];
            }
        }
        if (!empty($push_error_list['return'])) {
            foreach ($push_error_list['return'] as $return) {
                $reshipInfo = $reshipMdl->dump(array('reship_id' => $return['reship_id']), 'reship_bn');
                $not_push_list['all_list'][] = ['bill_id' => $return['reship_id'], 'bill_no' => $reshipInfo['reship_bn'] ?? '', 'order_bn' => $return['order_bn'] ?? '', 'bill_type' => 'return', 'status' => 'fail'];
            }
        }
        # 取消订单，没有退款申请单
        if (!empty($not_push_list['no_refund'])) {
            foreach ($not_push_list['no_refund'] as $order) {
                $not_push_list['all_list'][] = [
                    'bill_id' => $order['order_id'],
                    'bill_no' => $order['order_bn'],
                    'order_bn' => $order['order_bn'],
                    'bill_type' => 'refund',
                    'status' => 'no_refund'
                ];

                # 生成退款单
                $apply_id = $this->generalRefundApply($order, $error_msg);
                # 重新推送ESB
                if (!empty($apply_id)) {
                    $sapObj->push_refund($apply_id, $order['shop_type']);
                }
            }
        }

        # 发送邮件 -- 实时
        if (!empty($not_push_list['all_list'])) {
            kernel::single('monitor_event_notify')->addNotify('ordertoesb_check_timer', [
                'content' => $this->get_send_content($not_push_list['all_list'], $begintime, $endtime)
            ], true);
        }

        $error_msg = '查询时间段：' . date('Y-m-d H:i:s', $begintime) . '至' . date('Y-m-d H:i:s', $endtime);
        return true;
    }

    /**
     * 生成已退款的退款单
     * @param $params
     * @return mixed
     */
    private function generalRefundApply($params, &$error_msg)
    {
        if (empty($params['order_id'])) {
            $error_msg = '订单ID参数错误';
            return false;
        }

        //申请退款单号
        $refundapp = app::get('ome')->model('refund_apply');
        $orderMdl = app::get('ome')->model('orders');
        $refundsMdl = app::get('ome')->model('refunds');
        $sapOrderItemsMdl = app::get('sales')->model('sap_order_items');
        $paymentsMdl = app::get('ome')->model('payments');

        // 订单
        $oids = $bns = $product_list = [];
        $orderInfo = $orderMdl->dump($params['order_id'], "*", array("order_objects" => array("*", array("order_items" => array("*")))));
        # 历史订单不处理
        if (!empty($orderInfo['is_history'])) {
            $error_msg = '该订单是导入的历史订单，不处理';
            return false;
        }

        if (in_array($orderInfo['pay_status'], ['0', '3'])) {
            $error_msg = '未支付或部分付款的订单，不处理';
            return false;
        }

        # 检查支付单
        $paymentList = $paymentsMdl->getList('*', array('order_id' => $orderInfo['order_id']));
        if (empty($paymentList)) {
            $error_msg = '没有支付单信息，不处理';
            return false;
        }

        if (!empty($orderInfo['order_objects'])) {
            $class_name = sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($orderInfo['shop_type']));
            foreach ($orderInfo['order_objects'] as $object) {
                $oids[] = $object['oid'];
                $bns[] = $object['bn'];
                # 子单号
                $oid = kernel::single($class_name)->getOid($orderInfo['order_id'], $object['oid']);
                # 检查是否存在esb退款单数据
                $esb_filter = [
                    'orderType' => 'REFUNDED',
                    'oriFlowNo' => $oid
                ];
                $sapOrderItems = $sapOrderItemsMdl->getList('item_id', $esb_filter);
                if (!empty($sapOrderItems)) {
                    $error_msg = '子单号[' . $oid . ']已经生成了推送esb的退款单数据';
                    return false;
                }

                foreach ($object['order_items'] as $item) {
                    $product_list[] = array(
                        'product_id' => $item['product_id'],
                        'bn' => $item['bn'],
                        'name' => !empty($item['title']) ? $item['title'] : $item['name'],
                        'num' => $item['quantity'],
                        'price' => $item['price'],
                        'order_item_id' => $item['item_id'],
                        'oid' => $object['oid'],
                        'modified' => time(),
                    );
                }
            }
        }

        $is_update_order = true;//是否更新订单付款状态
        $error_msg = '';
        $db = kernel::database();
        $refund_apply_bn = $refundapp->gen_id();

        try {
            $db->beginTransaction();

            $insertData = array(
                'order_id' => $params['order_id'],
                'refund_apply_bn' => $refund_apply_bn,
                'pay_type' => 'online',
                'money' => $orderInfo['total_amount'],
                'refunded' => $orderInfo['total_amount'],
                'refund_money' => $orderInfo['total_amount'],
                'memo' => '强制生成的退款单',
                'create_time' => time(),
                'status' => '4',
                'shop_id' => $orderInfo['shop_id'],
                'source' => 'local',
                'shop_type' => $orderInfo['shop_type'],
                'refund_refer' => '0', //退款来源
                'org_id' => $orderInfo['org_id'],
                'bn' => implode(',', $bns),
                'oid' => implode(',', $oids), //oid子单
                'tag_type' => '0', //退款类型
            );
            if (!empty($product_list)) {
                $insertData['product_data'] = serialize($product_list);
            }
            //根据商品编码获取门店信息
            $bn = current($product_list)['bn'];
            $store = kernel::single("o2o_store_material")->getOrderStore($bn,$params['order_id']);
            if ($store) {
                $belong_type = $store['performance_type'] == 'store' ? 'store' : 'town';
                $store_id = $store['store_id'];
            } else {
                $belong_type = 'customer';
                $store_id = 0;
            }
            $insertData['belong_store_id'] = $store_id;
            $insertData['belong_type'] = $belong_type;
            $insertData['check_payed'] = false;
            # 创建售后申请单
            $rs = kernel::single('ome_refund_apply')->createRefundApply($insertData, $is_update_order, $error_msg);
            if (!$rs) {
                throw new Exception($error_msg);
            }

            # 生成退款单
            $data = array(
                'refund_bn' => $refund_apply_bn,
                'shop_id' => $orderInfo['shop_id'],
                'order_id' => $orderInfo['order_id'],
                'currency' => 'CNY',
                'money' => $orderInfo['total_amount'],
                'cur_money' => $orderInfo['total_amount'],
                'pay_type' => 'online',
                'download_time' => time(),
                'status' => 'succ',
                'memo' => '强制退款',
                'modifiey' => time(),
                't_ready' => time(),
                't_sent' => time(),

            );
            $rs = $refundsMdl->insert($data);
            if (!$rs) {
                throw new Exception('退款单生成失败：' . $refundsMdl->db->errorinfo());
            }

            $db->commit();

            return $insertData['apply_id'];
        } catch (Exception $e) {
            $db->rollBack();

            $error_msg = $e->getMessage();
            return false;
        }
    }

    /**
     * 读取订单全额退款，但是无完成退款的退款申请单
     * @param $params
     * @return void
     */
    private function checkNotPushEsbApply($params)
    {
        $orderMdl = app::get('ome')->model('orders');
        $refundsMdl = app::get('ome')->model('refund_apply');
        $result = [
            'refund' => ['not_list' => []],
        ];

        $filter = [
            'shop_id' => $params['shop_id'],
            'last_modified|lthan' => time() - 7200,
            'pay_status' => ['4', '5'], // 部分退款、全额退款
        ];
        # 查询订单
        $orderList = $orderMdl->getList('order_id,order_bn,shop_type', $filter);
        if (empty($orderList)) {
            return false;
        }

        # 订单ID
        $order_ids = array_column($orderList, 'order_id');
        # 读取订单对应的仅退款单 - 已退款成功的
        $apply_filter = [
            'shop_id' => $params['shop_id'],
            'order_id' => $order_ids,
            'status|noequal' => '3',
        ];
        $applyList = $refundsMdl->getList('apply_id,refund_apply_bn,shop_type,reship_id,order_id', $apply_filter);
        if (!empty($applyList)) {
            $applyData = array_column($applyList, null, 'order_id');
        }

        # 匹配订单
        if (empty($applyData)) {
            $result['refund']['not_list'] = $orderList;
        } else {
            foreach ($orderList as $item) {
                if (!empty($applyData[$item['order_id']])) {
                    continue;
                }
                $result['refund']['not_list'][] = $item;
            }
        }
        return $result['refund'];
    }

    private function get_send_content($list, $begintime, $endtime)
    {
        $body = '<html>
                <head>
                    <style>
                        table {
                            border-collapse: collapse;
                        }
               
                        table,
                        th,
                        td {
                            border: 1px solid black;
                        }
                    </style>
                </head>
                <body>';
        $body .= '<h3>系统检测到未推送ESB单据，并进行了自动重新推送。</h3><br>';
        $body .= '<table><thead><tr><th>订单号</th><th>单据号</th><th>单据类型</th><th>异常类型</th><th>OMS处理结果</th></tr></thead><tbody>';
        foreach ($list as $item) {
            $tr = '<tr><td>' . $item['order_bn'] . '</td><td>' . $item['bill_no'] . '</td>'
                . '<td>' . ($this->billType[$item['bill_type']] ?? '未知') . '</td>'
                . '<td>' . ($this->statusList[$item['status']] ?? '未知') . '</td>';
            switch ($item['status']) {
                case 'succ':
                    $tr .= '<td><span style="color: green">重新生成EBS数据并推送</span></td>';
                    break;
                case 'fail':
                    $tr .= '<td><span style="color: red">推送ESB失败，暂不处理</span></td>';
                    break;
                case 'no_refund':
                    $tr .= '<td><span style="color: orange">取消订单未生成退款单，暂不处理</span></td>';
                    break;
            }
            $tr .= '</tr>';
            $body .= $tr;
        }
        $body .= '</tbody></table><br><br><br>检测时间范围：' . date('Y/m/d H:i:s', $begintime) . ' - ' . date('Y/m/d H:i:s', $endtime)
            . '<br>发送时间：' . date('Y-m-d H:i:s');
        $body .= '</body></html>';

        return $body;
    }

    /**
     * 查询未推送esb的仅退款单
     * @param $params
     * @return mixed
     */
    private function checkNotPushEsbRefunds($params)
    {
        if (empty($params['shop_id'])) {
            return false;
        }

        $refundsMdl = app::get('ome')->model('refund_apply');
        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $orderMdl = app::get('ome')->model('orders');
        $result = [
            'refund' => ['fail_list' => [], 'not_list' => []],
            'return' => ['fail_list' => [], 'not_list' => []]
        ];

        # 读取订单对应的仅退款单 - 已退款成功的
        $apply_filter = [
            'shop_id' => $params['shop_id'],
            'status' => '4',
            'last_modified|between' => [$params['start_time'], $params['end_time']],
        ];
        $applyList = $refundsMdl->getList('apply_id,refund_apply_bn,shop_type,reship_id,order_id', $apply_filter);
        if (!empty($applyList)) {
            $order_ids = array_column($applyList, 'order_id');
            # 获取订单
            $orderList = $orderMdl->getList('order_id,order_bn', array('order_id' => $order_ids));
            if (!empty($orderList)) {
                $orderData = array_column($orderList, null, 'order_id');
            }
            # 记录仅退款和退货退款不同退款类型的ID列表
            $tmp_data = ['refund' => [], 'return' => [], 'esb_data' => [], 'esb_fail_data' => []];
            foreach ($applyList as $apply) {
                # 设置订单号
                $apply['order_bn'] = empty($orderData[$apply['order_id']]) ? '' : $orderData[$apply['order_id']]['order_bn'];
                if (empty($apply['reship_id'])) {
                    $tmp_data['refund'][$apply['apply_id']] = $apply;
                } else {
                    $tmp_data['return'][$apply['reship_id']] = $apply;
                }
            }

            $all_ids = [];
            # 退款单id和退货单id
            if (!empty($tmp_data['refund'])) {
                $all_ids = array_merge($all_ids, array_keys($tmp_data['refund']));
            }
            if (!empty($tmp_data['return'])) {
                $all_ids = array_merge($all_ids, array_keys($tmp_data['return']));
            }
            if (empty($all_ids)) {
                return false;
            }

            # 查询已推送esb的单据号
            $esb_filter = [
                'shop_type' => $params['shop_type'],
                'bill_id' => $all_ids,
                'bill_type' => ['refund', 'return']
            ];
            $sapList = $sapOrderMdl->getList('bill_id,bill_type,shop_type,sync_status', $esb_filter);
            if (!empty($sapList)) {
                foreach ($sapList as $sap) {
                    if ($sap['sync_status'] == 'fail') {
                        $tmp_data['esb_fail_data'][$sap['bill_type']][] = $sap['bill_id'];
                    } else {
                        $tmp_data['esb_data'][$sap['bill_type']][$sap['bill_id']] = $sap;
                    }
                }
            }

            if (!empty($tmp_data['refund'])) {
                # 退款单ID列表
                $refund_ids = array_keys($tmp_data['refund']);
                # 已推送esb的退款单id列表
                $esb_refunds = empty($tmp_data['esb_data']['refund']) ? [] : array_keys($tmp_data['esb_data']['refund']);
                # 对比退款单
                $diff_refunds = array_diff($refund_ids, $esb_refunds);
            }

            if (!empty($tmp_data['return'])) {
                # 退货单ID列表
                $reship_ids = array_keys($tmp_data['return']);
                # 已推送esb的退货单id列表
                $esb_reships = empty($tmp_data['esb_data']['return']) ? [] : array_keys($tmp_data['esb_data']['return']);
                # 对比退货单
                $diff_reships = array_diff($reship_ids, $esb_reships);
            }

            # 保存未推送esb的退款单和退货单
            if (!empty($diff_refunds)) {
                foreach ($diff_refunds as $refund_id) {
                    if (!empty($tmp_data['esb_fail_data']['refund']) && in_array($refund_id, $tmp_data['esb_fail_data']['refund'])) {
                        $result['refund']['fail_list'][] = $tmp_data['refund'][$refund_id];
                    } else {
                        $result['refund']['not_list'][] = $tmp_data['refund'][$refund_id];
                    }
                }
            }
            if (!empty($diff_reships)) {
                foreach ($diff_reships as $reship_id) {
                    if (!empty($tmp_data['esb_fail_data']['return']) && in_array($reship_id, $tmp_data['esb_fail_data']['return'])) {
                        $result['return']['fail_list'][] = $tmp_data['return'][$reship_id];
                    } else {
                        $result['return']['not_list'][] = $tmp_data['return'][$reship_id];
                    }
                }
            }
        }
        return $result;
    }

    /**
     * 查询未推送esb的发货单
     * @param $params
     * @return array|false
     */
    private function checkNotPushEsbDeliverys($params)
    {
        if (empty($params['shop_id'])) {
            return false;
        }

        $deliveryMdl = app::get('ome')->model('delivery');
        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $result = ['fail_list' => [], 'not_list' => []];

        # 读取订单对应的发货单 - 已发货的
        $filter = [
            'shop_id' => $params['shop_id'],
            'delivery_time|between' => [$params['start_time'], $params['end_time']],
            'is_bind' => 'false',
            'status' => 'succ'
        ];
        $deliveryList = $deliveryMdl->getList('delivery_id,delivery_bn,shop_type', $filter);
        if (!empty($deliveryList)) {
            $typeDeliverys = array_column($deliveryList, null, 'delivery_id');
            # 发货单id列表
            $delivery_ids = array_keys($typeDeliverys);
            # 查询已推送esb的单据号
            $esb_filter = [
                'shop_type' => $params['shop_type'],
                'bill_id' => $delivery_ids,
                'bill_type' => 'delivery'
            ];
            $sapList = $sapOrderMdl->getList('bill_id,sync_status', $esb_filter);
            if (!empty($sapList)) {
                foreach ($sapList as $item) {
                    if ($item['sync_status'] == 'fail') {
                        $esb_fail_deliverys[] = $item['bill_id'];
                    } else {
                        $esb_deliverys[] = $item['bill_id'];
                    }
                }
            }

            # 对比差异订单
            $diff_deliverys = array_diff($delivery_ids, empty($esb_deliverys) ? [] : $esb_deliverys);
            # 保存未推送esb的订单
            if (!empty($diff_deliverys)) {
                foreach ($diff_deliverys as $delivery_id) {
                    $tmp_data = $typeDeliverys[$delivery_id];
                    # 根据发货单获取订单号
                    $orderInfo = $deliveryMdl->getOrderByDeliveryId($delivery_id);
                    if (!empty($orderInfo)) {
                        $tmp_data['order_bn'] = $orderInfo['order_bn'];
                    }
                    if (!empty($esb_fail_deliverys) && in_array($delivery_id, $esb_fail_deliverys)) {
                        $result['fail_list'][] = $tmp_data;
                    } else {
                        $result['not_list'][] = $tmp_data;
                    }
                }
            }
        }
        return $result;
    }

    /**
     * 查询未推送esb的支付订单
     * @param $params
     * @return mixed
     */
    private function checkNotPushEsbOrders($params)
    {
        if (empty($params['shop_id'])) {
            return false;
        }

        $orderMdl = app::get('ome')->model('orders');
        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $result = ['fail_list' => [], 'not_list' => []];

        $filter = [
            'shop_id' => $params['shop_id'],
            'paytime|between' => [$params['start_time'], $params['end_time']]
        ];
        # 查询订单
        $orderList = $orderMdl->getList('order_id,order_bn,shop_type', $filter);
        if (!empty($orderList)) {
            $typeOrders = array_column($orderList, null, 'order_id');
            # 订单id列表
            $order_ids = array_keys($typeOrders);
            # 查询已推送esb的单据号
            $esb_filter = [
                'shop_type' => $params['shop_type'],
                'bill_id' => $order_ids,
                'bill_type' => 'payed'
            ];
            $sapList = $sapOrderMdl->getList('bill_id,sync_status', $esb_filter);
            if (!empty($sapList)) {
                foreach ($sapList as $item) {
                    # 记录生成了esb数据，但是推送esb失败的订单
                    if ($item['sync_status'] == 'fail') {
                        $esb_fail_orders[] = $item['bill_id'];
                    } else {
                        $esb_orders[] = $item['bill_id'];
                    }
                }
            }

            # 对比差异订单
            $diff_orders = array_diff($order_ids, empty($esb_orders) ? [] : $esb_orders);
            # 保存未推送esb的订单
            if (!empty($diff_orders)) {
                foreach ($diff_orders as $order_id) {
                    if (!empty($esb_fail_orders) && in_array($order_id, $esb_fail_orders)) {
                        $result['fail_list'][] = $typeOrders[$order_id];
                    } else {
                        $result['not_list'][] = $typeOrders[$order_id];
                    }
                }
            }
        }
        return $result;
    }
}