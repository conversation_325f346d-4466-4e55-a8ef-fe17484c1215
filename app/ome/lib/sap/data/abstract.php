<?php

abstract class ome_sap_data_abstract
{

    /**
     * 需要扣除金额的字段集合
     * @var array
     */
    protected $platform_dec_fields = [];

    /**
     * 不推送金蝶sap的券类型（假性营销）
     * @var array
     */
    protected $notPushCouponTypeList = [];

    /**
     * 券ID字段名称
     * @var string
     */
    protected $coupon_field_name = 'coupon_meta_id';

    /**
     * 成功输出
     *
     * @return array
     * <AUTHOR>
    final public function succ($msg = '', $msgcode = '', $data = null)
    {
        return array('rsp' => 'succ', 'msg' => $msg, 'msg_code' => $msgcode, 'data' => $data);
    }

    /**
     * 失败输出
     *
     * @return array
     * <AUTHOR>
    final public function error($msg, $msgcode, $data = null)
    {
        return array('rsp' => 'fail', 'msg' => $msg, 'err_msg' => $msg, 'msg_code' => $msgcode, 'data' => $data);
    }

    /**
     * 获取订单参数
     * @param $order_id
     * @return false|void
     */
    public function get_order_sdf($order_id, $extend = [])
    {
        if (empty($order_id)) {
            return false;
        }

        $orderMdl = app::get('ome')->model('orders');
        $orderInfo = $orderMdl->dump(array('order_id' => $order_id), "*", array("order_objects" => array("*", array("order_items" => array("*")))));
        # 未支付、部分付款订单不推送esb
        if (empty($orderInfo) || in_array($orderInfo['pay_status'], ['0', '3'])) {
            return false;
        }

        # 失败订单，或者订单明细为空，都不生成esb数据
        if ($orderInfo['is_fail'] == 'true' || empty($orderInfo['order_objects'])) {
            return false;
        }

        # 默认过滤删除的sku明细
        $filter_items = $extend['filter_delete_item'] ?? true;
        # 过滤已经删除的sku
        foreach ($orderInfo['order_objects'] as $obj_id => $object) {
            if ($object['delete'] == 'true' && $filter_items) {
                unset($orderInfo['order_objects'][$obj_id]);
            }
        }

        $result = [
            # 主订单
            'order' => [
                'order_id' => $orderInfo['order_id'],
                'order_bn' => $orderInfo['order_bn'],
                'createtime' => $orderInfo['createtime'],
                'paytime' => $orderInfo['paytime'] ?? 0,
                'shop_id' => $orderInfo['shop_id'],
                'shop_type' => $orderInfo['shop_type'],
                'consignee' => array(
                    'name' => $orderInfo['consignee']['name'],
                    'area' => $orderInfo['consignee']['area'],
                    'addr' => $orderInfo['consignee']['addr'],
                    'zip' => $orderInfo['consignee']['zip'],
                    'mobile' => $orderInfo['consignee']['mobile'],
                ),
                'is_cod' => $orderInfo['is_cod'],
                'cost_item' => $orderInfo['cost_item'],
                'cost_freight' => $orderInfo['shipping']['cost_shipping'],
                'pmt_goods' => $orderInfo['pmt_goods'],
                'pmt_order' => $orderInfo['pmt_order'],
                'total_amount' => $orderInfo['total_amount'],
                'payed' => $orderInfo['payed'],
                'mark_type' => $orderInfo['mark_type'],
                'order_source' => $orderInfo['source'],
                'card_number' => $orderInfo['card_number'], // 会员卡号
                'order_type' => $orderInfo['order_type'],
                'relate_order_bn' => $orderInfo['relate_order_bn'],
                'createway' => $orderInfo['createway'],
                'omnichannel' => $orderInfo['omnichannel'],
                'process_status' => $orderInfo['process_status'],
                'pay_status' => $orderInfo['pay_status'],
                'ship_status' => $orderInfo['ship_status'],
                'is_history' => $orderInfo['is_history'] ?? 'false', // 历史订单
                'is_lucky_flag' => false, // 是否抽奖0元订单
                'order_objects' => $orderInfo['order_objects']
            ],
            # 支付信息
            'payments' => [],
            # 优惠券信息
            'coupon' => [],
        ];

        # 是否抽奖0元订单
        $is_lucky_flag = $this->is_lucky_order($orderInfo);
        if ($is_lucky_flag) {
            $result['order']['is_lucky_flag'] = true;
        }

        # 默认获取支付信息
        $default_is_payment = $extend['is_payment'] ?? true;
        # 默认获取优惠券
        $default_is_coupon = $extend['is_coupon'] ?? true;

        # 订单支付信息
        if ($default_is_payment) {
            $payments = $this->get_payments($orderInfo);
            if (!empty($payments)) {
                $result['payments'] = $payments;
            }
        }

        # 优惠券信息
        if ($default_is_coupon) {
            $coupons = $this->get_coupons($order_id);
            if (!empty($coupons)) {
                $result['coupon'] = $coupons;
            }
        }

        return $result;
    }

    /**
     * 仅退款数据
     * @param $apply_id
     * @return void
     */
    public function get_refund_sdf($apply_id)
    {
        if (empty($apply_id)) {
            return false;
        }

        $refundsMdl = app::get('ome')->model('refunds');
        $oOperation_log = app::get('ome')->model('operation_log');
        $orderObjectMdl = app::get('ome')->model('order_objects');

        # 退款单 - 仅退款
        $sql = "SELECT * FROM sdb_ome_refund_apply WHERE status = '4'"
            . " AND (refund_refer = '0' OR (refund_refer='1' AND (reship_id IS NULL OR reship_id = '')))"
            . " AND apply_id = " . $apply_id;
        $applyInfo = kernel::database()->selectrow($sql);
        if (empty($applyInfo)) {
            $oOperation_log->write_log('refund_apply@ome', $apply_id, '推送ESB接口失败：当前退款单不符合推送ESB的条件');
            return false;
        }

        $result = [
            # 退款申请
            'refund_apply' => [
                'apply_id' => $applyInfo['apply_id'],
                'refund_apply_bn' => $applyInfo['refund_apply_bn'],
                'order_id' => $applyInfo['order_id'],
                'pay_type' => $applyInfo['pay_type'],
                'money' => $applyInfo['money'],
                'refunded' => $applyInfo['refunded'],
                'payment' => $applyInfo['payment'],
                'create_time' => $applyInfo['create_time'],
                'status' => $applyInfo['status'],
                'shop_id' => $applyInfo['shop_id'],
                'return_id' => $applyInfo['return_id'],
                'reship_id' => $applyInfo['reship_id'],
                'refund_refer' => $applyInfo['refund_refer'],
                'source' => $applyInfo['source'],
                'shop_type' => $applyInfo['shop_type'],
                'oid' => $applyInfo['oid'],
                'refund_time' => $applyInfo['outer_lastmodify'], // 实际退款时间，默认为退款申请单创建时间
                'is_part_refund' => $applyInfo['is_part_refund'], // 是否部分退款
                'is_history' => $applyInfo['is_history'] ?? 'false', // 历史退款单
            ],
        ];
        # 售后商品信息
        if (!empty($applyInfo['product_data'])) {
            $product_list = unserialize($applyInfo['product_data']);
            if (count($product_list) != count($product_list, 1)) {
                $result['refund_apply']['product_list'] = $product_list;
            } else {
                $result['refund_apply']['product_list'][] = $product_list;
            }
        }
        # 读取实际退款时间
        $refunds_filter = [
            'order_id' => $applyInfo['order_id'],
            'refund_bn' => $applyInfo['refund_apply_bn']
        ];
        $refunds = $refundsMdl->dump($refunds_filter, 'order_id,t_sent,t_received');
        if (!empty($refunds)) {
            $result['refund_apply']['refund_time'] = $refunds['t_sent'] ?? $refunds['t_received'];
        }

        # 获取订单信息
        $order_result = $this->get_order_sdf($applyInfo['order_id'], ['is_payment' => false, 'is_coupon' => false]);
        if (!$order_result) {
            return false;
        }

        # 设置shop_goods_id和shop_product_id
        if (!empty($result['refund_apply']['product_list'])) {
            foreach ($result['refund_apply']['product_list'] as $k => $item) {
                $product_items = [];
                foreach ($order_result['order']['order_objects'] as $object) {
                    if ($object['oid'] == $item['oid']) {
                        $product_items = $object['order_items'];
                        break;
                    }
                }
                if (!empty($product_items)) {
                    $itemInfo = current($product_items);
                    $result['refund_apply']['product_list'][$k]['shop_goods_id'] = $itemInfo['shop_goods_id'] ?? '';
                    $result['refund_apply']['product_list'][$k]['shop_product_id'] = $itemInfo['shop_product_id'] ?? '';
                }
            }
        }

        # 如果没有商品信息，则用订单明细作为退货商品
        if (empty($applyInfo['oid'])) {
            foreach ($order_result['order']['order_objects'] as $object) {
                foreach ($object['order_items'] as $item) {
                    $product_list = [
                        'bn' => $item['bn'],
                        'name' => $item['name'],
                        'num' => $item['quantity'],
                        'price' => $item['divide_order_fee'],
                        'oid' => $object['oid'],
                        'shop_goods_id' => $item['shop_goods_id'],
                        'shop_product_id' => $item['shop_product_id'],
                    ];
                    $result['refund_apply']['product_list'][] = $product_list;
                }
            }
        }

        # 替换历史子单号
        if (!empty($result['refund_apply']['product_list']) && $order_result['order']['is_history'] == 'true') {
            # 获取订单objects数据
            $order_objects = $orderObjectMdl->getList('order_id,oid,history_oid', array('order_id' => $applyInfo['order_id']));
            if (!empty($order_objects)) {
                $objectList = array_column($order_objects, null, 'oid');
            }
            # 替换成历史子单号
            foreach ($result['refund_apply']['product_list'] as $key => $item) {
                $history_oid = empty($objectList[$item['oid']]) ? $item['oid'] : $objectList[$item['oid']]['history_oid'];
                $result['refund_apply']['product_list'][$key]['history_oid'] = $history_oid;
            }
        }

        # 合并订单数据
        $result = array_merge($result, $order_result);
        return $result;
    }

    /**
     * 发货单数据
     * @param $delivery_id
     * @return void
     */
    public function get_delivery_sdf($delivery_id, $delivery_shipping_id = null)
    {
        if (empty($delivery_id)) {
            return false;
        }

        $deliveryMdl = app::get('ome')->model('delivery');
        # 获取发货单
        $deliveryInfo = $deliveryMdl->dump(array('delivery_id' => $delivery_id), '*', array('delivery_items' => array('*'), 'delivery_order' => array('*')));
        if (empty($deliveryInfo)) {
            return false;
        }

        foreach ($deliveryInfo['delivery_items'] as $k => $iv) {
            if ($iv['delivery_shipping_id'] == $delivery_shipping_id) {
                $delivery_shipping_info = app::get('ome')->model('delivery_shipping')->dump(['delivery_shipping_id' => $delivery_shipping_id], 'push_sap');
                if ($delivery_shipping_info['push_sap'] == 'true') {
                    unset($deliveryInfo['delivery_items'][$k]);
                }
            }
        }

        // 如果是部分发货，则只推送部分发货的商品
        if ($delivery_shipping_id) {
            foreach ($deliveryInfo['delivery_items'] as $k => $iv) {
                if ($iv['delivery_shipping_id'] != $delivery_shipping_id) {
                    unset($deliveryInfo['delivery_items'][$k]);
                }
            }
        }

        $result = [
            # 发货单
            'delivery' => [
                'delivery_id' => $deliveryInfo['delivery_id'],
                'delivery_bn' => $deliveryInfo['delivery_bn'],
                'is_cod' => $deliveryInfo['is_cod'],
                'consignee' => $deliveryInfo['consignee'],
                'create_time' => $deliveryInfo['create_time'],
                'status' => $deliveryInfo['status'],
                'branch_id' => $deliveryInfo['branch_id'],
                'delivery_time' => $deliveryInfo['delivery_time'],
                'delivery_cost_actual' => $deliveryInfo['delivery_cost_actual'],
                'shop_id' => $deliveryInfo['shop_id'],
                'order_type' => $deliveryInfo['order_type'],
                'shop_type' => $deliveryInfo['shop_type'],
                'original_delivery_bn' => $deliveryInfo['original_delivery_bn'],
                'is_history' => $deliveryInfo['is_history'] ?? 'false', // 历史发货单
                'delivery_items' => $deliveryInfo['delivery_items'],
                'delivery_order' => $deliveryInfo['delivery_order'],
            ],
            'orders' => []
        ];

        foreach ($deliveryInfo['delivery_order'] as $item) {
            # 获取订单
            $orderInfo = $this->get_order_sdf($item['order_id'], ['is_payment' => false, 'is_coupon' => false]);
            if (empty($orderInfo)) {
                continue;
            }
            $result['orders'][] = $orderInfo;
        }
        return $result;
    }

    public function get_reship_sdf($reship_id, $is_push_change = false)
    {
        if (empty($reship_id)) {
            return false;
        }

        $reshipMdl = app::get('ome')->model('reship');
        $returnProductMdl = app::get('ome')->model('return_product');

        # 获取退货单
        $reshipInfo = $reshipMdl->dump(array('reship_id' => $reship_id), '*', array("reship_items" => array("*")));
        if (empty($reshipInfo)) {
            return false;
        }

        # 换货单不推送esb
        if ($reshipInfo['return_type'] == 'change' && !$is_push_change) {
            return false;
        }
        if ($reshipInfo['return_type'] == 'change' && $is_push_change) {
            //过滤换出的明细
            if (!empty($reshipInfo['reship_items'])) {
                foreach($reshipInfo['reship_items'] as $rik => $riv){
                    if($riv['return_type'] != 'return'){
                        unset($reshipInfo['reship_items'][$rik]);
                    }
                }
                sort($reshipInfo['reship_items']);
            }
        }

        # 重新格式化退货单明细
        if (!empty($reshipInfo['reship_items'])) {
            $reshipInfo['reship_items'] = array_values($reshipInfo['reship_items']);
        }

        # 计算是否最后一单售后（sky级别）
        if ($reshipInfo['shop_type'] == 'ecos.ecshopx' && !empty($reshipInfo['reship_items'])) {
            $orderItemsMdl = app::get('ome')->model('order_items');
            # 读取订单的已发货数量
            $orderItemList = $orderItemsMdl->getList('product_id,sendnum', array('order_id' => $reshipInfo['order_id']));
            if (!empty($orderItemList)) {
                $orderItemData = array_column($orderItemList, null, 'product_id');
            }

            # 设置是否最后一单
            foreach ($reshipInfo['reship_items'] as $key => $item) {
                # 默认不是最后一单标记
                $reshipInfo['reship_items'][$key]['is_last_reship'] = false;
                # 已退的sku退货数量
                $sql = "SELECT a.reship_id,a.check_time,b.product_id,b.num FROM sdb_ome_reship a LEFT JOIN sdb_ome_reship_items b ON a.reship_id = b.reship_id"
                    . " WHERE a.order_id = {$reshipInfo['order_id']} AND b.product_id = {$item['product_id']}"
                    . " AND b.return_type = 'return' AND a.is_check = '7'"
                    . " ORDER BY a.check_time ASC";
                $allReshipList = kernel::database()->select($sql);
                if (empty($allReshipList)) {
                    continue;
                }

                # 最后一条
                $lastInfo = end($allReshipList);
                if ($lastInfo['reship_id'] == $reshipInfo['reship_id']) {
                    # 其他已退数量
                    $other_nums = 0;
                    foreach ($allReshipList as $row) {
                        if ($row['reship_id'] == $reshipInfo['reship_id']) {
                            break;
                        }
                        $other_nums += $row['num'];
                    }
                    # 本次退货数量+其他已退数量是否等于发货数量
                    $total_reship_nums = bcadd($item['num'], $other_nums);
                    # 发货数量
                    $delivery_nums = $orderItemData[$item['product_id']]['sendnum'] ?? 0;
                    # 如果当前退货数量=已发货数量，则标记为最后一单
                    if (bccomp($total_reship_nums, $delivery_nums) == 0) {
                        $reshipInfo['reship_items'][$key]['is_last_reship'] = true;
                    }
                }
            }
        }

        $result = [
            'reship' => $reshipInfo,
            'return' => [],
        ];
        # 获取售后申请单
        if (!empty($reshipInfo['return_id'])) {
            $result['return'] = $returnProductMdl->dump(array('return_id' => $reshipInfo['return_id']), '*');
            # 设置是否部分退款标识
            # $result['reship']['is_part_refund'] = $result['return']['is_part_refund'] ?? '0';
        }
        # 获取退款单
        $sql = "select a.order_id,a.refund_apply_bn,a.last_modified,b.t_sent"
            . " from sdb_ome_refund_apply a LEFT JOIN sdb_ome_refunds b ON a.order_id =b.order_id AND a.refund_apply_bn = b.refund_bn"
            . " WHERE a.order_id = {$reshipInfo['order_id']} AND a.reship_id = " . $reship_id;
        $applyInfo = kernel::database()->selectrow($sql);
        if (!empty($applyInfo)) {
            $result['reship']['t_begin'] = empty($applyInfo['t_sent']) ? $applyInfo['last_modified'] : $applyInfo['t_sent'];
        }

        # 获取订单
        $orderInfo = $this->get_order_sdf($reshipInfo['order_id'], ['is_payment' => false, 'is_coupon' => false]);
        if (!empty($orderInfo)) {
            $result = array_merge($result, $orderInfo);
        }
        return $result;
    }

    /**
     * 获取订单的支付信息
     * @param $order_id
     * @return void
     */
    public function get_payments($params)
    {
        return [];
    }

    /**
     * 获取优惠信息
     * @param $order_id
     * @return void
     */
    public function get_coupons($order_id, $oid = [])
    {
        return [];
    }

    /**
     * 根据优惠券id获取优惠券列表
     * @param $order_id
     * @param $coupon_id
     * @return void
     */
    public function get_palateform_coupons($order_id, $coupon_id = [])
    {

    }

    /**
     * 平台活动、优惠相关
     * @param $order_id
     * @return void
     */
    protected function getPlateformPromotion($order_id)
    {
    }

    public function getOid($order_id, $oid)
    {
    }

    protected function is_lucky_order($orderInfo)
    {

    }

    /**
     * 获取原单的
     * @param $order_id
     * @param $oid
     * @param $type
     * @return mixed
     */
    public function getSourceOrderItems($order_id, $oid, $type = 'PAID')
    {
        if (empty($order_id) || empty($oid)) {
            return false;
        }

        $orderItemsMdl = app::get('sales')->model('sap_order_items');

        $filter = [
            'order_id' => $order_id,
            'orderType' => $type,
            'eshopOrderSn' => $oid
        ];
        $result = $orderItemsMdl->getList('order_id,eshopOrderSn,spuId,orderPrice,discAmount,payAmount', $filter);
        if (empty($result)) {
            return false;
        }
        return current($result);
    }
}