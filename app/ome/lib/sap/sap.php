<?php

class ome_sap_sap
{
    protected $current_type = 'sap';

    public function __construct()
    {
        $this->db = kernel::database();
    }

    /**
     * 推送订单
     * @param $order_id
     * @return void
     */
    public function push_order($order_id, $shop_type)
    {
        if (empty($order_id) || empty($shop_type)) {
            return array('rsp' => 'fail', 'msg' => '参数错误');
        }

        $params = [
            'order_id' => $order_id,
            'shop_type' => $shop_type,
            'push_type' => 'order'
        ];
        return $this->runTask($order_id, $params);
    }

    /**
     * 推送退款单
     * @param $apply_id
     * @return void
     */
    public function push_refund($apply_id, $shop_type)
    {
        if (empty($apply_id) || empty($shop_type)) {
            return array('rsp' => 'fail', 'msg' => '参数错误');
        }

        $params = [
            'apply_id' => $apply_id,
            'shop_type' => $shop_type,
            'push_type' => 'refund'
        ];
        return $this->runTask($apply_id, $params);
    }

    /**
     * 推送销售单
     * @param $delivery_id
     * @return mixed
     */
    public function push_delivery($delivery_id, $shop_type, $delivery_shipping_id = null)
    {
        if (empty($delivery_id) || empty($shop_type)) {
            return array('rsp' => 'fail', 'msg' => '参数错误');
        }

        $params = [
            'delivery_id' => $delivery_id,
            'delivery_shipping_id' => $delivery_shipping_id,
            'shop_type' => $shop_type,
            'push_type' => 'delivery'
        ];
        return $this->runTask($delivery_id, $params);
    }

    /**
     * 推送售后单
     * @param $reship_id
     * @return void
     */
    public function push_reship($reship_id, $shop_type, $is_push_change = false)
    {
        if (empty($reship_id) || empty($shop_type)) {
            return array('rsp' => 'fail', 'msg' => '参数错误');
        }

        $params = [
            'reship_id' => $reship_id,
            'shop_type' => $shop_type,
            'push_type' => 'reship',
            'is_push_change' => $is_push_change
        ];
        return $this->runTask($reship_id, $params);
    }

    /**
     * 加入队列处理
     * @param $log_id
     * @param $params
     * @return mixed
     */
    private function runTask($log_id, $params)
    {
        # 使用队列
        $push_params = array(
            'data' => array(
                'log_id' => $log_id,
                'task_type' => 'pushsap',
                'params' => json_encode($params, JSON_UNESCAPED_UNICODE)
            ),
            'url' => kernel::openapi_url('openapi.autotask', 'service')
        );
        return kernel::single('taskmgr_interface_connecter')->push($push_params);
    }

    /**
     * 获取退款单的esb商家金额
     * @param $order_id
     * @return mixed
     */
    public function _esb_refund_amount($apply_id)
    {
        $result = ['esb_amount' => 0, 'esb_pmt_amount' => 0];
        if (empty($apply_id)) {
            return $result;
        }

        $esb_reshipObj = kernel::single('erpapi_sap_request_reship');
        $reshipItemsMdl = app::get('ome')->model('reship_items');
        $orderObjectsMdl = app::get('ome')->model('order_objects');
        $ordersMdl = app::get('ome')->model('orders');
        $orderObj = kernel::single('ome_order');
        $applyMdl = app::get('ome')->model('refund_apply');

        # 退款申请单
        $applyInfo = $applyMdl->dump(array('apply_id' => $apply_id), '*');
        if (empty($applyInfo)) {
            return $result;
        }

        $apply_shop_type = $applyInfo['shop_type'];

        # 数据类
        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($apply_shop_type)));
        # 判断订单是否为换货单，换货单不推送sap
        $is_change = app::get('ome')->model('reship')->is_change_order($applyInfo['order_id']);

        # 如果是换货单，需要读取原始订单的esb金额
        if ($is_change) {
            if (!empty($applyInfo['reship_id'])) {
                # 获取退货单明细
                $reship_items = $reshipItemsMdl->getList('*', array('reship_id' => $applyInfo['reship_id'], 'return_type' => 'return'));
                # 获取原始订单的sku明细
                $return_items = $esb_reshipObj->_getSourceChangeItems($applyInfo['order_id'], $reship_items);
                foreach ($reship_items as $item) {
                    # 原换货单对应的退货入库sku
                    $source_return_item = $return_items[$item['product_id']];
                    if (empty($source_return_item)) {
                        continue;
                    }
                    $order_item_id = $source_return_item['order_item_id'];
                    # 查询子单号
                    $sql = "select a.item_id,b.oid from sdb_ome_order_items a"
                        . " LEFT JOIN sdb_ome_order_objects b ON a.order_id = b.order_id AND a.obj_id = b.obj_id"
                        . " WHERE a.item_id = {$order_item_id}";
                    $orderItem = kernel::database()->selectrow($sql);
                    if (!empty($orderItem['oid'])) {
                        $oids[] = $orderItem['oid'];
                    }
                }
            }

            # 获取原始订单编号
            $orderInfo = $ordersMdl->dump(array('order_id' => $applyInfo['order_id']), 'order_id,order_bn,relate_order_bn');
            $source_order_bn = $orderObj->getSourceOrder($orderInfo);
            if (!empty($source_order_bn) && $source_order_bn != $orderInfo['order_bn']) {
                $sourceOrderInfo = $ordersMdl->dump(array('order_bn' => $source_order_bn), 'order_id');
                $source_order_id = $sourceOrderInfo['order_id'];
            } else {
                $source_order_id = $orderInfo['order_id'];
            }
        } else {
            # 判断退款申请的子单号，没有子单号则直接取订单的子单号
            if (!empty($applyInfo['oid'])) {
                $oids = is_array($applyInfo['oid']) ? $applyInfo['oid'] : explode(',', $applyInfo['oid']);
            } else {
                $objectsList = $orderObjectsMdl->getList('oid', array('order_id' => $applyInfo['order_id']));
                $oids = empty($objectsList) ? [] : array_column($objectsList, 'oid');
            }
            # 非换货单，直接取当前订单ID
            $source_order_id = $applyInfo['order_id'];
        }

        if (empty($oids)) {
            return $result;
        }

        foreach ($oids as $oid) {
            # 子单号
            $esb_oid = $platformObj->getOid($source_order_id, $oid);
            # 根据oid查询推送esb明细的订单金额
            $esb_order_item = $platformObj->getSourceOrderItems($source_order_id, $esb_oid);
            $esb_amount = empty($esb_order_item) ? 0 : $esb_order_item['payAmount'];
            $esb_pmt_amount = empty($esb_order_item) ? 0 : $esb_order_item['discAmount'];

            $result['esb_amount'] = bcadd($result['esb_amount'], $esb_amount, 2);
            $result['esb_pmt_amount'] = bcadd($result['esb_pmt_amount'], $esb_pmt_amount, 2);
        }
        return $result;
    }

    /**
     * 根据发货单ID获取推送esb支付单数据
     * @param $delivery_id
     * @return mixed
     */
    public function getEsbPayedOrdersByDeliveryId($delivery_id)
    {
        if (empty($delivery_id)) {
            return array();
        }

        $dlyOrderMdl = app::get('ome')->model('delivery_order');
        $orderMdl = app::get('ome')->model('orders');
        $orderObj = kernel::single('ome_order');

        # 根据发货单获取所有订单ID
        $dlyOrders = $dlyOrderMdl->getList('order_id', array('delivery_id' => $delivery_id));
        if (empty($dlyOrders)) {
            return array();
        }

        # 获取订单列表
        $order_ids = array_column($dlyOrders, 'order_id');
        $orderList = $orderMdl->getList('order_id,order_bn,relate_order_bn,shop_id,shop_type', array('order_id' => $order_ids));
        if (empty($orderList)) {
            return array();
        }

        $result = array(
            'total_pay_amount' => 0,
            'total_disc_amount' => 0,
            'items' => []
        );

        foreach ($orderList as $order) {
            $order_id = $order['order_id'];
            # 获取原始订单
            $sourceOrderBn = $orderObj->getSourceOrder($order);
            if ($sourceOrderBn != $order['order_bn']) {
                $sourceOrderInfo = $orderMdl->dump(array('order_bn' => $sourceOrderBn), 'order_id');
                $order_id = $sourceOrderInfo['order_id'];
            }

            # 查询esb支付单
            $sql = "SELECT a.bill_id,a.bill_no,b.eshopOrderSn,b.payAmount,b.discAmount FROM sdb_sales_sap_orders a"
                . " LEFT JOIN sdb_sales_sap_order_items b ON a.sap_id = b.sap_id"
                . " WHERE a.bill_type = 'payed' AND a.bill_id = {$order_id} AND  a.sync_status = 'succ' AND a.shop_id = '{$order['shop_id']}'";
            $sapItemList = kernel::database()->select($sql);
            # 如果有合单或者推送esb失败的，都直接返回空数组
            if (empty($sapItemList)) {
                return array();
            }

            foreach ($sapItemList as $item) {
                # 累计实付和优惠金额
                $result['total_pay_amount'] = bcadd($result['total_pay_amount'], $item['payAmount'], 2);
                $result['total_disc_amount'] = bcadd($result['total_disc_amount'], $item['discAmount'], 2);

                # 格式化明细数据
                $key = sprintf('%s_%s', $item['bill_no'], $item['eshopOrderSn']);
                $result['items'][$key] = $item;
            }
        }
        return $result;
    }

    /**
     * 根据退货单ID获取推送esb支付单数据
     * @param $reship_id
     * @return mixed
     */
    public function getEsbPayedOrdersByReshipId($reship_id)
    {
        if (empty($reship_id)) {
            return array();
        }

        $reshipMdl = app::get('ome')->model('reship');
        $orderMdl = app::get('ome')->model('orders');
        $orderObj = kernel::single('ome_order');

        # 获取订单ID
        $reshipInfo = $reshipMdl->dump(array('reship_id' => $reship_id), 'order_id');
        if (empty($reshipInfo)) {
            return array();
        }

        # 获取订单列表
        $orderList = $orderMdl->getList('order_id,order_bn,relate_order_bn,shop_id,shop_type', array('order_id' => $reshipInfo['order_id']));
        if (empty($orderList)) {
            return array();
        }

        $result = array(
            'total_pay_amount' => 0,
            'total_disc_amount' => 0,
            'items' => []
        );

        foreach ($orderList as $order) {
            $order_id = $order['order_id'];
            # 获取原始订单
            $sourceOrderBn = $orderObj->getSourceOrder($order);
            if ($sourceOrderBn != $order['order_bn']) {
                $sourceOrderInfo = $orderMdl->dump(array('order_bn' => $sourceOrderBn), 'order_id');
                $order_id = $sourceOrderInfo['order_id'];
            }

            # 查询esb支付单
            $sql = "SELECT a.bill_id,a.bill_no,b.eshopOrderSn,b.payAmount,b.discAmount FROM sdb_sales_sap_orders a"
                . " LEFT JOIN sdb_sales_sap_order_items b ON a.sap_id = b.sap_id"
                . " WHERE a.bill_type = 'payed' AND a.bill_id = {$order_id} AND  a.sync_status = 'succ' AND a.shop_id = '{$order['shop_id']}'";
            $sapItemList = kernel::database()->select($sql);
            # 如果有合单或者推送esb失败的，都直接返回空数组
            if (empty($sapItemList)) {
                return array();
            }

            foreach ($sapItemList as $item) {
                # 累计实付和优惠金额
                $result['total_pay_amount'] = bcadd($result['total_pay_amount'], $item['payAmount'], 2);
                $result['total_disc_amount'] = bcadd($result['total_disc_amount'], $item['discAmount'], 2);

                # 格式化明细数据
                $key = sprintf('%s_%s', $item['bill_no'], $item['eshopOrderSn']);
                $result['items'][$key] = $item;
            }
        }
        return $result;
    }
}