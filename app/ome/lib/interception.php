<?php
class ome_interception {
    private $__interceptionObj = "";
    private $__operationLogObj = "";
    static public $max_retry_count = 500;
    public function __construct() {
        $this->__interceptionObj = app::get('ome')->model('interception');
        $this->__operationLogObj = app::get('ome')->model('operation_log');
    }
    public function check_delivery($delivery_id,$return_id,&$msg='') {
        $deliveryObj = app::get('ome')->model('delivery');
        $deliveryOrderObj = app::get('ome')->model('delivery_order');
        $orderObj = app::get('ome')->model('orders');
        $deliveryInfo = $deliveryObj->dump(['delivery_id'=>$delivery_id,'status'=>'succ']);
        $deliveryOrderInfo = $deliveryOrderObj->dump(['delivery_id'=>$delivery_id]);
        $return_product_obj = app::get('ome')->model('return_product');

        if(empty($deliveryInfo) || empty($deliveryOrderInfo)) {
            $msg = '发货单不存在';
            return false;
        }
        $orderInfo = $orderObj->dump(['order_id'=>$deliveryOrderInfo['order_id']]);
        if(empty($orderInfo)) {
            $msg = '订单不存在';
            return false;
        }


        $return_product_info = $return_product_obj->dump(['return_id'=>$return_id],'intercept_status');
        if($return_product_info['intercept_status'] == '3'){
            $msg = '';
            kernel::single('base_customlog')->saveLog('interception', ['find_error'=>$return_id.' 申请单已经拦截成']);
            return true;
        }

        $logi_no = $deliveryInfo['logi_no'];
        $waybill_info = app::get('logisticsmanager')->model('waybill')->dump(['waybill_number' => $logi_no]);
        if (!$waybill_info) {
            $msg = '未找到对应快递单';
            kernel::single('base_customlog')->saveLog('interception', ['find_error'=>$return_id.' 面单号表不存在，只能手动拦截!']);
            kernel::single('monitor_wechat_event_orderrefund')->sendInterception($return_id);
            return false;
        }

        if ($orderInfo['order_type'] == 'platform') {
            $msg = '平台订单不允许拦截';
            return false;
        }


        //判断申请单里面单的商品，和发货单里面的商品 是否对的上，对上了才拦截
        $delivery_items_details = app::get('ome')->model('delivery_items_detail')->getList('order_item_id',['delivery_id'=>$delivery_id]);
        $return_product_items = app::get('ome')->model('return_product_items')->getList('order_item_id',['return_id'=>$return_id]);


        //发货单和退货单的 总数量没对上
        if(count($delivery_items_details) != count($return_product_items)){

            //这里可以查，所有售后申请单，未拦截，未拒绝的单子 都查出来。和发货单做对比
            $return_order_id = $orderInfo['order_id'];
            $sql = "select order_item_id,return_id from `sdb_ome_return_product_items` where return_id in (select return_id from sdb_ome_return_product where return_type = 'refund' 
                                                        and intercept_status !='3' and status in('1','2','3','4','6','7','8') and order_id = $return_order_id )";

            $return_product_items = kernel::database()->select($sql);

            if(count($delivery_items_details) != count($return_product_items)) {
                $msg = '发货包裹存在部分退款商品，不支持拦截，需要联系客户收到货之后进行退货退款';
                return false;
            }
        }

        $same_number = 0;
        foreach ($delivery_items_details as $delivery_item_info){
            foreach ($return_product_items as $return_products_item_info){
                if($delivery_item_info['order_item_id'] == $return_products_item_info['order_item_id']){
                    $same_number+=1;
                    break;
                }
            }
        }

        if($same_number != count($delivery_items_details)){
            $msg = '发货包裹存在部分退款商品，不支持拦截，需要联系客户收到货之后进行退货退款';
            return false;
        }

        //一个物流单号对应多个发货单,生成拦截单，拦截状态为取消（这样给到申请单的日志就不会重复写了）
        $deliverys = $deliveryObj->getList('delivery_id',['logi_no'=>$logi_no,'status'=>'succ']);
        if($deliverys && count($deliverys)>1) {
            $this->delivery_more(['logi_no' => $logi_no, 'return_id' => $return_id, 'order_bn' => $orderInfo['order_bn']]);
            return true;
        }

        //如果同一个发货单，多个商品售后，那会下来多个售后单，这种情况也要拦截
        //1.如果发货单的商品的oid 和 对应多个售后单的商品 oid 能改匹配上
        //2.当前售后单为主单，其他的售后单为子单，都创建拦截记录
        //3.主单继续做拦截操作，如果主单拦截成功后，统一给子单创建退货单，如果失败了，统一改状态
        //4.重试的时候只重试主单
        $return_ids = array_unique(array_column($return_product_items,'return_id'));
        $return_ids = array_diff($return_ids,[$return_id]);

        //订单全额退款，发货状态为已发货，则可以进行拦截
        if($deliveryInfo['status']=='succ'){
            //创建系统拦截单
            $data = array(
                'delivery_id' => $delivery_id,
                'delivery_bn' => $deliveryInfo['delivery_bn'],
                'order_id' => $orderInfo['order_id'],
                'order_bn' => $orderInfo['order_bn'],
                'logi_id' => $deliveryInfo['logi_id'],
                'logi_name' => $deliveryInfo['logi_name'],
                'logi_no' => $deliveryInfo['logi_no'],
                'status' => 'ready',
                'branch_id' => $deliveryInfo['branch_id'],
                'delivery_time' => $deliveryInfo['delivery_time'],
                'shop_id' => $deliveryInfo['shop_id'],
                'shop_type' => $deliveryInfo['shop_type'],
                'create_time' => time(),
                'last_modified' => time(),
                'return_id'=>$return_id,
                'parent_return_id'=>0
            );

            //对应发货单拦截单据已拦截成功或者取消成功, 直接做拦截处理
            $interceptionInfo = $this->__interceptionObj->dump(['delivery_id'=>$delivery_id]);
            if(!empty($interceptionInfo)){
                if(in_array($interceptionInfo['status'],['succ','cancel_succ'])) {
                    $this->repeat_refund_succ($return_id, $delivery_id);
                    return true;
                }else{
                    $id = $this->__interceptionObj->insert($data);
                }
            }else{
                $id = $this->__interceptionObj->insert($data);
            }

        }
        if($id){

            //添加对应子单
            foreach ($return_ids as $returns_ids) {
                $this->add_son($data,$return_id,$returns_ids);
            }

            $this->__operationLogObj->write_log('interception_update@ome', $id, '系统拦截单创建成功');
            $this->interception_express($return_id,$msg);
            return true;
        }else{
            $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单('.$deliveryInfo['delivery_bn'].')系统拦截单创建失败');
            $msg = '系统拦截单创建失败';
            return false;
        }
        return true;
    }

    //拦截快递
    public function interception_express($return_id,&$msg){
        $interceptionInfo = $this->__interceptionObj->dump(['return_id'=>$return_id]);
        if(empty($interceptionInfo)){
            $msg = '拦截单据不存在';
            return false;
        }
        if(in_array($interceptionInfo['status'],['succ','cancel_succ'])){
            $msg = '拦截单当前状态无法拦截';
            return false;
        }
        $delivery_id = $interceptionInfo['delivery_id'];
        $deliveryObj = app::get('ome')->model('delivery');
        $deliveryInfo = $deliveryObj->dump(['delivery_id'=>$delivery_id,'status'=>'succ']);
        if(empty($deliveryInfo)) {
            $msg = '发货单不存在';
            $this->__interceptionObj->update(['status'=>'failed','error_msg'=>$msg],['id'=>$interceptionInfo['id']]);

            //son_add_fail

            return false;
        }
        $orderObj = app::get('ome')->model('orders');
        $orderInfo = $orderObj->dump(['order_id'=>$interceptionInfo['order_id']]);
        if(empty($orderInfo)) {
            $msg = '订单不存在';
            $this->__interceptionObj->update(['status'=>'failed','error_msg'=>$msg],['id'=>$interceptionInfo['id']]);

            //son_add_fail

            return false;
        }
        $dlyCorpObj = app::get('ome')->model('dly_corp');
        $return_product_obj = app::get('ome')->model('return_product');
        $dlyCorpInfo = $dlyCorpObj->dump(['corp_id'=>$deliveryInfo['logi_id']],'type,api_config');

        //修改售后申请单的拦截状态
        $order_id = $interceptionInfo['order_id'];
        $return_id = $interceptionInfo['return_id'];
        $return_product_info = $return_product_obj->dump(array('return_id'=>$return_id));

        //售后申请单已取消不做拦截
        if($return_product_info['status'] == '5'){
            $msg = '售后申请单已取消不做拦截';
            $this->__interceptionObj->update(['status'=>'cancel','error_msg'=>$msg],['id'=>$interceptionInfo['id']]);
            $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], $msg);
            return false;
        }


        //发货单揽件状态修改
        $deliveryObj->update(array('logi_status'=>'4'),array('delivery_id'=>$delivery_id));
        $this->__operationLogObj->write_log('delivery_modify@ome',$delivery_id,"拦截物流，物流状态更改为:退件/问题件");

        #更新wap发货单物流状态
        $deliveryLib = kernel::single('wap_event_receive_delivery');
        $params['logi_status'] = '4';
        $params['delivery_bn'] = $deliveryInfo['delivery_bn'];
        $deliveryLib->updateLogiStatus($params);

        //拦截快递
        $object = kernel::single('erpapi_router_request')->set('express', $deliveryInfo['logi_id']);
        $result = $object->express_cancel($deliveryInfo['delivery_bn']);

        if($result['rsp']!='succ'){
            $this->__interceptionObj->update(['status'=>'failed','error_msg'=>$result['msg']],['id'=>$interceptionInfo['id']]);
//            $this->__operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '系统拦截快递失败,'.$result['msg']);
//            $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单('.$deliveryInfo['delivery_bn'].')系统拦截快递失败,'.$result['msg']);
            $msg = $result['msg'];

            if($return_product_info) {
                //售后申请单失败记录
                $this->interception_fail($return_product_info['return_id']);
//                $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], '发货单('.$deliveryInfo['delivery_bn'].')系统拦截快递失败,'.$result['msg']);
                $memo = '';
                if($interceptionInfo['retry_count']>=ome_interception::$max_retry_count) {
                    $memo = '超过最大拦截次数,拦截失败';
                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], $memo);
                }

                //add_son_fail
                $this->son_fail($return_product_info['return_id'],$memo);
            }

            return false;
        }

        $is_succ = false;
        //京东快递拦截处理
        if(strtoupper($dlyCorpInfo['type'])=='JD') {
            if($result['data']['resultType']==4){//取消结果；枚举值：0 - 取消成功；1 - 拦截成功； 2 - 取消失败；3 - 拦截失败；4-拦截中，调用成功一定返回
                $this->__interceptionObj->update(['status'=>'running','error_msg'=>'拦截中'],['id'=>$interceptionInfo['id']]);

                //拦截中状态修改
                if($return_product_info) {
                    $return_product_obj->update(array('intercept_type'=>'system','belong_type'=>'customer','intercept_status'=>'2','wait_customer_check'=>'1') ,array('return_id'=>$return_product_info['return_id']));
//                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], 'JD发货单(' . $deliveryInfo['delivery_bn'] . ')系统拦截中');
                }

                //add_son_ing
                $this->son_ing($return_id);

                //超过 10 次 当失败处理
                if($interceptionInfo['retry_count'] >=ome_interception::$max_retry_count) {
                    $this->interception_fail($return_product_info['return_id']);
                    $this->__interceptionObj->update(['status'=>'failed','error_msg'=>$result['msg']],['id'=>$interceptionInfo['id']]);
                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], '超过最大拦截次数,拦截失败');

                    //add_son_fail
                    $this->son_fail($return_product_info['return_id'],'超过最大拦截次数,拦截失败');
                }

                return false;
            }

            if(in_array($result['data']['resultType'],[2,3])){
                $this->__interceptionObj->update(['status'=>'failed','error_msg'=>$result['msg']],['id'=>$interceptionInfo['id']]);
//                $this->__operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], 'JD系统拦截快递失败,'.$result['msg']);
//                $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], 'JD发货单('.$deliveryInfo['delivery_bn'].')系统拦截快递失败,'.$result['msg']);
                $msg = $result['msg'];

                //售后申请单失败记录
                $this->interception_fail($return_product_info['return_id']);
//                $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], 'JD发货单(' . $deliveryInfo['delivery_bn'] . ')系统拦截快递失败,' . $result['msg']);

                //超过 10 次 当失败处理
                $memo = '';
                if($interceptionInfo['retry_count'] >=ome_interception::$max_retry_count) {
                    $memo = '超过最大拦截次数,拦截失败';
                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], $memo);
                }

                //add_son_fail
                $this->son_fail($return_product_info['return_id'],$memo);

                return false;
            }

            if(in_array($result['data']['resultType'],[0,1])){
                //取消 和 拦截类型区分一下
                $cancel_type = $result['data']['resultType'] == 0?'cancel':'';
                $result_type = $result['data']['resultType'] == 0?'cancel_succ':'succ';
                //如果有返回物流单号，就用返回的物流单号
                $waybillCode = $result['data']['waybillCode'];
                if($waybillCode){
                    $deliveryInfo['return_logi_no'] = $waybillCode;
                }
                $is_succ = true;
                $deliveryInfo['return_id'] = $return_id;
                $reshipInfo = $this->createReship($orderInfo,$deliveryInfo,$cancel_type);
                $reship_bn = '';
                $reship_id = 0;
                if(!$reshipInfo){
                    $this->__operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '退货单创建失败,请手动创建退货单');
                    $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], 'JD发货单('.$deliveryInfo['delivery_bn'].')系统拦截退货单创建失败,请手动创建退货单');

                    if($return_product_info['return_id']) {
                        $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], 'JD发货单(' . $deliveryInfo['delivery_bn'] . ')系统拦截退货单创建失败,请手动创建退货单');
                    }
                }else{
                    $reship_bn = $reshipInfo['reship_bn'];
                    $reship_id = $reshipInfo['reship_id'];
                    $this->__operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '退货单创建成功,退货单号: '.$reship_bn);
                    $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], 'JD发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);

                    if($return_product_info['return_id']) {
                        $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], 'JD发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);
                    }
                }
                $this->__interceptionObj->update(['status'=>$result_type,'reship_bn'=>$reship_bn,'reship_id'=>$reship_id,'return_logi_no'=>$deliveryInfo['logi_no'],'error_msg'=>''],['id'=>$interceptionInfo['id']]);

                //add_son_succ
                $this->son_succ($return_id,$deliveryInfo,$cancel_type,$result_type);
            }
        }else{
            //其他快递拦截成功，创建系统退货单
            $is_succ = true;
            $deliveryInfo['return_id'] = $return_id;
            $reshipInfo = $this->createReship($orderInfo,$deliveryInfo);
            $reship_bn = '';
            $reship_id = 0;
            if(!$reshipInfo){
                $this->__operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '退货单创建失败,请手动创建退货单');
                $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单('.$deliveryInfo['delivery_bn'].')系统拦截退货单创建失败,请手动创建退货单');

                if($return_product_info['return_id']) {
                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], '发货单('.$deliveryInfo['delivery_bn'].')系统拦截退货单创建失败,请手动创建退货单');
                }

                $msg = '退货单创建失败,请手动创建退货单';
                $this->__interceptionObj->update(['status'=>'return_failed','error_msg'=>$msg],['id'=>$interceptionInfo['id']]);
                return false;
            }else{
                $reship_bn = $reshipInfo['reship_bn'];
                $reship_id = $reshipInfo['reship_id'];
                $this->__operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '退货单创建成功,退货单号: '.$reship_bn);
                $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);

                if($return_product_info['return_id']) {
                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], '发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);
                }
            }
            $this->__interceptionObj->update(['status'=>'succ','reship_bn'=>$reship_bn,'reship_id'=>$reship_id,'return_logi_no'=>$deliveryInfo['logi_no'],'error_msg'=>''],['id'=>$interceptionInfo['id']]);

            //add_son_succ
            $this->son_succ($return_id,$deliveryInfo,'','succ');
        }


        //修改售后申请单状态
        if($return_product_info && $is_succ) {
            $return_product_obj->update(array('intercept_type'=>'system','intercept_status'=>'3') ,array('return_id'=>$return_product_info['return_id']));
        }

        return true;
    }

    /**
     * 拦截失败
     */
    private function interception_fail($return_id){
        $return_product_obj = app::get('ome')->model('return_product');
        //售后申请单失败记录
        $return_product_obj->update(array('intercept_type'=>'system','intercept_status'=>'4','belong_type'=>'customer','wait_customer_check'=>'1') ,array('return_id'=>$return_id));
    }

    /**
     * 退货单没有匹配到发货单
     * @param $return_id
     * @param $delivery_bn
     * @param $msg
     */
    public function refund_not_find_delivery($return_id,$msg){
        $return_product_info = app::get('ome')->model('return_product')->dump(['return_id'=>$return_id]);
        if($return_product_info && $return_product_info['intercept_status'] != '4') {
            $this->interception_fail($return_id);
            $this->__operationLogObj->write_log('return@ome', $return_id, $msg);
        }
    }

    //取消快递
    public function cancel_express($return_id,&$msg){
        $interceptionInfo = $this->__interceptionObj->dump(['return_id'=>$return_id]);
        if(empty($interceptionInfo)){
            $msg = '拦截单据不存在';
            return false;
        }
        if(in_array($interceptionInfo['status'],['succ','cancel_succ'])){
            $msg = '拦截单当前状态无法拦截';
            return false;
        }

        $delivery_id = $interceptionInfo['delivery_id'];
        $deliveryObj = app::get('ome')->model('delivery');
        $deliveryInfo = $deliveryObj->dump(['delivery_id'=>$delivery_id,'status'=>'succ']);
        if(empty($deliveryInfo)) {
            $msg = '发货单不存在';
            $this->__interceptionObj->update(['status'=>'failed','error_msg'=>$msg],['id'=>$interceptionInfo['id']]);
            return false;
        }
        $orderObj = app::get('ome')->model('orders');
        $orderInfo = $orderObj->dump(['order_id'=>$interceptionInfo['order_id']]);
        if(empty($orderInfo)) {
            $msg = '订单不存在';
            $this->__interceptionObj->update(['status'=>'failed','error_msg'=>$msg],['id'=>$interceptionInfo['id']]);
            return false;
        }
        $dlyCorpObj = app::get('ome')->model('dly_corp');
        $dlyCorpInfo = $dlyCorpObj->dump(['corp_id'=>$deliveryInfo['logi_id']],'type,api_config');

        //取消运单
        if(in_array($dlyCorpInfo['type'],['SF','DBL','DBKD'])){

            $wapDelivery = app::get('wap')->model('delivery')->dump(array('outer_delivery_bn' => $deliveryInfo['delivery_bn']), 'delivery_id');
            $result = kernel::single('wap_event_trigger_logistics')->recycleWaybill($wapDelivery['delivery_id'],$interceptionInfo['id']);
            if($result['rsp'] =='fail') {
                $this->__operationLogObj->write_log('interception_update@ome', $interceptionInfo['id'], '系统取消快递失败,' . $result['msg']);
                $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单(' . $deliveryInfo['delivery_bn'] . ')系统取消快递失败,' . $result['msg']);

                $this->__operationLogObj->write_log('return@ome', $interceptionInfo['return_id'], '发货单(' . $deliveryInfo['delivery_bn'] . ')系统取消快递失败,' . $result['msg']);

                $msg = $result['msg'];
                return false;
            }

         }
        return true;
    }

    public function createReship($orderInfo,$deliveryInfo,$cancel_type=''){
        $delivery_id = $deliveryInfo['delivery_id'];
        $return_id = $deliveryInfo['return_id'];
        if(!$return_id){
            return false;
        }

        //找到申请单的商品
        $return_product_items = app::get('ome')->model('return_product_items')->getList('order_item_id,product_id',['return_id'=>$return_id]);
        $return_product_product_ids = array_column($return_product_items, 'product_id');

        $reshipObj = app::get('ome')->model('reship');
        $deliveryItemsObj = app::get('ome')->model('delivery_items');
        $deliveryItems = $deliveryItemsObj->getList('*',['delivery_id'=>$delivery_id,'product_id'=>$return_product_product_ids]);
        $product_ids = array_column($deliveryItems, 'product_id');
        $orderItemsObj = app::get('ome')->model('order_items');
        $orderItems = $orderItemsObj->getList('*',['order_id'=>$orderInfo['order_id'],'product_id'=>$product_ids]);

        foreach ($orderItems as $item){
            foreach ($deliveryItems as $key=>$ditem){
                if($item['product_id']==$ditem['product_id']){
                    $deliveryItems[$key]['item_id'] = $item['item_id'];
                    $deliveryItems[$key]['price'] = $item['divide_order_fee'];
                }
            }
        }
        $reshipItems = array(
            'goods_name' => [],
            'bn' => [],
            'product_id' => [],
            'effective' => [],
            'goods_bn' => [],
            'price' => [],
            'num' => [],
            'branch_id' => [],
        );

        $storeInfo = [];
        foreach ($deliveryItems as $item) {
            $reshipItems['goods_name'][$item['item_id']] = $item['product_name'];
            $reshipItems['bn'][$item['item_id']] = $item['bn'];
            $reshipItems['product_id'][$item['item_id']] = $item['product_id'];
            $reshipItems['effective'][$item['item_id']] = $item['number'];
            $reshipItems['goods_bn'][] = $item['item_id'];
            $reshipItems['price'][$item['item_id']] = $item['price'];
            $reshipItems['num'][$item['item_id']] = $item['number'];
            $reshipItems['branch_id'][$item['item_id']] = $deliveryInfo['branch_id'];

            if(!$storeInfo) {
                $storeInfo = kernel::single("o2o_store_material")->getBmDelivStore($item['bn'], $orderInfo['order_id']);
            }
        }

        $return_product = app::get('ome')->model('return_product')->dump(array('return_id'=>$return_id));

        //有对应退货单，并且退货单没拒绝的不创建
        $old_reship_info = $reshipObj->dump(['return_id'=>$return_id]);
        if($old_reship_info && $old_reship_info['is_check'] != '5'){
            return false;
        }

//        echo '<pre>';
//        print_r($orderItems);
//        print_r($deliveryItems);
//        print_r($reshipItems);
//        exit;
        $reship_bn = $return_product['return_bn'];//substr($reshipObj->gen_id(),2);
        $reshipData = array(
            'reship_bn'=>$reship_bn,
            'return_type' => 'return',
            'supplier' => $orderInfo['order_bn'],
            'order_id' => $orderInfo['order_id'],
            'is_protect' => 'false',
            'shop_id' => $orderInfo['shop_id'],
            'member_id' => $orderInfo['member_id'],
            'return_logi_name' => $deliveryInfo['return_logi_name'] ? $deliveryInfo['return_logi_name'] : $deliveryInfo['logi_name'],
            'return_logi_no' => $deliveryInfo['return_logi_no'] ? $deliveryInfo['return_logi_no'] : $deliveryInfo['logi_no'],
            'logi_name' => $deliveryInfo['logi_name'],
            'logi_id' => $deliveryInfo['logi_id'],
            'logi_no' => $deliveryInfo['logi_no'],
            'ship_name' => $deliveryInfo['consignee']['name'],
            'ship_area' => $deliveryInfo['consignee']['area'],
            'ship_addr' => $deliveryInfo['consignee']['addr'],
            'ship_zip' => $deliveryInfo['consignee']['zip'],
            'ship_tel' => $deliveryInfo['consignee']['telephone'],
            'ship_email' => $deliveryInfo['consignee']['email'],
            'ship_mobile' => $deliveryInfo['consignee']['mobile'],
            'delivery' => '快递',
            'send_branch_id' => $deliveryInfo['branch_id'],
            'branch_id' => $deliveryInfo['branch_id'],
            'problem_type' => [1],
            'bcmoney' => 0,
            'bmoney' => 0,
            'cost_freight_money' => 0,
            'return' => $reshipItems,
            'is_confirm' => true,
            'source_type' => 'intercept',
            'belong_type'=>'store',
            'source'=>$return_product['source'],
            'belong_store_id'=>$storeInfo['store_id'],
            'belong_store_bn'=>$storeInfo['store_bn'],
            'return_id'=>$return_id,
            'tmoney'=>$return_product['refundmoney'],
            'totalmoney'=>$return_product['refundmoney'],
        );
        $msg = '';
//        print_r($reshipData);

        //拦截成功马上同意退款
        // $res = kernel::single('ome_service_aftersale')->update_status($return_id, '4', 'sync');
        // kernel::single('base_customlog')->saveLog('interception', ['aftersale_res'=>$res]);


        $reship_bn = $reshipObj->create_treship($reshipData, $msg);
        if($reship_bn==false){
            return false;
        }else{
            $reshipInfo = $reshipObj->dump(['reship_bn'=>$reship_bn],'reship_id');

            $reshipLib = kernel::single('ome_reship');
            $rs = $reshipLib->confirm_reship(array(
                'reship_id' => $reshipInfo['reship_id'],
                'status'    => '1',
                'is_anti'   => false,
                'exec_type' => 1,
            ), $msg);
            if(!$rs) {
                app::get('ome')->model('operation_log')->write_log('reship@ome', $reshipInfo['reship_id'], '自动审核失败:'.$msg);
            }

            kernel::single("o2o_store_return")->auto_interceptSucc($return_id, $deliveryInfo['logi_no'],'system');

            if($cancel_type == 'cancel'){
                //取消接口成功状态发货单状态修改
                $deliveryLib = kernel::single('wap_event_receive_delivery');
                $deliveryObj = app::get('ome')->model('delivery');

                $params['logi_status'] = '8';
                $params['delivery_bn'] = $deliveryInfo['delivery_bn'];
                $deliveryLib->updateLogiStatus($params);

                $delivery_bn = $deliveryInfo['delivery_bn'];
                $update_sql = "update sdb_ome_delivery set logi_status = '8' where delivery_bn = '$delivery_bn' ";
                $deliveryObj->db->exec($update_sql);

                $this->__operationLogObj->write_log('return@ome', $return_id, '发货单(' . $deliveryInfo['delivery_bn'] . ')取消成功,发货单状态改成 拦截退回');
            }

            return ['reship_bn'=>$reship_bn,'reship_id'=>$reshipInfo['reship_id']];
        }
    }



    public function sendEmail($params)
    {
        $return_info = app::get('ome')->model('return_product')->dump(['return_id' => $params['return_id']], 'return_bn');
        kernel::single('monitor_event_notify')->addNotify('interception_logi_fail', [
            'delivery_bn' => $params['delivery_bn'],
            'order_bn' => $params['order_bn'],
            'return_bn' => $return_info['return_bn'],
            'logi_no' => $params['logi_no'],
            'errmsg' => $params['error_msg']
        ], true);
    }

    /**
     * 拦截循环预警
     * @param $params
     */
    public function sendEmailWhile($params)
    {
        kernel::single('monitor_event_notify')->addNotify('interception_fail_while', [
            'delivery_bn' => $params['delivery_bn'],
        ], true);
    }

    /**
     * 通过订单号获取申请单
     * @param $order_id
     * @return mixed
     */
    private function get_refund_product($order_id){
        //1.售后申请单的 intercept_status 有值 的 最新一个，因为退款转的申请单 intercept_status = 1
        $return_product_info = kernel::database()->selectrow("select * from sdb_ome_return_product where order_id = $order_id and intercept_type !='0' order by return_id desc ");
        return $return_product_info;
    }

    /**
     * 手动修改拦截成功
     * @param $return_id
     */
    public function self_interceptSucc($return_id)
    {
        $interception_info = $this->__interceptionObj->dump(['return_id' => $return_id]);
        if ($interception_info && $interception_info['status'] != 'succ') {
            $id = $interception_info['id'];
            $this->__interceptionObj->update(['status' => 'succ'], ['id' => $id]);
            $this->__operationLogObj->write_log('return@ome', $return_id, '手动拦截,同步拦截单据');
        }
    }

    /**
     * 重复的售后申请单成功
     * @param $return_id
     */
    public function repeat_refund_succ($return_id,$delivery_id)
    {
        $return_product_obj = app::get('ome')->model('return_product');
        $return_product_info = $return_product_obj->dump(['return_id'=>$return_id]);

        $deliveryInfo = app::get('ome')->model('delivery')->dump(['delivery_id'=>$delivery_id]);
        $orderInfo = app::get('ome')->model('orders')->dump(['order_id'=>$return_product_info['order_id']]);

        $deliveryInfo['return_id'] = $return_id;

        //看对应发货单的拦截是取消拦截成功，还是拦截成功
        $interceptionInfo = $this->__interceptionObj->dump(['delivery_id'=>$delivery_id]);
        $cancel_type = '';
        if($interceptionInfo['status'] == 'cancel_succ'){
            $cancel_type = 'cancel';
        }

        $reshipInfo = $this->createReship($orderInfo,$deliveryInfo,$cancel_type);

        if(!$reshipInfo){
            $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '发货单('.$deliveryInfo['delivery_bn'].')售后重复申请,拦截退货单创建失败,请手动创建退货单');
            $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], '发货单('.$deliveryInfo['delivery_bn'].')售后重复申请,生成拦截退货单创建失败,请手动创建退货单');
            return false;
        }else{
            $reship_bn = $reshipInfo['reship_bn'];
            $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], '售后重复申请,发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);
            $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], '售后重复申请,发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);

        }

        $return_product_obj->update(array('intercept_type'=>'system','intercept_status'=>'3') ,array('return_id'=>$return_id));
    }


    /**
     * 一个物流单号，多个发货单拦截
     */
    private function delivery_more($params)
    {
        $logi_no = $params['logi_no'];
        $return_id = $params['return_id'];

        $return_product_obj = app::get('ome')->model('return_product');
        $return_product_info = $return_product_obj->dump(['return_id' => $return_id]);
        $error_msg = $logi_no . '多包裹，不支持部分拦截';
        //创建拦截失败订单
        $data = array(
            'delivery_id' => 0,
            'delivery_bn' => '',
            'order_id' => $return_product_info['order_id'],
            'order_bn' => $params['order_bn'],
            'logi_id' => 0,
            'logi_name' => '',
            'logi_no' => $logi_no,
            'status' => 'cancel',
            'branch_id' => 0,
            'shop_id' => $return_product_info['shop_id'],
            'shop_type' => $return_product_info['shop_type'],
            'create_time' => time(),
            'last_modified' => time(),
            'return_id' => $return_id,
            'error_msg' => $error_msg
        );
        $this->__interceptionObj->insert($data);
        $this->__operationLogObj->write_log('return@ome', $return_id, $error_msg);
        $this->interception_fail($return_id);
    }

    /**
     * 添加子单
     * @param $data
     * @param $parent_return_id
     * @param $return_id
     */
    private function add_son($data,$parent_return_id,$return_id){
        $return_intercet_info = $this->__interceptionObj->dump(['return_id' => $return_id], 'id,return_id');
        //已经有的不处理
        if($return_intercet_info){
            return false;
        }

        $data['return_id'] = $return_id;
        $data['parent_return_id'] = $parent_return_id;
        $this->__interceptionObj->insert($data);
        return true;
    }

    /**
     * 子单拦截成功
     * @param $return_id
     */
    public function son_succ($return_id,$deliveryInfo,$cancel_type,$result_type){
        //创建退货单，添加日志等
        $interceptions = $this->__interceptionObj->getList('*',['parent_return_id'=>$return_id]);
        $return_product_obj = app::get('ome')->model('return_product');
        $order_obj = app::get('ome')->model('orders');


        foreach ($interceptions as $interception_info){
            $interception_return_id = $interception_info['return_id'];
            $return_product_info = $return_product_obj->dump(array('return_id'=>$interception_return_id));

            //售后申请单已取消不做拦截
            if($return_product_info['status'] == '5'){
                $msg = '售后申请单已取消不做拦截!';
                $this->__interceptionObj->update(['status'=>'cancel','error_msg'=>$msg],['id'=>$interception_info['id']]);
                $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], $msg);
                return false;
            }

            $orderInfo = $order_obj->dump(['order_id'=>$return_product_info['order_id']]);

            $deliveryInfo['return_id'] = $interception_return_id;
            $reshipInfo = $this->createReship($orderInfo,$deliveryInfo,$cancel_type);
            $reship_bn = '';
            $reship_id = 0;
            if(!$reshipInfo){
                $this->__operationLogObj->write_log('interception_update@ome', $interception_info['id'], '退货单创建失败,请手动创建退货单');
                $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], 'JD发货单('.$deliveryInfo['delivery_bn'].')系统拦截退货单创建失败,请手动创建退货单');

                if($return_product_info['return_id']) {
                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], 'JD发货单(' . $deliveryInfo['delivery_bn'] . ')系统拦截退货单创建失败,请手动创建退货单');
                }
            }else{
                $reship_bn = $reshipInfo['reship_bn'];
                $reship_id = $reshipInfo['reship_id'];
                $this->__operationLogObj->write_log('interception_update@ome', $interception_info['id'], '退货单创建成功,退货单号: '.$reship_bn);
                $this->__operationLogObj->write_log('order_modify@ome', $orderInfo['order_id'], 'JD发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);

                if($return_product_info['return_id']) {
                    $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], 'JD发货单('.$deliveryInfo['delivery_bn'].')快递拦截退货单创建成功,退货单号: '.$reship_bn);
                }
            }
            $this->__interceptionObj->update(['status'=>$result_type,'reship_bn'=>$reship_bn,'reship_id'=>$reship_id,'return_logi_no'=>$deliveryInfo['logi_no'],'error_msg'=>''],['id'=>$interception_info['id']]);

            //更改售后单状态
            $return_product_obj->update(array('intercept_type'=>'system','intercept_status'=>'3') ,array('return_id'=>$return_product_info['return_id']));
        }
    }

    public function son_fail($return_id,$memo=''){
        $interceptions = $this->__interceptionObj->getList('*',['parent_return_id'=>$return_id]);
        $return_product_obj = app::get('ome')->model('return_product');

        foreach ($interceptions as $interception_info){
            $interception_return_id = $interception_info['return_id'];
            $return_product_info = $return_product_obj->dump(array('return_id'=>$interception_return_id));

            //售后申请单已取消不做拦截
            if($return_product_info['status'] == '5'){
                $msg = '售后申请单已取消不做拦截!';
                $this->__interceptionObj->update(['status'=>'cancel','error_msg'=>$msg],['id'=>$interception_info['id']]);
                $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], $msg);
                return false;
            }

            $this->interception_fail($interception_return_id);
            $this->__interceptionObj->update(['status'=>'failed'],['id'=>$interception_info['id']]);
            if($memo) {
                $this->__operationLogObj->write_log('return@ome', $interception_return_id, $memo);
            }
        }
    }

    private function son_ing($return_id,$memo=''){
        $interceptions = $this->__interceptionObj->getList('*',['parent_return_id'=>$return_id]);
        $return_product_obj = app::get('ome')->model('return_product');

        foreach ($interceptions as $interception_info){
            $interception_return_id = $interception_info['return_id'];
            $return_product_info = $return_product_obj->dump(array('return_id'=>$interception_return_id));

            //售后申请单已取消不做拦截
            if($return_product_info['status'] == '5'){
                $msg = '售后申请单已取消不做拦截!';
                $this->__interceptionObj->update(['status'=>'cancel','error_msg'=>$msg],['id'=>$interception_info['id']]);
                $this->__operationLogObj->write_log('return@ome', $return_product_info['return_id'], $msg);
                return false;
            }

            $this->__interceptionObj->update(['status'=>'running','error_msg'=>'拦截中'],['id'=>$interception_info['id']]);

            //拦截中状态修改
            if($return_product_info) {
                $return_product_obj->update(array('intercept_type'=>'system','belong_type'=>'customer','intercept_status'=>'2','wait_customer_check'=>'1') ,array('return_id'=>$interception_return_id));
            }
        }
    }

}
