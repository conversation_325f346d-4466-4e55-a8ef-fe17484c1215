<?php

class ome_ecapi_order extends ome_ecapi_api{

    public function ziti_verify($order_bn, $verify_code, &$err_msg = '', $code_type = ''){
        $title = "自提订单核销";
        $ordInfo = $this->checkOrder($order_bn, $err_msg);
        if(!$ordInfo){
            return false;
        }
        if(!$verify_code){
            $err_msg = "自提码不能为空";
            return false;
        }

        $config  = $this->get_config($ordInfo['shop_id']);
        if(!$config){
            $err_msg = "店铺直连接口未配置";
            return false;
        }
        if(!$config['direct_api_url']){
            $err_msg = "店铺未配置直连API地址";
            return false;
        }
        $params = array(
            'to_node_id' => $config['node_id'],
            'act' => $this->get_act_type('ziti_verify'),
            'code' => $verify_code,
        );
        if($code_type == 'qrcode'){
            $params['type'] = 'qrcode';
        }else{
            $params['type'] = 'code';
            $params['order_id'] = $order_bn;
        }
        $opLogMdl = app::get('ome')->model('operation_log');
        $params['ac'] = $this->gen_sign($params, $config['direct_api_token']);
        $res = $this->curl($config['direct_api_url'], $params);
        if($res && $res['result'] == 'success'){
            $this->write_log($title, $order_bn, 'success', $params, $params, $res);
            $opLogMdl->write_log('order_modify@ome',$ordInfo['order_id'],$title."成功");
            return true;
        }
        $err_msg = $res['msg'] ?: "前端店铺核销失败";
        $this->write_log($title, $order_bn, 'fail', $params, $params, $res);
        $opLogMdl->write_log('order_modify@ome',$ordInfo['order_id'],$title."失败".$err_msg);
        return false;
    }

    /**
     * 校验订单是否可以核销
     * @param $order_bn
     * @param $err_msg
     * @return false
     */
    public function checkOrder($order_bn, &$err_msg){
        $oMdl = app::get("ome")->model("orders");
        $ordInfo = $oMdl->dump(array("order_bn" => $order_bn), "*");
        if(empty($ordInfo)){
            $err_msg  = "订单不存在";
            return false;
        }
        if($ordInfo['pay_status'] != '1'){
            $err_msg = "订单支付状态不支持核销";
            return false;
        }
        if($ordInfo['ship_status'] != '0'){
            $err_msg = "订单发货状态不支持核销";
            return false;
        }
        if($ordInfo['ziti_verify_status'] == '2'){
            $err_msg = "订单已核销，请勿重复核销";
            return false;
        }
        //检查是否存在有效退款申请
        $raMdl = app::get("ome")->model("refund_apply");
        $filter = array(
            'order_id' => $ordInfo['order_id'],
            'status' => ['0','1','2','4','5']
        );
        $raInfo = $raMdl->getList("*", $filter);
        if(!empty($raInfo)){
            $err_msg = "订单存在有效退款申请，无法核销";
            return false;
        }

        return $ordInfo;
    }

    /**
     * 获取前端店铺订单信息
     * @param $order_bn
     * @param $shop_id
     * @param string $err_msg
     */
    public function getPlatformOrdInfo($order_bn, $shop_id, &$err_msg = ''){
        if (!$order_bn) {
            $err_msg = '订单号不能为空!';
            return false;
        }
        if (!$shop_id) {
            $err_msg = '店铺ID不能为空!';
            return false;
        }
        $rsp_data = kernel::single('erpapi_router_request')->set('shop', $shop_id)->order_get_order_detial($order_bn);
        if ($rsp_data['rsp'] == 'succ') {
            return $rsp_data['data'];
        }else{
            $err_msg = '订单不存在!';
            return false;
        }
    }
}