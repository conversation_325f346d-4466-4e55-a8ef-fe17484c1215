<?php
class ome_order_history
{

    // 处理退款订单
    public function processRefund($sdf)
    {
        $history_oid = array_column($sdf['items'], 'history_oid');
        $order_objects = app::get('ome')->model('order_objects')->getList('*', array('history_oid' => $history_oid));
        if (empty($order_objects)) {
            // 未找到历史子订单
            return false;
        }

        $orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $order_objects['order_id']), '*');
        if ($sdf['status'] == '已退款') {
            $status = '4';
        } elseif ($sdf['status'] == '退款中') {
            $status = '5';
        } else {
            $status = '0';
        }

        $obj_ids = array_column($order_objects, 'obj_id');

        $order_items = app::get('ome')->model('order_items')->getList('*', array('order_id' => $order_objects['order_id'], 'obj_id' => $obj_ids));
        if (empty($order_items)) {
            // 未找到订单商品
            return false;
        }

        // 通过其中一个商品判断是否已发货
        if ($order_items[0]['sendnum'] == 0) {
            $refund_refer = '0';
        } else {
            $refund_refer = '1';
        }

        if ($refund_refer) {
            $this->processAftersale($orderInfo, $order_objects, $order_items, $sdf);
        }

        $oids = implode(',', array_column($order_objects, 'oid'));
        //data
        $data = array(
            'order_id' => $order_objects['order_id'],
            'refund_apply_bn' => $sdf['refund_bn'],
            'pay_type' => 'online',
            'account' => $sdf['account'],
            'money' => $sdf['refund_fee'],
            'refunded' => $sdf['refund_fee'],
            'memo' => '历史退款单',
            'create_time' => time(),
            'status' => $status,
            'shop_id' => $orderInfo['shop_id'],
            'addon' => serialize(array('refund_bn' => $sdf['refund_bn'])),
            'source' => 'matrix',
            'shop_type' => $orderInfo['shop_type'],
            'outer_lastmodify' => time(),
            'refund_refer' => $refund_refer, //退款来源
            'org_id' => $orderInfo['org_id'],
            'bn' => $order_items[0]['bn'],
            'oid' => $oids, //oid子单
            'bool_type' => $sdf['bool_type'],
            'is_history' => 'true',
        );

        foreach ($order_items as $order_item) {
            $arrProduct[] = array(
                'product_id' => $order_item['product_id'],
                'bn' => $order_item['bn'],
                'name' => $order_item['name'],
                'num' => $order_item['num'], // 需要使用传入的退款数量
                'price' => $order_item['price'], // 需要改成传入的退款金额
                'order_item_id' => $order_item['item_id'],
                'oid' => $oids,
                'modified' => time(),
            );
        }
        $data['product_data'] = serialize($arrProduct);
        //根据商品编码获取门店信息
        $store = kernel::single("o2o_store_material")->getBmStore($order_items[0]['bn']);
        if ($store) {
            $belong_type = $store['performance_type'] == 'store' ? 'store' : 'town';
            $store_id = $store['store_id'];
        } else {
            $belong_type = 'customer';
            $store_id = 0;
        }
        $data['belong_store_id'] = $store_id;
        $data['belong_type'] = $belong_type;

        app::get('ome')->model('refund_apply')->insert($data);
        if ($status == '4') {
            $refundData = array(
                'refund_bn' => $sdf['refund_bn'],
                'shop_id' => $orderInfo['shop_id'],
                'order_id' => $order_objects['order_id'],
                'currency' => 'CNY',
                'money' => $sdf['refund_fee'],
                'cur_money' => $sdf['refund_fee'],
                'pay_type' => 'online',
                'download_time' => time(),
                'status' => 'succ',
                'memo' => '历史退款单',
                'trade_no' => $sdf['refund_bn'],
                'modifiey' => time(),
                'payment' => '在线支付',
                't_ready' => time(),
                't_sent' => time(),
                't_received' => time(),
                'org_id' => $orderInfo['org_id'],
                'refund_refer' => $refund_refer, //退款来源
                'is_history' => 'true',
            );

            //存在退款申请单且存在门店ID和所属类型
            if ($data['belong_store_id'] && $data['belong_type']) {
                $refundData['belong_store_id'] = $data['belong_store_id'];
                $refundData['belong_type'] = $data['belong_type'];
            }
            app::get('ome')->model('refunds')->insert($refundData);
        }
    }

    // 处理售后订单
    public function processAftersale($orderInfo, $sdf)
    {
        $modelReturnProduct = app::get('ome')->model('return_product');

        $opInfo = kernel::single('ome_func')->get_system();
        $data = array(
            'return_bn' => $sdf['refund_bn'],
            'shop_id' => $orderInfo['shop_id'],
            'member_id' => $orderInfo['member_id'],
            'order_id' => $orderInfo['order_id'],
            'title' => $orderInfo['order_bn'] . '售后申请单',
            'content' => '历史售后单',
            'comment' => '历史售后单',
            'add_time' => time(),
            'status' => '4',
            'op_id' => $opInfo['op_id'],
            'refundmoney' => $sdf['refund_fee'],
            'money' => $sdf['refund_fee'],
            'shipping_type' => 'EXPRESS',
            'source' => 'matrix',
            'shop_type' => $orderInfo['shop_type'],
            'outer_lastmodify' => time(),
            'delivery_id' => $sdf['delivery_id'],
            'memo' => '',
            'org_id' => $orderInfo['org_id'],
            'flag_type' => '0',
            'platform_status' => 'SUCCESS', //平台售后单状态
            'apply_remark' => '', //售后申请描述
            'kinds' => 'reship',
            'jsrefund_flag' => '0', //极速退款标识
            'is_history' => 'true',
        );

        //售后类型
        $data['return_type'] = 'return';

        //平台订单号
        $data['platform_order_bn'] = $orderInfo['order_bn'];

        $storeInfo = [];
        foreach ($sdf['refund_item_list'] as $val) {
            $data['return_product_items'][] = array(
                'product_id' => $val['product_id'] ? $val['product_id'] : 0,
                'bn' => $val['bn'],
                'name' => $val['title'] ? $val['title'] : $val['name'],
                'num' => $val['num'],
                'price' => $val['price'],
                'amount' => $val['amount'],
                'branch_id' => $sdf['branch_id'],
                'order_item_id' => $val['order_item_id'],
                'shop_goods_bn' => $val['shop_goods_bn'],
                'obj_type' => $val['obj_type'],
                'quantity' => $val['quantity'],
            );

            //获取发货门店信息
            $storeInfo = kernel::single("o2o_store_material")->getBmDelivStore($val['bn'], $data['order_id']);
        }
        $data['is_fail'] = 'false';
        //退货单增加赠品明细
        $data['return_gift_items'] = app::get('ome')->model('reship')->addReturnGiftItems($data['return_product_items'], $data['order_id'], $sdf['branch_id']);

        $data['belong_store_id'] = isset($storeInfo['store_id']) ? $storeInfo['store_id'] : 0;
        $data['belong_store_bn'] = isset($storeInfo['store_bn']) ? $storeInfo['store_bn'] : '';

        //获取商品org_id
        if ($data['return_product_items']) {
            $bn = $data['return_product_items'][0]['bn'];
            $data['org_id'] = kernel::single('o2o_store_material')->getOrgByMaterialBn($bn);
        }

        $modelReturnProduct->insert($data);
        $returnProductItems = $data['return_product_items'];
        foreach ($returnProductItems as &$val) {
            $val['return_id'] = $data['return_id'];
        }
        $modelItem = app::get('ome')->model('return_product_items');
        $sql = ome_func::get_insert_sql($modelItem, $returnProductItems);
        $modelItem->db->exec($sql);
    }

    // 生成退换货单，并且自动完成
    public function createReship($data)
    {
        $modelReship = app::get('ome')->model('reship');
        
        // 设置退换货单基本信息
        $reshipData = array(
            'reship_bn' => $data['refund_bn'],
            'order_id' => $data['order_id'],
            'platform_order_bn' => $data['platform_order_bn'],
            'shop_id' => $data['shop_id'],
            'member_id' => $data['member_id'],
            'return_type' => $data['return_type'],
            'problem_id' => $data['problem_id'] ? $data['problem_id'] : 0,
            'status' => 'succ', // 自动完成
            'is_check' => '7',
            'return_reason' => $data['return_reason'] ? $data['return_reason'] : '历史退换货单',
            'created_time' => time(),
            'modified_time' => time(),
            'finish_time' => time(),
            'belong_store_id' => $data['belong_store_id'],
            'belong_store_bn' => $data['belong_store_bn'],
            'org_id' => $data['org_id'],
            'is_history' => 'true'
        );

        // 插入退换货单
        $modelReship->insert($reshipData);
        $reship_id = $reshipData['reship_id'];
        
        // 处理退换货商品明细
        if ($reship_id && !empty($data['return_product_items'])) {
            $reshipItems = array();
            foreach ($data['return_product_items'] as $item) {
                $reshipItems[] = array(
                    'reship_id' => $reship_id,
                    'product_id' => $item['product_id'],
                    'bn' => $item['bn'],
                    'name' => $item['name'],
                    'num' => $item['num'],
                    'price' => $item['price'],
                    'amount' => $item['amount'],
                    'order_item_id' => $item['order_item_id'],
                    'shop_goods_bn' => $item['shop_goods_bn'],
                    'obj_type' => $item['obj_type'],
                    'quantity' => $item['quantity']
                );
            }
            // 批量插入退换货商品明细
            $modelReshipItem = app::get('ome')->model('reship_items');
            $sql = ome_func::get_insert_sql($modelReshipItem, $reshipItems);
            $modelReshipItem->db->exec($sql);
        }
        
        return $reship_id;
    }

    // 处理历史订单
    public function processOrder($shop_id, $order_bn, $deliveryList)
    {
        $res = $this->getOrder($shop_id, $order_bn, $deliveryList);
        if ($res) {
            // 获取订单ID
            $orderInfo = app::get('ome')->model('orders')->dump(['order_bn' => $order_bn], 'order_id,shop_id');
            if (!$orderInfo) {
                return false;
            }
            $order_id = $orderInfo['order_id'];

            if (!empty($deliveryList)) {
                // 按照导入的发货单，生成OMS发货单
                foreach ($deliveryList as $delivery) {
                    $this->processDelivery($order_id, $delivery);
                }
            }
        }
        return true;
    }

    // 获取订单
    public function getOrder($shop_id, $order_bn, $deliveryList)
    {

        $delivery_items = [];
        // 发货商品
        if (!empty($deliveryList)) {
            foreach ($deliveryList as $logi_no => $delivery) {
                $delivery_items[] = array_merge($delivery_items, $delivery['items']);
            }
        }

        $obj_syncorder = kernel::single("ome_syncorder");
        // 创建订单
        $return_data = kernel::single('erpapi_router_request')->set('shop', $shop_id)->order_get_order_detial($order_bn);
        if ($return_data['rsp'] == 'success' || $return_data['rsp'] == 'succ') {

            $sdf_order = $return_data['data']['trade'];
            $sdf_order['ship_status'] = 'SHIP_NO';
            $sdf_order['pay_status'] = 'PAY_FINISH';

            // 处理历史物料替换为最新基础物料
            $sdf_order = $this->updateHistoryMaterial($sdf_order, $delivery_items);
            if ($sdf_order) {
                // 未发货，但是不存在商品
                error_log($order_bn . "\n", 3, 'history_order_error_bn.csv');
                return false;
            }

            $sdf_order['is_history'] = '1';
            $msg = '';
            $obj_syncorder->get_order_log($sdf_order, $shop_id, $msg);

            $orderInfo = app::get('ome')->model('orders')->dump(['order_bn' => $order_bn], 'order_id');
            if (!$orderInfo) {
                // 创建订单失败
                return false;
            }
            foreach ($delivery_items as $row) {
                // 通过订单ID和sku_id匹配，更新历史oid
                $order_objects = app::get('ome')->model('order_objects');
                $order_object = $order_objects->dump(['order_id' => $orderInfo['order_id'], 'oid' => $row['items']['sku_id']], 'obj_id');

                if ($order_object) {
                    // 更新历史子订单号
                    $order_objects->update([
                        'history_oid' => $row['items']['history_oid'] ?: $row['items']['sku_id']
                    ], [
                        'obj_id' => $order_object['obj_id'],
                        'order_id' => $orderInfo['order_id']
                    ]);
                }
            }
            //增加订单的操作日志：导入历史订单
            $log_model = app::get('ome')->model('operation_log');
            $log_model->write_log('order_import', $orderInfo['order_id'], '导入历史订单：' . $order_bn);
        } else {
            // 记录单拉失败订单记录
            error_log($order_bn . "\n", 3, 'history_order_error_get.csv');
            return false;
        }
        return true;
    }

    // 处理历史物料替换为最新基础物料
    function updateHistoryMaterial($sdf_order, $delivery_items)
    {
        foreach ($sdf_order['order_objects'] as &$row) {
            // 判断销售物料是否存在，如果不存在则新增销售物料和基础物料
            $material_info = app::get('material')->model('basic_material')->dump(['material_bn' => $row['bn']], 'bm_id');
            if ($delivery_items[$row['oid']]) {
                $row['is_history'] = 'true';
                if (empty($material_info)) {
                    $this->createMaterial($row, $delivery_items[$row['oid']]);

                    $material_info = app::get('material')->model('basic_material')->dump(['material_bn' => $row['bn']], 'bm_id');

                    // 更新关联表的图片 
                    app::get('material')->model('basic_material_ext')->update(['banner' => $delivery_items[$row['oid']]['image']], ['bm_id' => $material_info['bm_id']]);
                }
            } else {
                $row['is_history'] = 'false';
            }
        }
        return $sdf_order;
    }

    function createMaterial($objects, $item)
    {
        $order_items = $objects['order_items']['order_items'][0];
        $color = '';
        $size = '';
        foreach ($order_items['sku_properties'] as $sku_properties) {
            if ($sku_properties['label'] == '颜色') {
                $color = $sku_properties['value'];
            }

            if ($sku_properties['label'] == '尺码') {
                $size = $sku_properties['value'];
            }
        }
        #拼接数据
        $master = [
            'material_bn' => $objects['bn'],
            'material_spu' => $objects['bn'],
            'cost' => $objects['price'],
            'retail_price' => $objects['sale_price'],
            'spu_id' => $objects['bn'],
            'material_name' => $objects['title'],
            'store_bn' => $item['customerStoreCode'],
            'status' => '1',  // 商品状态：0-下架，1-上架
            'material_spu_id' => $item['largeSize'],  // 商品大码
            'busness_material_bn' => $item['itemNo'], // 商户货号
            'updatetime' => time(),
            'color' => $item['color'] ? $item['color'] : $color,
            'size' => $item['size'] ? $item['size'] : $size,
        ];

        $result['data'] = $master;
        $params = array();
        $error_msg = '';
        kernel::single('material_kucun100')->updateMaterial($params, $result, $error_msg);

        $this->updateMaterialToHistory($objects['bn']);
    }

    /**
     * 将基础物料更新为历史商品
     * 
     * @param string $material_bn 物料编号
     * @return boolean 更新结果
     */
    public function updateMaterialToHistory($material_bn)
    {
        if (empty($material_bn)) {
            return false;
        }

        $material_model = app::get('material')->model('basic_material');
        $material_info = $material_model->dump(['material_bn' => $material_bn], 'bm_id');

        if (empty($material_info)) {
            return false;
        }

        $update_data = [
            'is_history' => 'true',
            'updatetime' => time()
        ];

        return $material_model->update($update_data, ['material_bn' => $material_bn]);
    }

    public function processDelivery($order_id, $deliveryItems)
    {
        $delivery = $this->getDeliveryItems($order_id, $deliveryItems);
        // 创建发货单
        $res = $this->addDelivery($order_id, $delivery);
        if ($res['rsp'] == 'succ') {
            $delivery_bn = $res['data'];
        } else {
            error_log($order_id . "\n", 3, 'history_order_error_delivery.csv');
            return false;
        }

        // 模拟发货
        $msg = '';
        $rs = $this->simulateDelivery($delivery_bn, $deliveryItems, $msg);
        if (!$rs) {
            error_log($order_id . "\n", 3, 'history_order_error_delivery.csv');
            return false;
        }
        return true;
    }

    // 模拟发货
    public function simulateDelivery($delivery_bn, $deliveryItems, &$msg)
    {

        $wapDeliveryInfo = app::get('wap')->model('delivery')->dump(array('delivery_bn' => $delivery_bn), '*');

        // 模拟发货
        $wap_delivery_id = $wapDeliveryInfo['delivery_id'];
        foreach ($deliveryItems as $item) {
            $logi_no = $item['logi_no'];
            $logi_id = $item['logi_id'];
            $logi_code = $item['logi_code']; // 物流公司编码
            $logi_name = $item['logi_name']; // 物流公司名称
            break;
        }

        $msg = '';
        // 补录快递单号
        $data = array('delivery_id' => $wap_delivery_id, 'logi_no' => $logi_no, 'logi_code' => $logi_code);
        $res = kernel::single('wap_delivery_process')->insertExpress($data);
        if ($res['rsp'] == 'succ') {

            $orderObj    = app::get('ome')->model('orders');

            $dlyObj = app::get('ome')->model('delivery');
            $deliveryInfo = $dlyObj->dump(array('delivery_bn' => $delivery_bn), '*', array('delivery_items' => array('*'), 'delivery_order' => array('*')));

            $de     = $deliveryInfo['delivery_order'];
            $or     = array_shift($de);
            $ord_id = $or['order_id'];

            $updateSdf = array();
            $updateSdf['logi_no'] = $logi_no;
            $updateSdf['logi_id'] = $logi_id;
            $orderObj->update($updateSdf, array('order_id' => $ord_id));

            //订单发货数量更新
            $dlyObj->consignOrderItem($deliveryInfo);

            $singledly['logi_id'] = $logi_id;
            $singledly['logi_name'] = $logi_name;
            $singledly['logi_no'] = $logi_no;
            // 更新主发货单
            $singledly['delivery_id']          = $deliveryInfo['delivery_id'];
            $singledly['delivery_bn']          = $delivery_bn;
            $singledly['process']              = 'true';
            $singledly['status']               = 'succ';
            $singledly['logi_status']          = '7';
            $singledly['delivery_time']        = time();

            //打印状态
            $singledly['expre_status'] = 'true';
            $singledly['deliv_status'] = 'true';
            $singledly['stock_status'] = 'true';


            $affect_row = $dlyObj->update($singledly, array('delivery_id' => $singledly['delivery_id'], 'process' => 'false'));
            if (!is_numeric($affect_row) || $affect_row <= 0) {
                $msg = '发货单发货状态更新失败!';
                return false;
            }
            $operationLogObj = app::get('ome')->model('operation_log');
            $operationLogObj->write_log('delivery_process@ome', $deliveryInfo['delivery_id'], '发货单发货完成,（发货单号：' . $delivery_bn . '）', '');

            $item_num = $dlyObj->countOrderSendNumber($ord_id);
            if ($item_num == 0) {
                $orderInfo = $orderObj->db_dump(['order_id' => $ord_id]);
                // 检测京东订单是否有微信支付先用后付的单据
                $use_before_payed = false;
                if ($orderInfo['shop_type'] == '360buy') {
                    $labelCode = kernel::single('ome_bill_label')->getLabelFromOrder($orderInfo['order_id']);
                    $labelCode = array_column($labelCode, 'label_code');
                    $use_before_payed = kernel::single('ome_order')->canDeliveryFromBillLabel($labelCode);
                }
                if (($deliveryInfo['is_cod'] == 'false' && !$use_before_payed) || ($deliveryInfo['is_cod'] == 'false' && $use_before_payed && $orderInfo['pay_status'] == '1') || ($deliveryInfo['is_cod'] == 'true' && $orderInfo['pay_status'] == '1')) {
                    $orderdata['status'] = 'finish';
                }
                $orderdata['archive']     = 1; //订单归档
                $orderdata['ship_status'] = '1';
                $orderdata['delivery_time'] = time();
                $affect_order             = $orderObj->update($orderdata, array('order_id' => $ord_id)); //更新订单发货状态
            } else {
                //部分发货
                $orderdata['ship_status'] = '2';
                $orderdata['delivery_time'] = time();
                $affect_order             = $orderObj->update($orderdata, array('order_id' => $ord_id)); //更新订单发货状态
            }

            if (!is_numeric($affect_order) || $affect_order <= 0) {
                $msg      = '订单状态更新失败!';
                return false;
            }

            //标记当前门店履约订单已发货
            kernel::single('ome_o2o_performance_orders')->updateProcessStatus($ord_id, 'consign');

            $wdMdl = app::get('console')->model('wms_delivery');
            $wdRow = $wdMdl->db_dump(['delivery_id' => $deliveryInfo['delivery_id'], 'delivery_status' => '2'], 'id');
            if ($wdRow) {
                $wdRs = $wdMdl->update(['delivery_status' => '3'], ['id' => $wdRow['id'], 'delivery_status' => '2']);
                if (!is_bool($wdRs)) {
                    app::get('ome')->model('operation_log')->write_log('wms_delivery@console', $wdRow['id'], '历史发货单完成');
                }
            }
        } else {
            $msg = 'H5模拟发货失败!';
            return false;
        }

        return true;
    }

    private function getDeliveryItems($order_id, $deliveryItems)
    {
        $order = app::get('ome')->model('orders')->dump(['order_id' => $order_id], '*', array('order_objects' => array('*', array('order_items' => array('*')))));

        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', $order['shop_type']));

        $delivery = [];
        foreach ($order['order_objects'] as $obj) {

            if (!$deliveryItems[$obj['oid']]) {
                continue;
            }

            foreach ($obj['order_items'] as $item) {
                # 过滤已删除的明细
                if ($item['delete'] == 'true') {
                    continue;
                }

                # 子单号
                $esb_oid = $platformObj->getOid($order['order_id'], $obj['oid']);
                # 根据oid查询推送esb明细的订单金额
                $esb_order_item = $platformObj->getSourceOrderItems($order['order_id'], $esb_oid);
                $esb_amount = empty($esb_order_item) ? 0 : $esb_order_item['payAmount'];
                $esb_pmt_amount = empty($esb_order_item) ? 0 : $esb_order_item['discAmount'];

                if ($delivery['delivery_items'][$item['product_id']]) {
                    $delivery['delivery_items'][$item['product_id']]['number'] += $item['nums'];
                    # 累计ESB商家收入、ESB优惠金额
                    $delivery['delivery_items'][$item['product_id']]['esb_amount'] += $esb_amount;
                    $delivery['delivery_items'][$item['product_id']]['esb_pmt_amount'] += $esb_pmt_amount;
                } else {
                    $delivery['delivery_items'][$item['product_id']] = array(
                        'item_type' => $item['item_type'],
                        'product_id' => $item['product_id'],
                        'shop_product_id' => $item['shop_product_id'],
                        'bn' => $item['bn'],
                        'number' => $item['nums'],
                        'product_name' => $item['name'],
                        'spec_info' => $item['addon'],
                        'esb_amount' => $esb_amount,         // ESB商家收入
                        'esb_pmt_amount' => $esb_pmt_amount, // ESB优惠金额
                    );
                }

                $delivery['branch_id'] = $item['fulfillment_store_id'];
                $delivery['consignee'] = $order['consignee'];
                $delivery['logi_id'] = $deliveryItems[$obj['oid']]['logi_id'];

                $delivery['order_items'][] = array(
                    'item_id' => $item['item_id'],
                    'product_id' => $item['product_id'],
                    'number' => $item['nums'],
                    'bn' => $item['bn'],
                    'product_name' => $item['name'],
                    'oid' => $obj['oid'],
                    's_type' => $obj['s_type'],
                    'esb_amount' => $esb_amount,         // ESB商家收入
                    'esb_pmt_amount' => $esb_pmt_amount, // ESB优惠金额
                );
            }
        }
        return $delivery;
    }

    /*
     * 新建发货单
     *
     * @param bigint $order_id 订单id
     * @param array $ship_info 收货人相关信息
     * @param $split_status  拆单后订单状态
     * @param $is_diff_order 补差价订单生成已发货的发货单 不涉及库存
     * @return $int $delivery_id 发货单id
     */
    public function addDelivery($order_id, $delivery)
    {
        $order_items = $delivery['order_items'];

        $oOrder    = app::get("ome")->model("orders");
        $oDly_corp = app::get("ome")->model("dly_corp");
        $branchObj = app::get("ome")->model("branch");

        // 验证明细
        if (!$delivery['delivery_items']) {
            return array('rsp' => 'fail', 'msg' => '没有明细');
        }

        // 减少死锁概率，以product_id排序
        usort($delivery['delivery_items'], array('ome_mdl_delivery', 'cmp_delivery_productid'));

        //开启添加发货单事务,锁定当前订单记录
        kernel::database()->exec('begin');
        //防止订单编辑与生成发货单并发导致错误
        $oOrder->update(['last_modified' => time()], ['order_id' => $order_id]);
        $psRow = app::get('ome')->model('order_platformsplit')->db_dump(['order_id' => $order_id], 'id');
        if ($psRow) {
            return array('rsp' => 'fail', 'msg' => '已经进行京东平台拆，不能生成发货单');
        }
        $order = $oOrder->dump($order_id);

        $ship_info           = $delivery['consignee'] ? $delivery['consignee'] : $order['consignee'];
        $delivery_bn         = $delivery['delivery_bn'] ? $delivery['delivery_bn'] : app::get('ome')->model('delivery')->gen_id();
        $data['delivery_bn'] = $delivery_bn;
        $is_protect          = $delivery['is_protect'] ? $delivery['is_protect'] : $order['shipping']['is_protect'];

        $is_cod = $delivery['is_cod'] ? $delivery['is_cod'] : $order['shipping']['is_cod'];
        if ($is_cod) {
            $data['is_cod'] = $is_cod;
        }
        if ($order['order_type'] == 'vopczc') {
            $delivery['type'] = 'vopczc';
        }
        $data['delivery']       = $delivery['delivery'] ? $delivery['delivery'] : $order['shipping']['shipping_name'];
        $data['logi_id']        = $delivery['logi_id'];
        $data['memo']           = $delivery['memo'];
        $data['delivery_group'] = $delivery['delivery_group'];
        $data['sms_group']      = $delivery['sms_group'];
        $data['branch_id']      = $delivery['branch_id'];
        $data['wms_channel_id'] = $delivery['wms_channel_id'] ?: kernel::single('console_delivery_yjdf')->getWMSChannelId($delivery['branch_id'], $delivery['delivery_items']); //WMS渠道ID

        //平台订单号
        if (kernel::single('ome_order_bool_type')->isJDLVMI($order['order_bool_type'])) {
            $data['platform_order_bn'] =   $order['platform_order_bn'];
        } elseif ($order['platform_order_bn']) {
            $data['platform_order_bn'] = $order['platform_order_bn'];
        }

        $logi_name = "";
        if ($delivery['type']) {
            $data['type'] = $delivery['type'];
        }

        //计算预计物流费用
        $weight = 0;
        if (isset($delivery['weight'])) {
            $weight = $delivery['weight'];
        } else {
            //[拆单]根据发货单中货品详细读取重量
            $orderSplitLib = kernel::single('ome_order_split');
            $split_seting  = $orderSplitLib->get_delivery_seting();

            if ($split_seting) {
                $weight = $orderSplitLib->getDeliveryWeight($order_id, $order_items);
            } else {
                $weight = app::get('ome')->model('orders')->getOrderWeight($order_id);
            }
        }

        list($area_prefix, $area_chs, $area_id) = explode(':', $ship_info['area']);

        $price = 0.00;
        if ($delivery['logi_id']) {
            $price     = app::get('ome')->model('delivery')->getDeliveryFreight($area_id, $delivery['logi_id'], $weight);
            $dly_corp  = $oDly_corp->dump($delivery['logi_id']);
            $logi_name = $dly_corp['name'];
            //计算保价费用
            $protect = $dly_corp['protect'];
            if ($protect == 'true') {
                $is_protect    = 'true';
                $protect_price = $dly_corp['protect_rate'] * $weight;
                $cost_protect  = max($protect_price, $dly_corp['minprice']);
            }
        }

        //[同城配]配送方式
        if ($dly_corp['corp_model'] == 'instatnt') {
            //同城配送
            $data['delivery'] = 'instatnt';
        } elseif ($dly_corp['corp_model'] == 'seller') {
            //商家配送
            $data['delivery'] = 'seller';
        }

        //order has logi_info：aikucun
        if ($delivery['delivery_waybillCode']) {
            $data['logi_no'] = $delivery['delivery_waybillCode'];
        }

        if ($delivery['delivery_sub_waybillCode']) {
            $data['logi_number'] = count($delivery['delivery_sub_waybillCode']) + 1;
        }

        $data['logi_name']            = $logi_name;
        $data['is_protect']           = $is_protect ? $is_protect : 'false';
        $data['create_time']          = time();
        $data['cost_protect']         = $cost_protect ? $cost_protect : '0';
        $data['net_weight']           = $weight;
        $data['delivery_cost_expect'] = $price;
        $data['member_id']            = $delivery['member_id'] ? $delivery['member_id'] : $order['member_id'];
        $data['shop_id']              = $order['shop_id'];
        $data['shop_type']            = $order['shop_type'];
        $data['org_id']               = $order['org_id'];

        $data['delivery_items'] = $delivery['delivery_items'];
        $data['consignee']      = $ship_info;

        //支持四、五级地区
        $temp_area                     = explode('/', $area_chs);
        $data['consignee']['province'] = $temp_area[0];
        $data['consignee']['city']     = $temp_area[1];
        $data['consignee']['district'] = $temp_area[2];
        $data['consignee']['town']     = empty($temp_area[3]) ? '' : $temp_area[3];
        $data['consignee']['village']  = empty($temp_area[4]) ? '' : $temp_area[4];

        $data['order_createtime'] = ($order['paytime'] && $is_cod == 'false') ? $order['paytime'] : $order['createtime']; #付款时间为空时取创建时间

        $opInfo          = kernel::single('ome_func')->getDesktopUser();
        $data['op_id']   = $opInfo['op_id'];
        $data['op_name'] = $opInfo['op_name'];

        //得物急速现货
        if ($order['order_type'] == 'jisuxianhuo') {
            $data['bool_type'] = $data['bool_type'] | ome_delivery_bool_type::__JISU_CODE;
        }

        if ($order['order_type'] == 'platform') {
            $data['original_delivery_bn'] = $delivery_bn;
            $data['bool_type'] = ($data['bool_type'] ? $data['bool_type'] : 0) | ome_delivery_bool_type::__PLATFORM_CODE;
        }
        if ($order['shop_type'] == '360buy') {
            if (kernel::single('ome_bill_label_shsm')->isTinyPieces($order['order_id'])) {
                $data['bool_type'] = ($data['bool_type'] ? $data['bool_type'] : 0) | ome_delivery_bool_type::__SHSM_CODE;
            }
        }
        $bns      = array();
        $totalNum = 0;
        foreach ($data['delivery_items'] as $v) {
            $totalNum += $v['number'];
            $bns[$v['product_id']] = $v['bn'];
        }
        ksort($bns);

        //11.25新增
        $data['skuNum']     = count($delivery['delivery_items']);
        $data['itemNum']    = $totalNum;
        $data['bnsContent'] = serialize($bns);
        $data['idx_split']  = $data['skuNum'] * 10000000000 + sprintf("%u", crc32($data['bnsContent']));

        $data['bind_key'] = app::get('ome')->model('delivery')->getBindKey($data);
        $data['bool_type']  = (int)$data['bool_type'];
        $data['is_history'] = true;

        //save
        $result = app::get('ome')->model('delivery')->save($data);

        if (!$result || !$data['delivery_id']) {
            kernel::database()->rollBack();
            return array('rsp' => 'fail', 'msg' => '发货单生成失败');
        }

        if ($delivery['type'] != 'reject') {
            //库存管控
            $storeManageLib = kernel::single('ome_store_manage');
            $storeManageLib->loadBranch(array('branch_id' => $delivery['branch_id']));

            $params = array();
            $params['params']    = array_merge($delivery, array('order_id' => $order_id, 'shop_id' => $order['shop_id'], 'delivery_id' => $data['delivery_id'], 'order_type' => $order['order_type']));
            $params['node_type'] = 'addDly';

            if (!kernel::single('ome_order_object_splitnum')->addDeliverySplitNum($order_items)) {
                kernel::database()->rollBack();
                return array('rsp' => 'fail', 'msg' => '明细已经生成发货单');
            }

            // 判断是否已经拆分完
            $is_splited   = app::get('ome')->model('order_items')->is_splited($order_id);
            $split_status = $is_splited ? 'splited' : 'splitting';
            app::get('ome')->model('orders')->update(['process_status' => $split_status], ['order_id' => $order_id]);

            //有优惠明细记录，实付进行重算
            $order_items = app::get('ome')->model('delivery')->_regroupDeliveryItemDetailData($order_id, $order_items);
        }

        if ($data['delivery_id'] && !empty($order_items) && is_array($order_items)) {
            app::get('ome')->model('delivery')->create_delivery_items_detail($data['delivery_id'], $order_items);
        }

        //插关联表
        if ($order_id) {
            $rs  = kernel::database()->exec('SELECT * FROM sdb_ome_delivery_order WHERE 0=1');
            $ins = array('order_id' => $order_id, 'delivery_id' => $data['delivery_id']);
            $sql = kernel::single("base_db_tools")->getinsertsql($rs, $ins);
            kernel::database()->exec($sql);
        }

        //更新订单相应状态
        app::get('ome')->model('delivery')->updateOrderLogi($data['delivery_id'], $data);

        //标签写入发货单
        kernel::single('ome_bill_label')->orderToDeliveryLabel($order_id, $data['delivery_id']);

        kernel::database()->commit();

        return array('rsp' => 'succ', 'data' => $data['delivery_bn']);
    }
}
