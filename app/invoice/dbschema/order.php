<?php
$db['order'] = array(
    'columns' => array(
        'id'                => array(
            'type'            => 'int(10)',
            'pkey'            => true,
            'extra'           => 'auto_increment',
            'required'        => true,
            'label'           => '编号',
            'filterdefault'   => true,
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 60,
            'hidden'          => true,
            'order'           => 10,
        ),
        'order_id'          => array(
            'type'            => 'int(11)',
            'default'         => 0,
            'label'           => '订单id',
            'in_list'         => false,
            'default_in_list' => false,
        ),
        'order_bn'          => array(
            'type'            => 'text',
            'label'           => '订单号',
            'searchtype'      => 'nequal',
            'filterdefault'   => true,
            'filtertype'      => 'textarea',
            'in_list'         => true,
            'default_in_list' => true,
            'width'           => 240,
            'order'           => 20,
        ),
        'type_id'           => array(
            'type'            => array(
                0 => '普通发票',
                1 => '专用发票',
            ),
            'default'         => '0',
            'label'           => '发票类型',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 100,
        ),
        'mode'              => array(
            'type'            => array(
                0 => '纸质发票',
                1 => '电子发票',
            ),
            'default'         => '0',
            'required'        => true,
            'label'           => '开票方式',
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'order'           => 3,
            'width'           => 70,
        ),
        'amount'            => array(
            'type'            => 'money',
            'default'         => '0',
            'required'        => true,
            'label'           => '开票金额',
            'filtertype'      => 'number',
            'filterdefault'   => true,
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 40,
            'width'           => 80,
        ),
        'cost_tax'          => array(
            'type'            => 'money',
            'default'         => '0',
            'required'        => true,
            'label'           => '税金',
            'in_list'         => true,
            'default_in_list' => true,
            'order'           => 50,
            'width'           => 60,
        ),
        'tax_rate'          => array(
            'type'            => 'tinyint(2)',
            'default'         => '0',
            'label'           => '税率',
            'order'           => 52,
            'in_list'         => false,
            'default_in_list' => false,
        ),
        'hsbz'              => array(
            'type'    => 'char(10)',
            'default' => '1',
            'label'   => '含税标志', #0:不含税  1:含税
        ),
        'yhzcbs'            => array(
            'type'    => 'char(10)',
            'default' => '0',
            'label'   => '是否使用优惠政策', #0:未使用，1:使用
        ),
        'zzstsgl'           => array(
            'type'  => 'varchar(255)',
            'label' => '增值税特殊管理', #如果yhzcbs为1时，此项必填，具体信息取百望《商品和服务税收分类与编码》.xls中的增值税特殊管理列
        ),
        'lslbs'             => array(
            'type'  => 'char(10)',
            'label' => '零税率标识', #1 出口免税和其他免税优惠政策;2 不征增值税;3 普通零税率
        ),
        'title'             => array(
            'type'            => 'varchar(150)',
            'label'           => '发票抬头',
            'in_list'         => true,
            'default_in_list' => true,
            'searchtype'      => 'head',
            'filtertype'      => 'normal',
            'filterdefault'   => false,
            'order'           => 25,
            'width'           => 200,
        ),
        'is_status'         => array(
            'type'            => array(
                0 => '未开票',
                1 => '已开票',
                2 => '已作废',
            ),
            'default'         => '0',
            'required'        => true,
            'label'           => '开票状态',
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'order'           => 5,
            'width'           => 70,
        ),
        'sync'              => array(
            'type'    => array(
                0 => '未同步',
                1 => '开蓝中',
                2 => '开蓝失败',
                3 => '开蓝成功',
                4 => '冲红中',
                5 => '冲红失败',
                6 => '冲红成功',
                7 => '冲红确认中',
                8 => '冲红确认失败',
                9 => '冲红确认成功',
                10 => '金税三期冲红',
            ),
            'label'   => '状态同步',
            'default' => '0',
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'width'=>80,
        ),
        'is_print'          => array(
            'type'    => array(
                0 => '否',
                1 => '是',
            ),
            'default' => '0',
            'label'   => '打印',
        ),
        'invoice_code'      => array(
            'type'            => 'varchar(32)',
            'label'           => '发票代码',
            'in_list'         => true,
            'default_in_list' => false,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'width'           => 70,
        ),
        'invoice_no'        => array(
            'type'            => 'varchar(32)',
            'label'           => '发票号码',
            'in_list'         => true,
            'default_in_list' => false,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'width'           => 80,
        ),
        'content'           => array(
            'type'    => 'text',
            'label'   => '发票内容', #旧发票的内容，保留这个字段
            'order'   => 23,
        ),
        'batch_number'      => array(
            'type'            => 'varchar(100)',
            'label'           => '批次号',
            'in_list'         => false,
            'default_in_list' => false,
            'searchtype'      => 'nequal',
            'filterdefault'   => true,
            'order'           => 28,
        ),
        'delivery_id'       => array(
            'type'            => 'int(10)',
            'default'         => '0',
            'required'        => true,
            'label'           => '发货单号',
            'in_list'         => false,
            'default_in_list' => false,
        ),
        'remarks'           => array(
            'type'            => 'text',
            'label'           => '发票备注',
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'operator'          => array(
            'type'            => 'int(10)',
            'default'         => '0',
            'required'        => true,
            'in_list'         => true,
            'default_in_list' => false,
            'label'           => '操作人',
        ),
        'print_num'         => array(
            'type'     => 'smallint(6)',
            'default'  => '0',
            'required' => true,
            'label'    => '打印次数',
        ),
        'create_time'       => array(
            'type'            => 'time',
            'default'         => '0',
            'label'           => '创建时间',
            'filtertype'      => 'time',
            'filterdefault'   => true,
            'in_list'         => true,
            'default_in_list' => true,
            'width'           => 130,
        ),
        'dateline'          => array(
            'type'            => 'time',
            'default'         => '0',
            'label'           => '开票时间',
            'filtertype'      => 'time',
            'filterdefault'   => true,
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 130,
        ),
        'update_time'       => array(
            'type'     => 'int(10)',
            'default'  => '0',
            'required' => true,
            'label'    => '更新时间',
        ),
        //此字段将废弃
        'tax_company'       => array(
            'type'            => 'varchar(255)',
            'label'           => '客户名称',
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'ship_area'         => array(
            'type'  => 'varchar(100)',
            'label' => '收票地区',
        ),
        'ship_addr'         => array(
            'type'            => 'text',
            'label'           => '收票地址',
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'ship_tel'          => array(
            'type'            => 'varchar(200)',
            'label'           => '收票电话',
            'in_list'         => true,
            'default_in_list' => false,
        ),

        'ship_email' => array(
            'type'     => 'varchar(150)',
            'label'    => '收票人Email',
            'comment'  => '收票人Email',
            'editable' => false,
            'in_list'  => true,
        ),
        'ship_name'          => array(
            'type'            => 'varchar(200)',
            'label'           => '收票联系人',
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'ship_tax'          => array(
            'type'            => 'varchar(255)',
            'label'           => '客户税号',
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'ship_bank'         => array(
            'type'            => 'varchar(255)',
            'label'           => '客户开户银行',
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'ship_bank_no'      => array(
            'type'            => 'varchar(255)',
            'label'           => '客户银行账号',
            'in_list'         => true,
            'default_in_list' => false,
        ),
        'ship_company_addr' => array(
            'type'    => 'varchar(255)',
            'label'   => '注册地址',
            'in_list' => true,
        ),
        'ship_company_tel' => array(
            'type'    => 'varchar(255)',
            'label'   => '注册电话',
            'in_list' => true,
        ),
        'shop_id'           => array(
            'type'            => 'table:shop@ome',
            'default'         => 0,
            'required'        => true,
            'editable'        => false,
            'label'           => '来源店铺',
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'order'           => 11,
        ),
        'shop_type'         => array(
            'type'     => 'varchar(50)',
            'label'    => '店铺类型',
            'width'    => 75,
            'editable' => false,
        ),
        'payee_name'        => array(
            'type'            => 'varchar(255)',
            'label'           => '开票方名称',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 90,
        ),
        'tax_no'            => array(
            'type'            => 'varchar(255)',
            'label'           => '开票方税号',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 90,
        ),
        'address'           => array(
            'type'            => 'varchar(255)',
            'label'           => '开票方地址',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 90,
        ),
        'telephone'         => array(
            'type'            => 'char(32)',
            'label'           => '开票方电话',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 90,
        ),
        'payee_operator'    => array(
            'type'            => 'varchar(50)',
            'label'           => '开票人',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 90,
        ),
        'payee_receiver'    => array(
            'type'            => 'varchar(50)',
            'label'           => '收款人',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 60,
        ),
        'payee_checker'     => array(
            'type'            => 'varchar(50)',
            'label'           => '复核人',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 60,
        ),
        'bank'              => array(
            'type'            => 'varchar(32)',
            'label'           => '开票方开户银行',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 120,
        ),
        'bank_no'           => array(
            'type'            => 'varchar(255)',
            'label'           => '开票方银行账号',
            'in_list'         => true,
            'default_in_list' => false,
            'width'           => 120,
        ),
       'return_logi_no' => array(
           'type'            => 'varchar(50)',
           'label'           => '回寄物流单号',
           'width'           => 110,
           'in_list'         => true,
           'default_in_list' => true,
       ),
       'return_logi_name' => array(
           'type'            => 'varchar(50)',
           'label'           => '回寄物流公司',
           'width'           => 110,
           'in_list'         => true,
           'default_in_list' => true,
       ),
        'last_modify' =>
        array (
          'type' => 'last_modify',
          'label' => '最后更新时间',
          'editable' => false,
          'filtertype' => 'yes',
          'filterdefault' => false,
          'in_list' => true,
          'default_in_list' => true,
        ),
        'itemsdf'             => array(
            'type' => 'longtext',
        ),
        'changesdf'     => array(
            'type'    => 'longtext',
            'label'   => '改票内容',
            'comment' => '改票内容',
        ),
        'change_status' => array(
            'type'    => array(
                0 => '未改票',
                1 => '已改票未审核',
                2 => '已改票已审核',
            ),
            'default' => '0',
            'label'   => '改票状态',
            'comment' => '改票状态',
        ),
        'source_status'      => array(
            'type'            => 'varchar(64)',
            'label'           => '平台状态',
            'editable'        => false,
            'in_list'         => false,
            'default_in_list' => false,
        ),
        'is_make_invoice'          => array(
            'type'    => array(
                0 => '不可操作',
                1 => '可操作',
                2=>'待红冲',
            ),
            'default' => '0',
            'label'   => '开票操作',
        ),
        'action_type' => array(
            'type' => array(
                'create_order'    => '创建开票',
                'order_aftersale' => '售后冲红',
                'content_update'  => '开票内容更新',
                'merge_order'     => '合票冲红',
            ),
            'default'  => 'create_order',
            'label'    => '开票操作',
        ),
        'cost_freight' => array(
            'type'            => 'money',
            'default'         => '0',
            'label'           => '配送费用',
            'width'           => 70,
            'editable'        => false,
            'filtertype'      => 'number',
            'in_list'         => true,
            'default_in_list' => true,
        ),
        'disabled' =>
        array (
          'type'    => 'bool',
          'default' => 'false',
          'label'   => '列表是否显示',  
          'editable'=> false,
        ),
        'memo'        => array(
            'type'     => 'longtext',
            'label'    => '发票备注',
            'editable' => false,
        ),
        'invoice_type' => array(
            'type'            => array(
                'normal' => '未合并开票',
                'merge'  => '合并开票',
            ),
            'default'         => 'normal',
            'label'           => '合票',
            'editable'        => false,
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
        ),
        'is_edit'     => array(
            'type'     => 'bool',
            'default'  => 'false',
            'editable' => false,
        ),
        'sync_msg' => [
            'type' => 'text',
            'label' => '发票同步信息',
            'in_list' => true,
            'default_in_list' => true,
            'width' => 200,
        ],
        'channel_id' => array(
            'type' => 'table:channel@invoice',
            'label' => '开票渠道',
            'in_list' => false,
            'default_in_list' => false,
            'width' => 70,
            'editable' => false,
            'order' => 10,
        ),
        'invoice_apply_bn' => array(
            'type' => 'varchar(32)',
            'required' => true,
            'label' => '开票申请单号',
            'is_title' => true,
            'default_in_list' => true,
            'in_list' => true,
            'filtertype' => 'normal',
            'filterdefault' => true,
            'searchtype' => 'nequal',
        ),
        'golden_tax_version' => array(
            'type' => array(
                0 => '金税三期',
                1 => '金税四期',
            ),
            'default' => '1',
            'required' => true,
            'label' => '金税系统版本',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 3,
            'width' => 70,
        ),
        'shop_sync_status'         => array(
            'type'            => array(
                0 => '未同步',
                1 => '同步中',
                2 => '同步成功',
                3 => '同步失败',
            ),
            'default'         => '0',
            'label'           => '店铺同步状态',
            'in_list'         => true,
            'default_in_list' => true,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'order'           => 5,
            'width'           => 70,
        ),
        'shop_sync_msg' => array(
            'type' => 'varchar(255)',
            'label' => '店铺同步信息',
            'is_title' => true,
            'default_in_list' => false,
            'in_list' => true,
        ),
        'town_code' => array(
            'type' => 'varchar(32)',
            'label' => '来源小镇',
            'in_list' => true,
            'default_in_list' => true,
            'width' => 200,
        ),
        'order_source'         => array(
            'type'            => array(
                0 => 'local',
                1 => 'api',
            ),
            'default'         => '0',
            'label'           => '订单来源',
            'in_list'         => true,
            'default_in_list' => false,
            'filtertype'      => 'normal',
            'filterdefault'   => true,
            'width'           => 70,
        ),
    ),
    'index' => array(
        'idx_status'        => array('columns' => array('is_status',),),
        'idx_mode'          => array('columns' => array('mode',),),
        'ind_create_time'   => array('columns' => array('create_time',),),
        'ind_delivery_id'   => array('columns' => array('delivery_id',),),
        'ind_last_modify'   => array('columns' => array('last_modify',),),
        'ind_sync'          => array('columns' => array('sync',),),
        'ind_change_status' => array('columns' => array('change_status',),),
        'ind_source_status' => array('columns' => array('source_status',),),
        'ind_is_make_invoice' => array('columns' => array('is_make_invoice',),),
        'ind_invoice_apply_bn' => array('columns' => array('invoice_apply_bn',),),
    ),
    'comment' => '订单发票表',
    'engine'  => 'innodb',
    'version' => '$Rev:  $',
);
