<?php
/**
 * 基础物料数据结构
 *
 * <AUTHOR>
 * @version 0.1
 */

$db['basic_material_history']=array(
  'columns' =>
  array(
    'history_bm_id' =>
    array(
      'type' => 'int unsigned',
      'required' => true,
      'pkey' => true,
      'extra' => 'auto_increment',
      'label' => 'ID',
      'width' => 110,
      'hidden' => true,
      'editable' => false,
      'comment' => '自增主键ID'
    ),
    'material_bn' => array(
        'type' => 'varchar(200)',
        'label' => '基础物料编码',
        'width' => 120,
        'editable' => false,
        'in_list' => true,
        'default_in_list' => true,
        'required' => true,
        'searchtype' => 'nequal',
        'filtertype' => 'textarea',
        'filterdefault' => true,
    ),
    'history_material_bn' =>
    array(
      'type' => 'varchar(200)',
      'required' => true,
      'label' => '历史物料编码',
      'is_title' => true,
      'default_in_list' => true,
      'width' => 260,
      'searchtype' => 'has',
      'editable' => false,
      'filtertype' => 'normal',
      'filterdefault' => true,
      'in_list' => true,
    ),
  ),
  'index' =>
  array(
    'uni_material_bn' =>
    array(
      'columns' =>
      array(
        0 => 'material_bn',
      ),
      'prefix' => 'UNIQUE',
    ),
    'ind_history_material_bn' =>
    array(
        'columns' =>
        array(
            0 => 'history_material_bn',
        ),
    ),
  ),
  'comment' => '基础物料历史表,用于存储SKU纬度的商品数据',
  'engine' => 'innodb',
  'version' => '$Rev:  $',
);
