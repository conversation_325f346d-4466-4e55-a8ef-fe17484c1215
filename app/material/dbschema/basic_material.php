<?php
/**
 * 基础物料数据结构
 *
 * <AUTHOR>
 * @version 0.1
 */

$db['basic_material']=array(
  'columns' =>
  array(
    'bm_id' =>
    array(
      'type' => 'int unsigned',
      'required' => true,
      'pkey' => true,
      'extra' => 'auto_increment',
      'label' => 'ID',
      'width' => 110,
      'hidden' => true,
      'editable' => false,
      'comment' => '自增主键ID'
    ),
    'material_bn' => array(
        'type' => 'varchar(200)',
        'label' => '基础物料编码',
        'width' => 120,
        'editable' => false,
        'in_list' => true,
        'default_in_list' => true,
        'required' => true,
        'searchtype' => 'nequal',
        'filtertype' => 'textarea',
        'filterdefault' => true,
    ),
    'material_name' =>
    array(
      'type' => 'varchar(200)',
      'required' => true,
      'label' => '基础物料名称',
      'is_title' => true,
      'default_in_list' => true,
      'width' => 260,
      'searchtype' => 'has',
      'editable' => false,
      'filtertype' => 'normal',
      'filterdefault' => true,
      'in_list' => true,
    ),
    'material_spu' =>
    array(
        'type' => 'varchar(200)',
        'label' => '基础物料款号',
        'width' => 120,
        'editable' => false,
        'in_list' => true,
        'searchtype' => 'nequal',
        'filtertype' => 'normal',
        'filterdefault' => true,
        'comment' => '基础物料SPU'
    ),
    'material_bn_crc32' =>
    array(
      'type' => 'bigint(13)',
      'label' => '基础物料编码整型索引值',
      'editable' => false,
      'required'        => true,
    ),
    'type' =>
    array(
      'type' => 'tinyint(1)',
      'label' => '物料属性',
      'width' => 100,
      'editable' => false,
      'default' => 1,
      'in_list' => true,
      'default_in_list' => true,
      'required' => true,
      'comment' => '物料属性,可选值:1(成品),2(半成品),3(普通),4(礼盒),5(虚拟)',
    ),
    'cat_id' =>
    array(
      'type' => 'table:basic_material_cat@material',
      'required' => false,
      'default' => 0,
      'label' => '分类',
      'width' => 75,
      'editable' => true,
      'filtertype' => 'yes',
      'filterdefault' => true,
      'in_list' => true,
      'default_in_list' => true,
      'panel_id' => 'basic_material_finder_top',
      'comment' => '分类ID,关联material_basic_material_cat.cat_id'
    ),
    'cat_path' =>
    array(
      'type' => 'varchar(100)',
      'default' => '',
      'label' => '分类路径',
      'width' => 110,
      'editable' => false,
      'in_list' => true,
      'comment' => '分类路径(从根至本结点的路径逗号分隔)',
    ),
    'serial_number' =>
    array(
      'type' => 'bool',
      'default' => 'false',
      'required' => true,
      'editable' => false,
      'label' => 'SN码',
      'width' => 75,
      'filtertype' => 'normal',
      'filterdefault' => true,
      'in_list' => true,
      'default_in_list' => false,
    ),
    'visibled' =>
    array(
      'type' => 'tinyint(1)',
      'label' => '销售状态',
      'width' => 100,
      'in_list' => true,
      'default_in_list' => true,
      'editable' => false,
      'default' => 1,
      'required' => true,
      'comment' => '销售状态,可选值:0(否),1(是)'
    ),
    'create_time' => array(
      'type' => 'time',
      'label' => '创建时间',
      'in_list' => true,
      'default_in_list' => true,
      'default' => 0,
    ),
    'tax_rate' =>
    array(
          'type' => 'tinyint(2)',
          'label' => '开票税率',
          'width' => 120,
          'in_list' => true,
          'default_in_list' => false,
    ),
    'tax_name' =>
    array(
          'type' => 'varchar(200)',
          'label' => '开票名称',
          'default' => '',
          'width' => 120,
          'searchtype' => 'head',
          'editable' => false,
          'filtertype' => 'yes',
          'filterdefault' => true,
          'in_list' => true,
          'default_in_list' => false,
    ),
    'tax_code' =>
    array(
          'type' => 'varchar(200)',
          'label' => '开票分类编码',
          'default' => '',
          'width' => 120,
          'searchtype' => 'head',
          'editable' => false,
          'filtertype' => 'yes',
          'filterdefault' => true,
          'in_list' => true,
          'default_in_list' => false,
    ),
    'disabled' =>
    array(
      'type' => 'bool',
      'default' => 'false',
      'required' => true,
      'editable' => false,
      'label' => '删除状态',
      'comment' => '删除状态,可选值:true(是), false(否)',
    ),
    'omnichannel' =>
    array(
      'type' => 'tinyint(1)',
      'label' => '是否全渠道',
      'in_list' => true,
      'editable' => false,
      'default' => 2,
      'comment' => '0(否),1(是)',
    ),
    'last_modified' => array(
        'label' => '最后更新时间',
        'type' => 'last_modify',
        'width' => 130,
        'in_list' => true,
        'default_in_list' => true,
    ),
    'source' => array(
        'type'     => 'varchar(50)',
        'required' => true,
        'label'    => '数据来源',
        'default'  => 'local',
        'comment' => '数据来源,可选值:local(本地),api(接口),hub(hub接口)',
    ),
      'store_id'      => array(
          'type'     => 'table:store@o2o',
          'width'    => 110,
          'editable' => false,
          'default'  => 0,
          'label'    => '所属门店',
          'in_list' => true,
          'default_in_list' => true,
          'filtertype'    => 'normal',
          'filterdefault' => true,
      ),
      'material_spu_id'=> array(
          'type' => 'varchar(200)',
          'label' => '商品大码',
          'width' => 120,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => true,
          'searchtype' => 'nequal',
          'filtertype' => 'textarea',
          'filterdefault' => true,
      ),
      'brand_sku_code' => array(
          'type' => 'varchar(200)',
          'label' => '品牌SKU编码',
          'width' => 120,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => true,
          'searchtype' => 'nequal',
          'filtertype' => 'textarea',
          'filterdefault' => true,
      ),
      'supply_model' => array(
          'type' => "varchar(200)",
          'label' => '发货模式',
          'width' => 120,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => false,
          'filtertype' => 'textarea',
          'filterdefault' => true,
      ),
      'busness_material_bn' => array(
          'type' => 'varchar(200)',
          'label' => '商户货号',
          'width' => 120,
          'editable' => false,
          'in_list' => true,
          'default_in_list' => true,
          'searchtype' => 'nequal',
          'filtertype' => 'textarea',
          'filterdefault' => true,
      ),
      'is_history'       => array(
        'type'    => array(
            0 => '普通商品',
            1 => '历史商品',
        ),
        'default' => '0',
        'label'   => '是否历史商品',
        'filtertype'      => 'normal',
        'filterdefault'   => true,
        'in_list'         => true,
        'default_in_list' => true,
        'comment' => '是否历史商品,可选值:0(普通商品),1(历史商品)',
    ), 
  ),
  'index' =>
  array(
    'uni_material_bn' =>
    array(
      'columns' =>
      array(
        0 => 'material_bn',
      ),
      'prefix' => 'UNIQUE',
    ),
    'ind_material_spu' =>
    array(
        'columns' =>
        array(
            0 => 'material_spu',
        ),
    ),
    'ind_last_modified' =>
    array(
        'columns' =>
        array(
            0 => 'last_modified',
        ),
    ),
      'ind_store_id' => array('columns' => array(0 => 'store_id')),
  ),
  'comment' => '基础物料表,用于存储SKU纬度的商品数据',
  'engine' => 'innodb',
  'version' => '$Rev:  $',
);
