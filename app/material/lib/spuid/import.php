<?php

/**
 * 导入大码开票信息
 * Class material_spuid_import
 */
class material_spuid_import
{
    const IMPORT_TITLE = [
        '开票税率'   => 'tax_rate',
        '大码'   => 'material_spu_id',
        '发票分类编码' => 'tax_code',
        '开票名称' => 'tax_name',
    ];

    public function getExcelTitle()
    {
        return ['大码导入发票信息模板.xlsx', [array_keys(self::IMPORT_TITLE)]];
    }

    /**
     * undocumented function
     *
     * @return void
     * <AUTHOR>
    public function processExcelRow($import_file, $post)
    {
        // 读取文件
        return kernel::single('omecsv_phpoffice')->import($import_file, function ($line, $buffer, $post, $highestRow) {
            static $title;

            if ($line == 1) {
                $title = $buffer;

                // 验证模板是否正确
                if (array_filter($title) != array_keys(self::IMPORT_TITLE)) {

                    return [false, '导入模板不正确'];
                }

                return [true];
            }
            if(count($buffer) < count(self::IMPORT_TITLE)) {
                return [true, '导入列不够', 'warnning'];
            }
            $buffer = array_combine(array_values(self::IMPORT_TITLE), array_slice($buffer, 0, count(self::IMPORT_TITLE)));


            $push_params = array(
                'data' => array(
                    'task_type' => 'spuidimport',
                    'buffer' => json_encode($buffer),
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service'),
            );
            kernel::single('taskmgr_interface_connecter')->push($push_params);


            return [true];
        }, []);
    }
}
