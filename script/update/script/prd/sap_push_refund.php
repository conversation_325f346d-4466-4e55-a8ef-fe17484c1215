<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$refund_apply_bns = [
    '2000001989842675',
    '2000001990403696',
    '2000002005678188',
    '2000002006307306',
    '2000002039200083'
];

$applyMdl = app::get('ome')->model('refund_apply');
$applyList = $applyMdl->getList('apply_id,refund_apply_bn,shop_type', array('refund_apply_bn' => $refund_apply_bns));
foreach ($applyList as $order) {
    $sdf = kernel::single('ome_sap_data_platform_' . $order['shop_type'])->get_refund_sdf($order['apply_id']);
    $result = kernel::single('erpapi_router_request')->set('sap', true)->refund_push($sdf);
    if ($result['rsp'] == 'fail') {
        echo '退款单号：' . $order['refund_apply_bn'] . ',推送sap失败：' . $result['msg'] . PHP_EOL;
        continue;
    }

    echo '退款单号：' . $order['refund_apply_bn'] . ',推送成功' . PHP_EOL;
}

echo 'end...' . PHP_EOL;

//$order_id = 13109;//; 5165
//$orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $order_id), 'shop_type');
//$sdf = kernel::single('ome_sap_data_platform_' . $orderInfo['shop_type'])->get_order_sdf($order_id);
//print_r($sdf['payments']);
//$result = kernel::single('erpapi_router_request')->set('sap', true)->order_push($sdf);
//print_r($result);


//$delivery_id = 210;
//$sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_delivery_sdf($delivery_id);
//print_r($sdf);
//$result = kernel::single('erpapi_router_request')->set('sap', true)->delivery_push($sdf);
//print_r($result);


//$apply_id = 8195;//; 516
//$sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_refund_sdf($apply_id);
//print_r($sdf);
//$result = kernel::single('erpapi_router_request')->set('sap', true)->refund_push($sdf);
//print_r($result);

//$reship_id = 61;
//$result = kernel::single('ome_sap_sap')->push_reship($reship_id, 'luban');
//var_dump($result);

//$sdf = kernel::single('ome_sap_data_platform_luban')->get_reship_sdf($reship_id);
////print_r($sdf);
//$result = kernel::single('erpapi_router_request')->set('sap', true)->reship_push($sdf);
//print_r($result);

# 8184051089473542
//$order_id = 10178;//; 5165
//$orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $order_id), '*');
//$ext_data = [
//    'order_id' => $order_id,
//    'order_bn' => $orderInfo['order_bn'],
//];
//$params = ['143121405'];
//$result = kernel::single('erpapi_router_request')->set('shop', $orderInfo['shop_id'])->coupon_couponsInfo($params, $ext_data);
//var_dump($result);

//$a = 'a:1:{i:0;a:10:{s:10:"product_id";s:2:"36";s:2:"bn";s:11:"VN0A5HZY6BT";s:4:"name";s:11:"VN0A5HZY6BT";s:3:"num";i:1;s:5:"price";s:6:"335.67";s:13:"order_item_id";s:3:"218";s:3:"oid";s:19:"6936103725981439873";s:12:"refund_phase";N;s:11:"refund_memo";N;s:8:"modified";i:1736515034;}}';
//$s = unserialize($a);
//var_dump($s);
//
//$s[0]['product_id'] = '201';
//$s[0]['bn'] = '80d15f3bc9bca947';
//$s[0]['name'] = 'Kate spade 经典百搭水桶包 KF863 KF862';
//$s[0]['num']  = 1;
//$s[0]['price']  = '850';
//$s[0]['order_item_id']  = '395';
//$s[0]['oid']  = '2907615877';
//$s[0]['refund_phase']  = '';
//$s[0]['refund_memo']  = '';
//$s[0]['modified']  = '1736515034';
//
//$s = serialize($s);
//var_dump($s);