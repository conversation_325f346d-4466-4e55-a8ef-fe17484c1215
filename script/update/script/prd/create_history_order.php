<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

define('BASE_URL', 'https://oms.fvo2o.com/');

// 获取视频号的所有历史订单
$shop_id = '09949bc39e0345b10294a478d786acc9';

// 导入的字段
// 订单号,子订单号,子订单ID,商品ID,SKU ID,所属小镇,商家编码,商品货号,商品大码,商品数量,退单数量,商品单价,订单金额,推送金蝶金额,发货状态,支付状态,订单状态,发货物流公司,发货物流单号,发货物流公司名称,发货时间,商品名称,商品颜色,商品规格,商品图片地址

// 获取csv文件
$file = 'data.csv';
$list = array_map('str_getcsv', file($file));

// 按照订单号组织数据
$orderData = [];
$payStatusMap = [];
$orderDataItems = [];
foreach ($list as $key => $row) {
    // 跳过标题行
    if ($key === 0) {
        continue;
    }

    $orderBn = $row[0]; // 订单号
    $subOrderBn = $row[1]; // 子订单号
    $subOrderId = $row[2]; // 子订单ID
    $goodsId = $row[3]; // 商品ID
    $skuId = $row[4]; // SKU ID
    $town = $row[5]; // 所属小镇
    $merchantCode = $row[6]; // 商家编码
    $itemNo = $row[7]; // 商家编码
    $largeCode = $row[8]; // 商品大码
    $goodsNum = $row[9]; // 商品数量
    $refundNum = $row[10]; // 退单数量
    $goodsPrice = $row[11]; // 商品单价
    $orderAmount = $row[12]; // 订单金额
    $kdAmount = $row[13]; // 推送金蝶金额
    $shipStatus = $row[14]; // 发货状态
    $payStatus = $row[15]; // 支付状态
    $orderStatus = $row[16]; // 订单状态
    $logiCompany = $row[17]; // 发货物流公司
    $logiNo = $row[18]; // 发货物流单号
    $logiCompanyName = $row[19]; // 发货物流公司名称
    $shipTime = $row[20]; // 发货时间
    $goodsName = $row[21]; // 商品名称
    $goodsColor = $row[22]; // 商品颜色
    $goodsSize = $row[23]; // 商品规格
    $goodsImage = $row[24]; // 商品图片地址

    if ($shipStatus == '未发货' && $payStatus == '已退款') {
        #continue;
    }

    if ($payStatus == '退款中') {
        $payStatusMap[$orderBn] = '6';
    }

    if ($logiCompany == 'DBKD' || $logiCompany == 'DBLKY' || $logiCompany == 'DHL') {
        $logiCompany = 'DBL';
    }

    if ($logiCompany == 'YD') {
        $logiCompany = 'YUNDA';
    }

    if ($logiCompany == 'JTSD') {
        $logiCompany = 'jitu';
    }

    $item = [
        'sku_id' => $skuId,
        'num' => $goodsNum,
        'history_oid' => $subOrderBn,
        'history_obj_id' => $subOrderId,
        'logi_no' => $logiNo,
        'pay_status' => $payStatus,
        'ship_status' => $shipStatus,
        'logi_company' => $logiCompany,
        'customerStoreCode' => $merchantCode, // 门店编码
        'itemNo' => $itemNo,
        'largeSize' => $largeCode,
        'color' => $goodsColor,
        'size' => $goodsSize,
        'image' => $goodsImage,
        'shipTime' => strtotime($shipTime),
        'kdAmount' => $kdAmount
    ];

    if (!isset($orderData[$orderBn])) {
        $orderData[$orderBn] = [];
    }

    $corpInfo = app::get('ome')->model('dly_corp')->dump(array('type' => $logiCompany), '*');
    $logiId = $corpInfo['corp_id'];
    $logiCode = $corpInfo['code'];
    if (!isset($orderData[$orderBn][$logiNo]) && $logiNo) {
        $orderData[$orderBn][$logiNo] = [
            'logi_no' => $logiNo,
            'logi_company' => $logiCompanyName,
            'logi_id' => $logiId,
            'logi_code' => $logiCompany,
            'shipTime' => strtotime($shipTime),
            'items' => []
        ];
    }

    if ($item) {
        if ($logiNo) {
            $orderData[$orderBn][$logiNo]['items'][$skuId] = $item;
        }
        $orderDataItems[$orderBn][$skuId] = $item;
    }
}

// 处理每个订单
$historyOrder = kernel::single('ome_order_history');
foreach ($orderData as $orderBn => $deliveryList) {
    echo $orderBn."\n";
    $push_params = array(
        'data' => array(
            'task_type' => 'wxshipinhistory',
            'method' => 'create',
            'shop_id' => $shop_id,
            'order_bn' => $orderBn,
            'deliveryList' => json_encode($deliveryList),
            'orderDataItems' => json_encode($orderDataItems[$orderBn]),
            'is_refund' => isset($payStatusMap[$orderBn]) ? '1' : '0',
        ),
        'url' => kernel::openapi_url('openapi.autotask', 'service'),
    );
    kernel::single('taskmgr_interface_connecter')->push($push_params);
}
