<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bns = [
    '3729166470575697920',
    '3729166490577807360',
    '3729423130836474368',
    '3729253268749432320',
    '3729245605369093888'
];

$orderMdl = app::get('ome')->model('orders');
$orderList = $orderMdl->getList('order_id,order_bn,shop_type', array('order_bn' => $order_bns));

$extend = [
    'filter_delete_item' => false
];

foreach ($orderList as $order) {
    $extend = [
        'filter_delete_item' => false
    ];
    $sdf = kernel::single('ome_sap_data_platform_' . erpapi_sap_func::getShopType($order['shop_type']))->get_order_sdf($order['order_id'], $extend);
    $result = kernel::single('erpapi_router_request')->set('sap', true)->order_push($sdf);
    if ($result['rsp'] == 'fail') {
        echo '订单号：' . $order['order_bn'] . ',推送sap失败：' . $result['msg'] . PHP_EOL;
        continue;
    }

    echo '订单号：' . $order['order_bn'] . ',推送成功' . PHP_EOL;
}

echo 'end...' . PHP_EOL;
