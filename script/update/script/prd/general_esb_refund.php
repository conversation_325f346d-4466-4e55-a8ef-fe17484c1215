<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bns = [
    '4929658000520816',
    '4929658000600816',
    '4929674000216051',
    '4929674000236051',
    '4929707000170816',
];
$orderList = app::get('ome')->model('orders')->getList('order_id,order_bn', array('order_bn' => $order_bns));
if (empty($orderList)) {
    echo '订单不存在' . PHP_EOL;
    die;
}

$sapOrdersMdl = app::get('sales')->model('sap_orders');
$sapOrderItemsMdl = app::get('sales')->model('sap_order_items');
$sapGoodsItemsMdl = app::get('sales')->model('sap_good_items');
$sapPaymentsMdl = app::get('sales')->model('sap_payments');
$sapCouponsMdl = app::get('sales')->model('sap_coupons');
$applyMdl = app::get('ome')->model('refund_apply');
$db = kernel::database();

foreach ($orderList as $order) {
    $ord_filter = [
        'bill_type' => 'payed',
        'bill_id' => $order['order_id'],
    ];
    $sapOrders = $sapOrdersMdl->dump($ord_filter, '*');
    if (empty($sapOrders)) {
        echo '订单号：' . $order['order_bn'] . '，不存在支付单esb数据' . PHP_EOL;
        continue;
    }

    # 获取退款申请单
    $applyList = $applyMdl->getList('*', ['order_id' => $order['order_id'], 'status' => '4']);
    if (empty($applyList)) {
        echo '订单号：' . $order['order_bn'] . '，不存在已完成的退款单' . PHP_EOL;
        continue;
    }

    try {
        $db->beginTransaction();

        $current_sap_id = $sapOrders['sap_id'];
        unset($sapOrders['sap_id']);
        $sapOrders['sync_status'] = 'normal';
        $sapOrders['sync_time'] = null;
        $sapOrders['create_time'] = time();
        $sapOrders['bill_type'] = 'refund';

        foreach ($applyList as $apply) {
            $sapOrders['bill_id'] = $apply['apply_id'];
            $sapOrders['bill_no'] = $apply['refund_apply_bn'];
            # 插入新的数据
            $ordersResult = $sapOrdersMdl->save($sapOrders);
            if (!$ordersResult || empty($sapOrders['sap_id'])) {
                throw new Exception('订单号：' . $order['order_bn'] . '，生成esb支付单数据失败：' . $sapOrdersMdl->db->errorinfo());
            }

            $oidList = [];

            $filter = [
                'sap_id' => $current_sap_id
            ];
            # 读取订单明细
            $sapOrderItems = $sapOrderItemsMdl->getList('*', $filter);
            if (!empty($sapOrderItems)) {
                foreach ($sapOrderItems as $k => $item) {
                    # 序号
                    $indexNo = str_pad($k + 1, 3, '0', STR_PAD_LEFT);
                    $oidList[$item['eshopOrderSn']] = $apply['refund_apply_bn'] . $indexNo;

                    $orderHead = [
                        'sap_id' => $sapOrders['sap_id'],
                        'order_id' => $order['order_id'],
                        'orderSource' => $item['orderSource'],
                        'orderType' => 'REFUNDED', // 固定值
                        'orderSn' => $apply['refund_apply_bn'],  // 订单编号
                        'mallId' => $item['mallId'], // 门店Id
                        'shopId' => $item['shopId'], // 商户id
                        'spuId' => $item['spuId'], // 商品大码
                        'vipId' => $item['vipId'], // 会员卡号
                        'orderTime' => $item['orderTime'], // 订单下单/退单时间 长度：13位
                        'orderPrice' => floatval($item['orderPrice']), // 订单总金额
                        'discAmount' => floatval($item['discAmount']), // 订单优惠金额
                        'transAmount' => floatval($item['transAmount']), // 运费
                        'bankAmount' => floatval($item['bankAmount']), // 银行手续费
                        'oriFlowNo' => $item['eshopOrderSn'], // 流水号，如果是退款单，则传订单号
                        'eshopOrderSn' => $apply['refund_apply_bn'] . $indexNo,
                        'payAmount' => floatval($item['payAmount']),
                    ];
                    $result = $sapOrderItemsMdl->insert($orderHead);
                    if (!$result) {
                        throw new Exception('订单号：' . $order['order_bn'] . '，保存订单明细失败：' . $sapOrderItemsMdl->db->errorinfo());
                    }
                }
            }

            # sku明细
            $sap_goods_items = $sapGoodsItemsMdl->getList('*', $filter);
            if (!empty($sap_goods_items)) {
                foreach ($sap_goods_items as $k => $item) {
                    # 订单明细
                    $orderItem = [
                        'sap_id' => $sapOrders['sap_id'],
                        'order_id' => $order['order_id'],
                        'eshopOrderSn' => $oidList[$item['eshopOrderSn']], // 在线商城业务订单编号
                        'spuId' => $item['spuId'], // 商品大码
                        'sku' => $item['sku'], // 商品SKU信息
                        'count' => intval($item['count']), // 订单购买商品数量
                        'unitPrice' => floatval($item['unitPrice']), // 商品单价
                        'mallId' => $item['mallId'], // 门店Id
                        'totalPrice' => floatval($item['totalPrice']),
                    ];
                    $sapGoodsItemsMdl->save($orderItem);
                }
            }

            # 优惠列表
            $sap_payments = $sapPaymentsMdl->getList('*', $filter);
            if (!empty($sap_payments)) {
                foreach ($sap_payments as $item) {
                    $payment = [
                        'sap_id' => $sapOrders['sap_id'],
                        'order_id' => $order['order_id'],
                        'eshopOrderSn' => $oidList[$item['eshopOrderSn']], // 业务订单编号
                        'paymentType' => $item['paymentType'], // 金蝶支付方式名称(DP支付名称)
                        'paymentCode' => $item['paymentCode'], // 金蝶支付方式编码，包含平台补贴(金蝶支付编码)
                        'originPaymentType' => $item['originPaymentType'], // 原始支付方式名称
                        'originPaymentCode' => $item['originPaymentCode'], // 原始支付方式编码
                        'amount' => floatval($item['amount']), // 支付金额
                        'subsidyAmount' => floatval($item['subsidyAmount']), // 平台补贴金额
                    ];
                    # 券号
                    if (!empty($item['couponCode'])) {
                        $payment['couponCode'] = $item['couponCode'];
                    }
                    $sapPaymentsMdl->save($payment);
                }
            }

            # 优惠券列表
            $sap_coupons = $sapCouponsMdl->getList('*', $filter);
            if (!empty($sap_coupons)) {
                foreach ($sap_coupons as $item) {
                    $coupon = [
                        'sap_id' => $sapOrders['sap_id'],
                        'order_id' => $order['order_id'],
                        'eshopOrderSn' => $oidList[$item['eshopOrderSn']],
                        'couponCode' => $item['couponCode'],  // 券ID，非用户优惠券ID
                        'couponName' => $item['couponName'],
                        'couponAmount' => $item['couponAmount'],
                        'couponType' => $item['couponType']
                    ];
                    $sapCouponsMdl->save($coupon);
                }
            }
        }


        $db->commit();
    } catch (Exception $e) {
        echo '订单号：' . $order['order_bn'] . ',处理失败：' . $e->getMessage() . PHP_EOL;
        $db->rollback();
        continue;
    }


    echo '订单号：' . $order['order_bn'] . ',处理成功' . PHP_EOL;
}

echo 'end...' . PHP_EOL;
