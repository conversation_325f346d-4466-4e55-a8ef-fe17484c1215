<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bns = [
    '4936382000299087'
];

$orderMdl = app::get('ome')->model('orders');
$shopexMdl = app::get('ome')->model('order_coupon_ecshopx');
$objectsCouponMdl = app::get('ome')->model('order_objects_coupon');
$shopexObj = kernel::single('erpapi_shop_response_plugins_order_couponecshopx');

foreach ($order_bns as $order_bn) {
    $orderInfo = $orderMdl->dump(array('order_bn' => $order_bn), 'order_id,createtime,shop_id,shop_type');
    if (empty($orderInfo)) {
        echo '订单号：' . $order_bn . ',对应的订单信息不存在' . PHP_EOL;
        continue;
    }

    $orderRsp = kernel::single('erpapi_router_request')->set('shop', $orderInfo['shop_id'])->order_get_order_detial($order_bn);
    if ($orderRsp['rsp'] != 'succ') {
        echo '订单号：' . $order_bn . ',拉取订单失败：' . $orderRsp['msg'] . PHP_EOL;
        continue;
    }

    if (empty($orderRsp['data'])) {
        echo '订单号：' . $order_bn . ',拉取的订单信息为空，不处理' . PHP_EOL;
        continue;
    }

    $aData = $orderRsp['data']['trade'];

    $order_sdf = [];
    $order_sdf['shop_type'] = $orderInfo['shop_type'];
    //订单商品结构数组信息
    $order_objects = array();
    foreach ($aData['orders']['order'] as $o_k => $o_v) {
        $order_objects[$o_k]['is_sh_ship'] = isset($o_v['is_sh_ship']) ? $o_v['is_sh_ship'] : '';
        $order_objects[$o_k]['obj_type'] = $o_v['type'];
        $order_objects[$o_k]['shop_goods_id'] = $o_v['iid'];
        $order_objects[$o_k]['oid'] = $o_v['oid'];
        $order_objects[$o_k]['obj_alias'] = $o_v['type_alias'];
        $order_objects[$o_k]['bn'] = $o_v['orders_bn'];
        $order_objects[$o_k]['name'] = $o_v['title']; //子订单名称
        $order_objects[$o_k]['price'] = $o_v['total_order_fee'] / $o_v['items_num']; //原始单价
        $order_objects[$o_k]['amount'] = $o_v['total_order_fee']; //原始价小计
        $order_objects[$o_k]['sale_price'] = $o_v['sale_price'];
        $order_objects[$o_k]['quantity'] = $o_v['items_num'];
        $order_objects[$o_k]['weight'] = $o_v['weight'];
        $order_objects[$o_k]['score'] = 0;//积分
        $order_objects[$o_k]['is_oversold'] = $o_v['is_oversold'];//淘宝超卖标记
        $order_objects[$o_k]['fx_oid'] = $o_v['fx_oid'];
        $order_objects[$o_k]['tc_order_id'] = $o_v['tc_order_id'];
        $order_objects[$o_k]['cost_tax'] = $o_v['cost_tax'];
        $order_objects[$o_k]['buyer_payment'] = $o_v['buyer_payment'];
        $order_objects[$o_k]['store_code'] = $o_v['store_code'];
        $order_objects[$o_k]['gift_mids'] = $o_v['gift_mids'];
        $order_items = array();
        $total_pmt_price = 0;

        foreach ($o_v['order_items']['orderitem'] as $i_k => $i_v) {
            if ($order_sdf['shop_type'] == 'alibaba') {
                $order_items[$i_k]['specId'] = $i_v['iid'];
            }
            $order_items[$i_k]['item_type'] = $i_v['item_type'];
            $order_items[$i_k]['shop_goods_id'] = $i_v['iid'];
            $order_items[$i_k]['shop_product_id'] = $i_v['sku_id'];
            $order_items[$i_k]['bn'] = $i_v['bn'];
            $order_items[$i_k]['name'] = $i_v['name'];
            $product_attr = array();
            #微信小店前端没有规格下来
            if (($order_sdf['shop_type'] != 'wx') && (!empty($i_v['sku_properties'])) && is_string($i_v['sku_properties'])) {
                $sku_properties = explode(';', $i_v['sku_properties']);
                foreach ($sku_properties as $si => $sp) {
                    $_sp = explode(':', $sp);
                    $product_attr[$si]['label'] = $_sp[0];
                    $product_attr[$si]['value'] = $_sp[1];
                }
            }
            if (!empty($product_attr) && $order_sdf['shop_type'] == 'youzan') {
                $order_items[$i_k]['original_str'] = $i_v['sku_properties'];
            }

            if (empty($product_attr) && $i_v['sku_properties'] && is_array($i_v['sku_properties'])) {
                $product_attr = $i_v['sku_properties'];
                $order_items[$i_k]['original_str'] = $i_v['sku_properties_string'];
            }

            $order_items[$i_k]['product_attr'] = $product_attr;
            $order_items[$i_k]['quantity'] = $i_v['num'];
            $order_items[$i_k]['price'] = $i_v['price'];
            $order_items[$i_k]['amount'] = $i_v['total_item_fee'];
            $order_items[$i_k]['pmt_price'] = $i_v['discount_fee'];
            $order_items[$i_k]['sale_price'] = $i_v['sale_price'];
            $order_items[$i_k]['weight'] = $i_v['weight'];
            $order_items[$i_k]['score'] = $i_v['score'];
            $order_items[$i_k]['status'] = $i_v['status'];
            $order_items[$i_k]['promotion_id'] = $i_v['promotion_id'] ?? '';
            $order_items[$i_k]['fx_oid'] = $i_v['fx_oid'];
            $order_items[$i_k]['cost_tax'] = $i_v['cost_tax'];
            $order_items[$i_k]['buyer_payment'] = $i_v['buyer_payment'];
            $order_items[$i_k]['divide_order_fee'] = $i_v['divide_order_fee'];
            $order_items[$i_k]['part_mjz_discount'] = $i_v['part_mjz_discount'];
            $order_items[$i_k]['extend_item_list'] = $i_v['extend_item_list'];
            $order_items[$i_k]['expand_card_basic_price_used_suborder'] = $i_v['expand_card_basic_price_used_suborder'];
            $order_items[$i_k]['expand_card_expand_price_used_suborder'] = $i_v['expand_card_expand_price_used_suborder'];
            $total_pmt_price += $i_v['discount_fee'];
        }
        $order_objects[$o_k]['order_items'] = $order_items;
        $order_objects[$o_k]['pmt_price'] = $o_v['discount_fee'] - $total_pmt_price;
    }
    $order_sdf['order_objects'] = $order_objects;

    // 矩阵返回优惠数据不请求接口，否则请求接口
    $ext_data = array();
    $ext_data['shop_id'] = $orderInfo['shop_id'];
    $ext_data['shop_type'] = $orderInfo['shop_type'];
    $ext_data['createtime'] = $orderInfo['createtime'];
    $ext_data['order_bn'] = $order_bn;
    $ext_data['coupon_source'] = 'sync';
    // 优惠明细数据format
    $result = kernel::single('ome_order_coupon')->couponDataFormat($order_sdf['order_objects'], $ext_data, $ext_data['shop_type']);

    $order_sdf['promotion_detail'] = $result['coupon_data'];
    $order_sdf['objects_coupon_data'] = $result['objects_coupon_data'];

    # 先删除数据
    $filter = [
        'order_id' => $orderInfo['order_id'],
    ];
    $shopexMdl->delete($filter);
    $objectsCouponMdl->delete($filter);

    # 保存优惠信息
    $shopexObj->postCreate($orderInfo['order_id'], $order_sdf);
}

echo 'end..' . PHP_EOL;
