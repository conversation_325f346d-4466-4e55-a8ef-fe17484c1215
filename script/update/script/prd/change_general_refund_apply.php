<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bns = [
    '147014798601035521'
];

$orderMdl = app::get('ome')->model('orders');
$refundsMdl = app::get('ome')->model('refunds');
$is_update_order = true;//是否更新订单付款状态
$db = kernel::database();

foreach ($order_bns as $order_bn) {
    $orderInfo = $orderMdl->dump(array('order_bn' => $order_bn), "*", array("order_objects" => array("*", array("order_items" => array("*")))));
    if (empty($orderInfo)) {
        echo '订单号：' . $order_bn . '，不存在订单' . PHP_EOL;
        continue;
    }

    $product_list = $bns = $oids = [];
    foreach ($orderInfo['order_objects'] as $object) {
        $bns[] = $object['bn'];
        $oids[] = $object['oid'];

        foreach ($object['order_items'] as $item) {
            $product_list[] = array(
                'product_id' => $item['product_id'],
                'bn' => $item['bn'],
                'name' => !empty($item['title']) ? $item['title'] : $item['name'],
                'num' => $item['quantity'],
                'price' => $item['price'],
                'order_item_id' => $item['item_id'],
                'oid' => $object['oid'],
                'modified' => time(),
            );
        }
    }

    try {
        $db->beginTransaction();
        $insertData = array(
            'order_id' => $orderInfo['order_id'],
            'refund_apply_bn' => $orderInfo['order_bn'],
            'pay_type' => 'online',
            'money' => $orderInfo['total_amount'],
            'refunded' => $orderInfo['total_amount'],
            'refund_money' => $orderInfo['total_amount'],
            'memo' => '强制生成的退款单',
            'create_time' => time(),
            'status' => '4',
            'shop_id' => $orderInfo['shop_id'],
            'source' => 'local',
            'shop_type' => $orderInfo['shop_type'],
            'refund_refer' => '0', //退款来源
            'org_id' => $orderInfo['org_id'],
            'bn' => implode(',', $bns),
            'oid' => implode(',', $oids), //oid子单
            'tag_type' => '0', //退款类型
        );
        if (!empty($product_list)) {
            $insertData['product_data'] = serialize($product_list);
        }

        //根据商品编码获取门店信息
        $bn = current($product_list)['bn'];
        $store = kernel::single("o2o_store_material")->getBmStore($bn);
        if ($store) {
            $belong_type = $store['performance_type'] == 'store' ? 'store' : 'town';
            $store_id = $store['store_id'];
        } else {
            $belong_type = 'customer';
            $store_id = 0;
        }
        $insertData['belong_store_id'] = $store_id;
        $insertData['belong_type'] = $belong_type;
        $insertData['check_payed'] = false;

        # 创建售后申请单
        $rs = kernel::single('ome_refund_apply')->createRefundApply($insertData, $is_update_order, $error_msg);
        if (!$rs) {
            throw new Exception($error_msg);
        }

        # 生成退款单
        $data = array(
            'refund_bn' => $orderInfo['order_bn'],
            'shop_id' => $orderInfo['shop_id'],
            'order_id' => $orderInfo['order_id'],
            'currency' => 'CNY',
            'money' => $orderInfo['total_amount'],
            'cur_money' => $orderInfo['total_amount'],
            'pay_type' => 'online',
            'download_time' => time(),
            'status' => 'succ',
            'memo' => '强制退款',
            'modifiey' => time(),
            't_ready' => time(),
            't_sent' => time(),

        );
        $rs = $refundsMdl->insert($data);
        if (!$rs) {
            throw new Exception('退款单生成失败：' . $refundsMdl->db->errorinfo());
        }

        # 更新订单状态
        kernel::single('ome_order_func')->update_order_pay_status($orderInfo['order_id']);

        $db->commit();
    } catch (Exception $e) {
        $db->rollBack();
        $error_msg = $e->getMessage();
        echo '订单号：' . $order_bn . '，报错信息：' . $error_msg . PHP_EOL;
    }
}