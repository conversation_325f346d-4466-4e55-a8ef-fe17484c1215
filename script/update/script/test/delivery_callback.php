<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$delivery_bn = '25072400000031';
$logi_no = 'SF3121070797'.mt_rand(100, 999);

$s = '{
  "app_id": "ecos.ome",
  "date": "' . date('Y-m-d H:i:s') . '",
  "delivery_bn": "' . $delivery_bn . '",
  "extendProps": "{}",
  "item": "[{\"product_bn\": \"100025-BKW8\", \"num\": \"1\"}]",
  "logi_no": "' . $logi_no . '",
  "logistics": "SF",
  "method": "wms.delivery.status_update",
  "msg_id": "6880611AC0A8028A84E41DF8A89428AE",
  "node_id": "1591150831",
  "node_version": "",
  "operate_time": "",
  "other_list_0": "[]",
  "out_delivery_bn": "ON' . $delivery_bn . '",
  "packageMaterialList": "{}",
  "sign": "C5545855F4CC31604447D2D9AC9851E1",
  "status": "DELIVERY",
  "task": "1753272729938",
  "warehouse": "OTHER",
  "weight": ""
}';
$sdf = json_decode($s, true);
$a = kernel::single('erpapi_router_response')->set_node_id($sdf['node_id'])->set_api_name($sdf['method'])->dispatch($sdf);
var_dump($a);