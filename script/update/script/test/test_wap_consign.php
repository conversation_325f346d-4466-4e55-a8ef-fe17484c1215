<?php

error_reporting(0);

$domain = $argv[1];
if (empty(trim($domain))) {
    die('参数不能为空！');
}

set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);


$delivery_id = '49065';
$filter    = array('delivery_id'=>$delivery_id);

$wapDeliveryObj    = app::get('wap')->model('delivery');
$deliveryInfo      = $wapDeliveryObj->dump($filter, '*');

// logi_no
$omeDelivery = app::get('ome')->model('delivery')->dump(array('delivery_bn' => $deliveryInfo['outer_delivery_bn']), 'logi_no,status');

$deliveryInfo['logi_no'] = $omeDelivery['logi_no'];
$deliveryInfo['retry_sync'] = true;

//执行发货
$dlyProcessLib  = kernel::single('wap_delivery_process');
$res            = $dlyProcessLib->consign($deliveryInfo);
if($res){

    //task任务更新统计数据
    $wapDeliveryLib    = kernel::single('wap_delivery');
    $wapDeliveryLib->taskmgr_statistic('consign');

    echo json_encode(array('res'=>'succ', 'msg'=>'发货成功'));
    exit;
}else {
    echo json_encode(array('res'=>'error', 'msg'=>'发货失败'));
    exit;
}