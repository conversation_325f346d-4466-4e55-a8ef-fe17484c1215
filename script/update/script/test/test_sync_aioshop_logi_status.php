<?php

error_reporting(0);

$domain = $argv[1];
if (empty(trim($domain))) {
    die('参数不能为空！');
}

set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$list = array(
    array('4937897000312536','JDVB44594367501'),
    array('4934847000196761','JDVB44402743876'),
    array('4931497000313590','SF1556606948147'),
    array('4933025000131956','JDVB44342723759'),
    array('4934811000275191','JDVB44063503799'),
    array('4940790000059403','JDV021814765944'),
    array('4941514000400520','JDVB44728839964'),
    array('4933535000121364','JDV021824368828'),
    array('4941934000122546','JDVB44776906140'),
    array('4940850000310918','JDVB44727196414'),
    array('4940783000331822','JDV021931866294'),
    array('4933484000313996','JDVC31249921970'),
    array('4939793000177382','SF1553070331380'),
    array('4942522000128954','JDVB44728839964'),
    array('4941810000255845','JDVB44776517610'),
    array('4941546000286761','JDVB44728839964'),
    array('4941793000318650','JDVB44775503391'),
    array('4940747000232628','JDVB44698617694'),
    array('4940695000140185','JDVB44723431239'),
    array('4941707000230196','JDVB44682158676'),
    array('4935584000188126','JDVB42962004815'),
    array('4938467000283136','JDVB44581081154'),
    array('4930928000278651','312814122226680'),
    array('4939415000652426','JDVB44627203958'),
    array('4933440000290827','JDVB44336186033'),
    array('4941394000273881','JDVB44731985817'),
    array('4941708000226761','JDVB44682158676'),
    array('4940280000189982','JDVB44678595763'),
    array('4933700000227194','JDVB44354179075'),
    array('4934642000243725','JDVB44400334542'),
    array('4936893000452373','JDVB44532063168'),
    array('4934578000262694','JDVB44397296076'),
    array('4936570000418319','JDVB44493055842'),
    array('4930322000123189','DPK202565172219'),
    array('4941524000069780','JDVB44735610417'),
    array('4932715000105037','312816068760565'),
    array('4930480000393945','SF1542539556939'),
    array('4930655000366326','SF0284599280080'),
    array('4940735000509453','JDVB44697886238'),
    array('4932625000186326','312816068764632'),
    array('4930573000717828','312814122226016'),
    array('4935573000115490','JDVC31229182444'),
    array('4931596000749208','JDVB44253313754'),
    array('4931740000285088','JDVC31229182444'),
    array('4936490000115252','JDVB44487316866'),
    array('4941687000196761','JDVB44682158676'),
    array('4939826000287309','JDVC31284586577'),
    array('4930463000136757','312814122223893'),
    array('4932622000174915','JDVB44302447688'),
    array('4941769000322289','JDV021814765944'),
    array('4933644000553899','SF1553051099719'),
    array('4942655000230581','78921271728525'),
    array('4941019000117851','JDVB44722382656'),
    array('4932470000237160','JDVC31221188985'),
    array('4937719000288126','JDVB44525689391'),
    array('4939491000304990','SF1553070331186'),
    array('4932843000278934','312816068761857'),
    array('4939443000112463','JDVB44631669782'),
    array('4943506000175921','JDVB44699285406'),
    array('4937241000090784','JDVB44531259908'),
    array('4940486000351507','JDJ002272189944'),
    array('4934706000164911','JDVB44040642953'),
    array('4930252000196259','JDVB44224853296'),
    array('4932702000227997','SF3199217156366'),
    array('4934816000243775','JDVB44063503799'),
    array('4935580000274694','JDVB42962004815'),
    array('4930366001246326','JDVB44204528508'),
    array('4934629000138148','JDVC31188203725'),
    array('4940348000179343','JDVB44675913325'),
    array('4932794000205845','JDV021830889130'),
    array('4941511000466424','JDVB44728839964'),
    array('4938517000249619','JDVB44594698590'),
    array('4941592000257538','JDVB44743428625'),
    array('4932621000319036','JDVB44299346987'),
    array('4932348000249536','JDVB44341346290'),
    array('4941723000310615','SF1553070331608'),
    array('4935455000293465','JDVB44438643372'),
    array('4938692000741053','JDVB44525689391'),
    array('4930591000175037','SF1553011570828'),
    array('4930667000148380','JDVB44207384279'),
    array('4938679000214694','JDVB44402787265'),
    array('4933554000210771','SF1553070336937'),
    array('4932620000102444','JDVB44302371764'),
    array('4930455000163361','JDVB44228180255'),
    array('4939415000742426','JDVC31335548368'),
    array('4935766000229122','JDVC31188211384'),
    array('4935263000325542','DPK202567604446'),
    array('4940517000277149','JDV021814765944'),
    array('4940735000353606','JDVB44699001331'),
    array('4934675000108476','JDVB44402341542'),
    array('4938672000326382','JDVB44587180452'),
    array('4937682000099924','SF1553070331186'),
    array('4935325000273738','DPK212576525731'),
    array('4930687000094588','JDVC31186101147'),
    array('4933633000408956','JDVB44352584905'),
    array('4943432000252497','JDVB44699230534'),
    array('4934932000275859','JDVB44637455685'),
    array('4935573000339101','JDV021859643184'),
    array('4940777000288283','JDVB44698976874'),
    array('4943839000125921','JDVB44679850131'),
    array('4931692000138454','SF1528034972609'),
    array('4940772000242201','JDVB44698722439'),
    array('4930429000458442','JDVB44205841859'),
    array('4930483000403955','SF3199767179529'),
    array('4934732000261264','JDVC31229020050'),
    array('4934303000256372','DPK202566969250'),
    array('4943435000365011','JDVB44699230534'),
    array('4943618000309139','YT2529884596252'),
    array('4938672000724321','JDVB44587180452'),
    array('4941650000160927','JDVB44699283443'),
    array('4934816000404911','JDVB44063503799'),
    array('4939565000225806','JDVE15009152547'),
    array('4933623000150013','DPK202566969250'),
    array('4935825000241073','JDV021850356401'),
    array('4941850000212904','JDVB44776284636'),
    array('4939629000136912','JDV021838003074'),
    array('4930147000084820','JDVB44194018388'),
    array('4940583000219953','JDVB44694411018'),
    array('4935534000226219','JDVC31168604443'),
    array('4941792000426119','JDV021814765944'),
    array('4930515000249908','SF3199767179529'),
    array('4943520000037050','SF3196182453948'),
    array('4932693000104564','SF3199217156366'),
    array('4938959000243058','JDVB44625952092'),
    array('4939524000209208','JDVB44635758240'),
    array('4941519000219780','JDVB44735760220'),
    array('4935595000253058','JDVC31229020050'),
    array('4934910000412531','DPK202567603726'),
    array('4932685000112694','DPK202566507788'),
    array('4930004000127261','JDVB44228362379'),
    array('4941460000238755','JDVC31319416624'),
    array('4930148000204820','JDVB44193949609'),
    array('4930627000203067','JDVB44202082784'),
    array('4931943000246839','DPK202565956001'),
    array('4941583000890854','JDVB44728839964'),
    array('4930691000104588','JDVC31186101147'),
    array('4935641000212175','JDVB44448048975'),
    array('4937580000155191','JDVB44525689391'),
    array('4939442000174831','JDVB44627886139'),
    array('4935646000183775','JDVB42804913829'),
    array('4939614000093775','JDVB44595317451'),
    array('4940459000215655','JDVB44679693188'),
    array('4930645000289441','JDVB44202275907'),
    array('4943446000238484','JDVB44699285406'),
    array('4937751000443019','JDVB44572944090'),
    array('4930869000224796','DPK202565336227'),
    array('4930457000143319','312814122226989'),
    array('4933801000152481','312816723956724'),
    array('4935558000171522','JDVB44442767228'),
    array('4933652000228773','SF3199217156366'),
    array('4933742000088753','SF1553070331838'),
    array('4936635000317453','JDVB44497279721'),
    array('4934551000205845','JDVB44420937556'),
    array('4932479000270395','DPK212396622610'),
    array('4931893000023005','JDVB44293919942'),
    array('4937365000235446','SF3199463164593'),
    array('4940747000322628','JDVB44698446587'),
    array('4933458000140843','JDVB44339077686'),
    array('4930649000250395','772044178933050'),
    array('4935645000176674','JDVB42804913829'),
    array('4932944000213046','JDVB44341871845'),
    array('4932467000177160','SF1551237529295'),
    array('4934701000164911','JDVB44040642953'),
    array('4930539000152834','JDVB44192888183'),
    array('4934809000416850','JDVB44040412599'),
    array('4942617000175298','JDVB44794840152'),
    array('4941913000271726','JDVB44776677394'),
    array('4930690000239644','DPK202565172923'),
    array('4934733000105088','JDVC31229020050'),
    array('4933626000343899','SF1553051099719'),
    array('4941780000382375','JDV021814765944'),
    array('4936298000177159','JDVB44496669323'),
    array('4930692000264549','JDVB44207357989'),
);
$err_msg = '';
$dMdl = app::get("ome")->model("delivery");
$doMdl = app::get("ome")->model("delivery_order");
$oMdl = app::get("ome")->model("orders");
foreach($list as $k => $v){
    $oInfo = $oMdl->dump(array("order_bn"=>$v[0]), "order_id");
    $doList = $doMdl->getList("*", array("order_id" => $oInfo['order_id']));
    $didList = array_column($doList, "delivery_id");
    $filter = array(
        'delivery_id' => $didList,
        'logi_no' => $v[1]
    );
    $dInfo = $dMdl->dump($filter, "delivery_id");
    if($dInfo && $dInfo['delivery_id']){
        $res = kernel::single("ome_ecapi_delivery")->sync_logi_status($dInfo['delivery_id'], '3', $err_msg);
        var_dump($res);
        var_dump($err_msg);
    }

}

