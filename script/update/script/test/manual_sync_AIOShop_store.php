<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

# "FVGF015H_ZH2721170956",
ini_set('memory_limit','4G');
$fileName = DATA_DIR.'/skucodes_20250721.csv';

define('BASE_URL','https://oms.fvo2o.com');

$materialObj = kernel::single('material_kucun100');
$operLogObj = app::get('ome')->model('operation_log');
$shop_id = '7beafba193c76c7488e6d84b0f933da9';//生产AIO武汉商城
$shop = app::get('ome')->model('shop')->dump($shop_id);

if ($shop['s_type'] != '1'){
    echo '非线上店铺不能同步';exit;
}

if (!$shop['node_id']) {
    echo '未绑定店铺不能同步';exit;
}

error_log('start:'.date('Y-m-d H:i:s').PHP_EOL,3,DATA_DIR.'/manual_sync_AIOShop_store.csv');
// 打开 CSV 文件
if (($handle = fopen($fileName, "r")) !== FALSE) {

    // 读取第一行作为标题（可选）
    $header = fgetcsv($handle, 1000, ",");

    // 循环读取每一行数据
    while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {

        $spuCode = explode('_', $row[0]);

        $params = array(
            'store_bn' => $spuCode[0],
            'sku_bn'   => $row[0]
        );
        $bn = $row[0];

        error_log($row[0].PHP_EOL,3,DATA_DIR.'/manual_sync_kuc100_store.csv');

        $list = app::get('material')->model('sales_material')->getList('sales_material_bn', ['sales_material_bn' => $bn]);
        if (!$list) {
            error_log('销售物料不存在'.PHP_EOL,3,DATA_DIR.'/manual_sync_kuc100_store.csv');
            echo $bn.'销售物料不存在';
            continue;
        }

        foreach ($list as $v) {
            $params = [
                'sales_material_bn' => trim($v['sales_material_bn']),
                'offset'            => '0',
                'limit'             => '10',
                'shop_id'           => $shop_id,
            ];

            kernel::single('inventorydepth_queue')->insert_stock_update_queue($v['sales_material_bn'],$params);
        }
    }

    // 关闭文件句柄
    fclose($handle);

    error_log('finish:'.date('Y-m-d H:i:s').PHP_EOL,3,DATA_DIR.'/manual_sync_AIOShop_store.csv');
}