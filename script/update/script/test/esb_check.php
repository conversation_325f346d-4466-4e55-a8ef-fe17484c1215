<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bn = '6919779142807814078';
$not_push_list = [
    'no_refund' => []
];

$orderInfo = app::get('ome')->model('orders')->dump(array('order_bn' => $order_bn), '*');
$sapObj = kernel::single('ome_sap_sap');
# 查询参数
$data = [
    'shop_id' => $orderInfo['shop_id'],
    'shop_type' => $orderInfo['shop_type'],
    'start_time' => strtotime('2025-06-01 00:00:00'),
    'end_time' => time(),
];
$result_apply = checkNotPushEsbApply($data);
if (!empty($result_apply['not_list'])) {
    $not_push_list['no_refund'] = array_merge($not_push_list['no_refund'], $result_apply['not_list']);
}
var_dump($not_push_list);
# 取消订单，没有退款申请单
if (!empty($not_push_list['no_refund'])) {
    foreach ($not_push_list['no_refund'] as $order) {
        # 生成退款单
        $apply_id = generalRefundApply($order, $error_msg);
        var_dump($apply_id,$error_msg);
        # 重新推送ESB
        if (!empty($apply_id)) {
            $sapObj->push_refund($apply_id, $order['shop_type']);
        }
    }
}

echo 'end..' . PHP_EOL;

function checkNotPushEsbApply($params)
{
    $orderMdl = app::get('ome')->model('orders');
    $refundsMdl = app::get('ome')->model('refund_apply');
    $result = [
        'refund' => ['not_list' => []],
    ];

    $filter = [
        'shop_id' => $params['shop_id'],
        'last_modified|lthan' => time() - 7200,
        'pay_status' => ['4', '5'], // 部分退款、全额退款
    ];
    # 查询订单
    $orderList = $orderMdl->getList('order_id,order_bn,shop_type', $filter);
    if (empty($orderList)) {
        return false;
    }

    # 订单ID
    $order_ids = array_column($orderList, 'order_id');
    # 读取订单对应的仅退款单 - 已退款成功的
    $apply_filter = [
        'shop_id' => $params['shop_id'],
        'order_id' => $order_ids,
        'status|noequal' => '3',
    ];
    $applyList = $refundsMdl->getList('apply_id,refund_apply_bn,shop_type,reship_id,order_id', $apply_filter);
    if (!empty($applyList)) {
        $applyData = array_column($applyList, null, 'order_id');
    }

    # 匹配订单
    if (empty($applyData)) {
        $result['refund']['not_list'] = $orderList;
    } else {
        foreach ($orderList as $item) {
            if (!empty($applyData[$item['order_id']])) {
                continue;
            }
            $result['refund']['not_list'][] = $item;
        }
    }
    return $result['refund'];
}

function generalRefundApply($params, &$error_msg)
{
    if (empty($params['order_id'])) {
        $error_msg = '订单ID参数错误';
        return false;
    }

    //申请退款单号
    $refundapp = app::get('ome')->model('refund_apply');
    $orderMdl = app::get('ome')->model('orders');
    $refundsMdl = app::get('ome')->model('refunds');
    $sapOrderItemsMdl = app::get('sales')->model('sap_order_items');

    // 订单
    $oids = $bns = $product_list = [];
    $orderInfo = $orderMdl->dump($params['order_id'], "*", array("order_objects" => array("*", array("order_items" => array("*")))));
    # 历史订单不处理
    if (isset($orderInfo['is_history']) && $orderInfo['is_history'] == 'true') {
        $error_msg = '该订单是导入的历史订单，不处理';
        return false;
    }
    if (!empty($orderInfo['order_objects'])) {
        $class_name = sprintf('ome_sap_data_platform_%s', $orderInfo['shop_type']);
        foreach ($orderInfo['order_objects'] as $object) {
            $oids[] = $object['oid'];
            $bns[] = $object['bn'];
            # 子单号
            $oid = kernel::single($class_name)->getOid($orderInfo['order_id'], $object['oid']);
            # 检查是否存在esb退款单数据
            $esb_filter = [
                'orderType' => 'REFUNDED',
                'oriFlowNo' => $oid
            ];
            $sapOrderItems = $sapOrderItemsMdl->getList('item_id', $esb_filter);
            if (!empty($sapOrderItems)) {
                $error_msg = '子单号[' . $oid . ']已经生成了推送esb的退款单数据';
                return false;
            }

            foreach ($object['order_items'] as $item) {
                $product_list[] = array(
                    'product_id' => $item['product_id'],
                    'bn' => $item['bn'],
                    'name' => !empty($item['title']) ? $item['title'] : $item['name'],
                    'num' => $item['quantity'],
                    'price' => $item['price'],
                    'order_item_id' => $item['item_id'],
                    'oid' => $object['oid'],
                    'modified' => time(),
                );
            }
        }
    }

    $is_update_order = true;//是否更新订单付款状态
    $error_msg = '';
    $db = kernel::database();
    $refund_apply_bn = $refundapp->gen_id();

    try {
        $db->beginTransaction();

        $insertData = array(
            'order_id' => $params['order_id'],
            'refund_apply_bn' => $refund_apply_bn,
            'pay_type' => 'online',
            'money' => $orderInfo['total_amount'],
            'refunded' => $orderInfo['total_amount'],
            'refund_money' => $orderInfo['total_amount'],
            'memo' => '强制生成的退款单',
            'create_time' => time(),
            'status' => '4',
            'shop_id' => $orderInfo['shop_id'],
            'source' => 'local',
            'shop_type' => $orderInfo['shop_type'],
            'refund_refer' => '0', //退款来源
            'org_id' => $orderInfo['org_id'],
            'bn' => implode(',', $bns),
            'oid' => implode(',', $oids), //oid子单
            'tag_type' => '0', //退款类型
        );
        if (!empty($product_list)) {
            $insertData['product_data'] = serialize($product_list);
        }
        //根据商品编码获取门店信息
        $bn = current($product_list)['bn'];
        $store = kernel::single("o2o_store_material")->getBmStore($bn);
        if ($store) {
            $belong_type = $store['performance_type'] == 'store' ? 'store' : 'town';
            $store_id = $store['store_id'];
        } else {
            $belong_type = 'customer';
            $store_id = 0;
        }
        $insertData['belong_store_id'] = $store_id;
        $insertData['belong_type'] = $belong_type;
        $insertData['check_payed'] = false;
        # 创建售后申请单
        $rs = kernel::single('ome_refund_apply')->createRefundApply($insertData, $is_update_order, $error_msg);
        if (!$rs) {
            throw new Exception($error_msg);
        }

        # 生成退款单
        $data = array(
            'refund_bn' => $refund_apply_bn,
            'shop_id' => $orderInfo['shop_id'],
            'order_id' => $orderInfo['order_id'],
            'currency' => 'CNY',
            'money' => $orderInfo['total_amount'],
            'cur_money' => $orderInfo['total_amount'],
            'pay_type' => 'online',
            'download_time' => time(),
            'status' => 'succ',
            'memo' => '强制退款',
            'modifiey' => time(),
            't_ready' => time(),
            't_sent' => time(),

        );
        $rs = $refundsMdl->insert($data);
        if (!$rs) {
            throw new Exception('退款单生成失败：' . $refundsMdl->db->errorinfo());
        }

        $db->commit();

        return $insertData['apply_id'];
    } catch (Exception $e) {
        $db->rollBack();

        $error_msg = $e->getMessage();
        return false;
    }
}