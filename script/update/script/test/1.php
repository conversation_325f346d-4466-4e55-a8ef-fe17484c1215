<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_id = 69331;//; 5165
$orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $order_id), 'shop_type');
$extend = [
    'filter_delete_item' => false
];
$sdf = kernel::single('ome_sap_data_platform_' . $orderInfo['shop_type'])->get_order_sdf($order_id, $extend);
var_dump($sdf['payments']);
$result = kernel::single('erpapi_router_request')->set('sap', true)->order_push($sdf);
print_r($result);


//$delivery_id = 952;
//$sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_delivery_sdf($delivery_id);
//var_dump($sdf);
//$result = kernel::single('erpapi_router_request')->set('sap', true)->delivery_push($sdf);
//print_r($result);


//$apply_id = 11798;//; 516
//$sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_refund_sdf($apply_id);
//print_r($sdf);
//$result = kernel::single('erpapi_router_request')->set('sap', true)->refund_push($sdf);
//print_r($result);


//$reship_id = 9;
//$sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_reship_sdf($reship_id);
////print_r($sdf);
//$result = kernel::single('erpapi_router_request')->set('sap', true)->reship_push($sdf);
//print_r($result);
