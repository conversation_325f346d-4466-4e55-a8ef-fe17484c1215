<?php

error_reporting(0);

$domain = $argv[1];
if (empty(trim($domain))) {
    die('参数不能为空！');
}

set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$log_id = 'fef2adee42945d8115c426dca1b5df5c';

$logMdl = app::get('ome')->model('api_log');
$logInfo = $logMdl->dump(array('log_id' => $log_id), 'params');
$sdf = json_decode($logInfo['params'], true);
if (empty($sdf)) {
    die('参数有问题');
}

$sdf['refund_type'] = 'refund';
echo var_export($sdf, true);
$a = kernel::single('erpapi_router_response')->set_node_id($sdf['node_id'])->set_api_name($sdf['method'])->dispatch($sdf);
var_dump($a);
