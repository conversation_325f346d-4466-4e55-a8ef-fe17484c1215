<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$s = '{"coupon_field": [
            {
                "promotion_talent_amount": 0,
                "government_reduce_plantfrom_cost": 0,
                "sku_id": 3487389361394434,
                "promotion_platform_amount": 0,
                "pay_amount": 362,
                "promotion_shop_amount": 0,
                "promotion_redpack_platform_amount": 0,
                "oid": 6919562647103765966,
                "origin_amount": 362,
                "order_amount": 362,
                "promotion_pay_amount": 0.11,
                "item_num": 1,
                "promotion_redpack_talent_amount": 0,
                "government_reduce_shop_cost": 0,
                "platform_cost_amount": 0,
                "shop_cost_amount": 0,
                "author_cost_amount": 0,
                "promotion_amount": 0,
                "promotion_redpack_amount": 0
            }
        ]}';
$couponData = json_decode($s, true);
var_dump($couponData);

foreach ($couponData['coupon_field'] as $key => $value) {
    //type
    foreach ($value as $k => $v) {
        if ($v <= 0 || in_array($k, array('sku_id', 'item_num', 'oid'))) {
            continue;
        }

        $coupon[] = array(
            'num' => $value['item_num'],
            'oid' => $value['oid'],
            'material_bn' => '',
            'material_name' => '',
            'type' => $k,
            'type_name' => (string)$this->couponTypeName[$k],
            'amount' => $v,
            'create_time' => sprintf('%.0f', time()),
            'pay_time' => $this->_ordersdf['payment_detail']['pay_time'],
            'shop_type' => $this->__channelObj->channel['shop_type'],
        );
    }

    //实付金额
    $realPay = $value['pay_amount'] - $value['promotion_pay_amount'];
    $realPayData = [
        'num' => $value['item_num'],
        'oid' => $value['oid'],
        'material_bn' => '',
        'material_name' => '',
        'type' => 'user_payamount',
        'type_name' => '实付金额',
        'amount' => $realPay,
        'create_time' => sprintf('%.0f', time()),
        'pay_time' => $this->_ordersdf['payment_detail']['pay_time'],
        'shop_type' => $this->__channelObj->channel['shop_type'],
    ];

    array_push($coupon, $realPayData);
}


var_dump($coupon);
