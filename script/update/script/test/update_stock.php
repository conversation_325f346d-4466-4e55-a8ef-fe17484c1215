<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$basic_bns = [
    'FVSH0120_SHDY-AG81455098705437',
    'FVSH0120_SHDY-AG01455098696085',
    'FVSH0120_SHDY-AG51455098682742',
    'FVSH0120_SHDY-AG01455098677423',
    'FVSH0120_SHDY-AG11455098668204',
    'FVSH0120_SHDY-AG11455098650407',
    'FVSH0120_SHDY-AG71455098625533',
    'FVSH0120_SHDY-AG41455098613470',
    'FVSH0120_SHDY-AG11455098600913',
    'FVSH0120_SHDY-AG81455098596435',
    'FVSH034E_SHDY-TUMI41447204430772',
    'FVSH034E_SHDY-TUMI71447204273927'
];
$store = 1000;

$basicMdl = app::get('material')->model('basic_material');
$branchProductMdl = app::get('ome')->model('branch_product');
$basicStockMdl = app::get('material')->model('basic_material_stock');
$storeMdl = app::get('o2o')->model('store');

$basicList = $basicMdl->getList('bm_id,material_bn,store_id', array('material_bn' => $basic_bns));
if (empty($basicList)) {
    exit('没有基础物料' . PHP_EOL);
}

$db = kernel::database();

try {
    $db->beginTransaction();

    foreach ($basicList as $item) {
        $storeInfo = $storeMdl->dump(array('store_id' => $item['store_id']), 'store_bn,branch_id,shop_id');
        if (empty($storeInfo)) {
            throw new Exception('store_id：' . $item['store_id'] . '，对应的仓库信息为空');
        }

        $filter = [
            'store_id' => $item['store_id'],
            'product_id' => $item['bm_id']
        ];
        $branchProduct = $branchProductMdl->dump($filter, 'id,store');
        if (empty($branchProduct)) {
            $data = [
                'branch_id' => $storeInfo['branch_id'],
                'product_id' => $item['bm_id'],
                'store' => $store,
                'last_modified' => time(),
                'store_bn' => $storeInfo['store_bn'],
                'store_id' => $item['store_id'],
            ];
        } else {
            $data = [
                'id' => $branchProduct['id'],
                'store' => $branchProduct['store'] + $store,
                'last_modified' => time(),
            ];
        }
        $success = $branchProductMdl->save($data);
        if (!$success) {
            throw new Exception('物料编码:' . $item['material_bn'] . '，保存失败：' . $branchProductMdl->db->errorinfo());
        }

        $filter = [
            'bm_id' => $item['bm_id']
        ];
        $basicStock = $basicStockMdl->dump($filter, 'bm_id,store');
        if (empty($basicStock)) {
            $data = [
                'bm_id' => $item['bm_id'],
                'store' => $store,
                'last_modified' => time(),
            ];
        } else {
            $data = [
                'bm_id' => $basicStock['bm_id'],
                'store' => $basicStock['store'] + $store,
                'last_modified' => time(),
            ];
        }
        $basicStockMdl->save($data);
    }

    $db->commit();
} catch (Exception $e) {
    $db->rollback();
    echo $e->getMessage() . PHP_EOL;
}


echo 'end...' . PHP_EOL;
