<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$reship_bn = '147059721757986805';
$logi_no = 'SF3121070797' . mt_rand(100, 999);

$s = '{
  "app_id": "ecos.ome",
  "date": "2025-07-24 10:06:02",
  "extendProps": "{}",
  "item": "[{\"product_bn\": \"100025-BKW8\", \"defective_num\": 0, \"normal_num\": \"1\"}]",
  "logi_no": "' . $logi_no . '",
  "logistics": "SF",
  "method": "wms.reship.status_update",
  "msg_id": "6881950AC0A80289D14151C50E343495",
  "node_id": "1591150831",
  "node_version": "",
  "operate_time": "' . date('Y-m-d H:i:s') . '",
  "remark": "",
  "reship_bn": "' . $reship_bn . '",
  "sign": "39FB0B7F815F227EF168A6B038DB4E77",
  "status": "FINISH",
  "task": "3d5d0044222f47c490",
  "warehouse": "OTHER"
}';
$sdf = json_decode($s, true);
$a = kernel::single('erpapi_router_response')->set_node_id($sdf['node_id'])->set_api_name($sdf['method'])->dispatch($sdf);
var_dump($a);
