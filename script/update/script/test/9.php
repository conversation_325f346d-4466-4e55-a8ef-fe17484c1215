<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

# "FVGF015H_ZH2721170956",
$s = '[{"store_bn":"FVGF015H","spu_id":["FVGF015H_ZH2721189218"]}]';
$params = json_decode($s, true);
$materialObj = kernel::single('material_kucun100');

foreach ($params as $param) {
    $result = kernel::single('erpapi_router_request')->set('kucun100', true)->goods_getSpuList($param);
//    $result = kernel::single('erpapi_router_request')->set('kucun100', true)->goods_getInventoryList($param);
    if ($result['rsp'] == 'fail') {
        exit($result['msg']);
    }

    # 更新基础物料和销售物料档案
    $successList = $materialObj->updateMaterial($param, $result, $error_msg);
}

