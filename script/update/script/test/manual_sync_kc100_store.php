<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

# "FVGF015H_ZH2721170956",
ini_set('memory_limit','4G');
$fileName = DATA_DIR.'/spucodes_20250721.csv';

define('BASE_URL','https://oms.fvo2o.com');

$materialObj = kernel::single('material_kucun100');
$operLogObj = app::get('ome')->model('operation_log');

error_log('start:'.date('Y-m-d H:i:s').PHP_EOL,3,DATA_DIR.'/manual_sync_kuc100_store.csv');
// 打开 CSV 文件
if (($handle = fopen($fileName, "r")) !== FALSE) {

    // 读取第一行作为标题（可选）
    $header = fgetcsv($handle, 1000, ",");

    // 循环读取每一行数据
    while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {

        $spuCode = explode('_', $row[0]);

        $params = array(
            'store_bn' => $spuCode[0],
            'spu_id'   => $row[0]
        );

        error_log($row[0].PHP_EOL,3,DATA_DIR.'/manual_sync_kuc100_store.csv');
        # 查询待推送的数据
        $sql = "select a.id,a.store_bn,a.unique_id,a.source,b.material_bn from sdb_material_material_unique a"
            . " left join sdb_material_material_unique_items b ON a.unique_id = b.unique_id"
            . " WHERE a.unique_id='".$params['spu_id']."' ";
        $dataList = kernel::database()->select($sql);

        if (empty($dataList)) {
            echo $row[0].'没有可处理的数据!';
            continue;
        }

        $uniqueData = [];
        foreach ($dataList as $item) {
            $key = sprintf('%s_%s_%s', $item['store_bn'], $item['unique_id'], $item['source']);
            if (empty($uniqueData[$key])) {
                $tmpData = [
                    'id' => $item['id'],
                    'store_bn' => $item['store_bn'],
                    'spu_id' => $item['unique_id'],
                    'source' => $item['source'],
                ];
                $tmpData['sku_id'][] = $item['material_bn'];
                $uniqueData[$key] = $tmpData;
            } else {
                if (!in_array($item['material_bn'], $uniqueData[$key]['sku_id'])) {
                    $uniqueData[$key]['sku_id'][] = $item['material_bn'];
                }
            }
        }

        foreach ($uniqueData as $item) {
            $params = [];
            $params[] = [
                'id' => $item['id'],
                'store_bn' => $item['store_bn'],
                'spu_id' => $item['spu_id'],
                'sku_id' => $item['sku_id'],
                'source' => $item['source'], // 来源
            ];
            # 记录日志
            $operLogObj->write_log('stock_sync@ome', $item['id'], '开始同步商品库存信息');
            # 队列处理
            $push_params = array(
                'data' => array(
                    'params' => json_encode($params, JSON_UNESCAPED_UNICODE),
                    'log_id' => time(),
                    'task_type' => 'uniquegetstock'
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service')
            );
            kernel::single('taskmgr_interface_connecter')->push($push_params);
        }
    }

    // 关闭文件句柄
    fclose($handle);

    error_log('finish:'.date('Y-m-d H:i:s').PHP_EOL,3,DATA_DIR.'/manual_sync_kuc100_store.csv');
}